"use client";

import {
  Center,
  Title,
  Text,
  PasswordInput,
  Button,
  Box,
  Stack,
  ScrollArea,
  TextInput,
} from "@mantine/core";
import React, { useEffect, useState } from "react";
import { useDisclosure } from "@mantine/hooks";
import Footer from "@/components/common/Footer";
import AccountSelectionModal, {
  AccountSelectionListProps,
} from "../AccountSelection";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import {
  passwordValidationLogin,
  phoneValidation,
} from "@/utils/common-functions";
import { useRouter } from "next/navigation";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { login } from "@/store/slices/authSlice";
import useBreakpoints from "@/hooks/useBreakpoints";
import BlacklistedModal from "@/components/common/BlacklistedModal";
import { Phone } from "@/utils/icons";
import LeftSection from "@/components/common/LeftSection";

const schema = yup.object().shape({
  phone: phoneValidation,
  password: passwordValidationLogin,
});

const DistributorAgentLoginForm: React.FC = () => {
  // const [value, setValue] = useState<string | null>(null);
  // const [opened, { open, close }] = useDisclosure(false);
  const router = useRouter();

  const dispatch: AppDispatch = useDispatch();
  const { isLoading, role, accessToken } = useSelector(
    (state: RootState) => state.auth
  );

  const form = useForm({
    mode: "onChange",
    resolver: yupResolver(schema),
    defaultValues: { phone: "", password: "" },
  });

  const onSubmit = (data: { phone: string; password: string }) => {
    if (!isLoading) {
      dispatch(
        login({
          loginData: { phone: data.phone, password: data.password },
          handleBlacklisted: openBlacklistedModal,
        })
      );
    }
  };

  useEffect(() => {
    if (accessToken) {
      const currentUrl = new URL(window.location.href);
      const searchParams = currentUrl.searchParams;
      const queryString = searchParams.toString();
      window.location.href = queryString ? `/?${queryString}` : "/";
    }
  }, [accessToken, role, router]);

  const [
    blacklistedModal,
    { open: openBlacklistedModal, close: closeBlacklistedModal },
  ] = useDisclosure(false);

  const { mobile } = useBreakpoints();
  return (
    <>
      <BlacklistedModal
        opened={blacklistedModal}
        onClose={closeBlacklistedModal}
        fieldBlocked="phone"
      />
      <Center
        component="form"
        w="100%"
        ta={{ sm: "center" }}
        onSubmit={form.handleSubmit(onSubmit)}
        p={mobile ? "xl" : "0"}
      >
        <ScrollArea
          w="100%"
          maw={{ base: "100%", sm: 592 }}
          h={{ base: "100%", md: "auto" }}
        >
          <Stack gap="xl">
            <Box ta={"center"}>
              <Title order={2}>Selamat Datang Kembali</Title>
              <Text c="text-light">Log in ke akunmu untuk kembali</Text>
            </Box>
            <Stack gap="xl" ta="start">
              <TextInput
                {...form.register("phone")}
                label="Nomor Telepon"
                type="tel"
                leftSectionPointerEvents="none"
                leftSectionWidth={40}
                error={form.formState.errors.phone?.message}
                placeholder="08xxxxxxxxxx"
                value={form.watch("phone") || ""}
                leftSection={<LeftSection icon={<Phone />} />}
              />
              <PasswordInput
                {...form.register("password")}
                label="Password"
                value={form.watch("password") || ""}
                error={form.formState.errors.password?.message}
              />
            </Stack>
            <Box>
              <Button
                type="submit"
                loading={isLoading}
                w={{ base: "100%", sm: "auto" }}
              >
                Log in
              </Button>
            </Box>
            <Footer pt={50} />
          </Stack>
        </ScrollArea>
      </Center>

      {/* <AccountSelectionModal
        opened={opened}
        onClose={close}
        value={value}
        onChange={setValue}
        data={accounts}
      /> */}
    </>
  );
};

export default DistributorAgentLoginForm;
