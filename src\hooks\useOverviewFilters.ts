"use client";

import { parseAsInteger, parseAsStringLiteral, useQueryState } from "nuqs";

export default function useOverviewFilters() {
  const [page, setPage] = useQueryState("page", parseAsInteger.withDefault(1));
  const [stats, setStats] = useQueryState(
    "stats",
    parseAsStringLiteral(["sales", "demo"] as const).withDefault("sales")
  );
  const [time, setTime] = useQueryState(
    "time",
    parseAsStringLiteral(["daily", "weekly", "monthly"] as const).withDefault(
      "daily"
    )
  );
  const [salesChart, setSalesChart] = useQueryState(
    "salesChart",
    parseAsStringLiteral([
      "sales",
      "orders",
      "bestSellingProducts",
      "visitors",
      "productSeen",
      "conversionRate",
    ] as const).withDefault("sales")
  );
  const [customerChart, setCustomerChart] = useQueryState(
    "customerChart",
    parseAsStringLiteral([
      "topArea",
      "repeatCustomersPercentage",
      "bestSellingHours",
      "averageRating",
    ] as const).withDefault("topArea")
  );

  return {
    stats,
    time,
    page,
    salesChart,
    customerChart,
    setStats,
    setTime,
    setPage,
    setSalesChart,
    setCustomerChart,
  };
}
