import InputGroup from '@/components/common/InputGroup'
import { AgentDistributorDetailsForceUpdateData } from '@/utils/types/distributor-agent'
import { Flex } from '@mantine/core'
import Input from '@/components/common/Input'
import { useSelector } from 'react-redux'
import { RootState } from '@/store'
import { capitalize } from '@/utils/common-functions'
import { useEffect } from 'react'

interface ChangesSubmittedProps {
 changes: AgentDistributorDetailsForceUpdateData
 reason: string
 setReason: (reason: string) => void
}

const ChangesSubmitted = ({
 changes,
 reason,
 setReason,
}: ChangesSubmittedProps) => {
 const { role } = useSelector((state: RootState) => state.auth)

 return (
  <Flex direction={'column'} gap={'md'} p={'xs'} mb={'3.5em'} w={'100%'}>
   {changes.name && (
    <InputGroup label={`Nama ${capitalize(role)} Baru`}>
     <Input value={changes.name} readOnly />
    </InputGroup>
   )}
   {changes.npwp && (
    <InputGroup label="NPWP Baru">
     <Input value={changes.npwp} readOnly />
    </InputGroup>
   )}
   {changes.nik && (
    <InputGroup label="NIK Baru">
     <Input value={changes.nik} readOnly />
    </InputGroup>
   )}
   {changes.address && (
    <InputGroup label="Alamat Baru">
     <Input value={changes.address} readOnly icon="info/location" />
    </InputGroup>
   )}
   {changes.warehouseAddress && (
    <InputGroup label="Alamat Warehouse Baru">
     <Input value={changes.warehouseAddress} readOnly icon="info/location" />
    </InputGroup>
   )}
   {changes.email && (
    <InputGroup label="Email Baru">
     <Input value={changes.email} icon="info/email" readOnly />
    </InputGroup>
   )}
   {changes.adminPhoneNumber && (
    <InputGroup label="Telepon Admin Baru">
     <Input value={changes.adminPhoneNumber} icon="info/phone" readOnly />
    </InputGroup>
   )}
   {changes.phoneNumber && (
    <InputGroup label="Telepon Telepon Pribadi Baru">
     <Input value={changes.phoneNumber} icon="info/phone" readOnly />
    </InputGroup>
   )}
   {changes.shops?.shopee && (
    <InputGroup label="Link Shopee Baru">
     <Input value={changes.shops.shopee} icon="social/shopee" readOnly />
    </InputGroup>
   )}
   {changes.shops?.tokopedia && (
    <InputGroup label="Link Tokopedia Baru">
     <Input value={changes.shops.tokopedia} icon="social/tokopedia" readOnly />
    </InputGroup>
   )}
   {changes.shops?.tiktok && (
    <InputGroup label="Link Tiktok Baru">
     <Input value={changes.shops.tiktok} icon="social/tokopedia" readOnly />
    </InputGroup>
   )}
   {changes.SK && (
    <InputGroup label="SK Baru">
     <Input value={changes.SK} readOnly />
    </InputGroup>
   )}
   <InputGroup label="Alasan">
    <Input
     value={reason}
     onChange={(e) => {
      setReason(e.target.value)
     }}
     placeholder="Contoh: Tidak sesuai ketentuan"
    />
   </InputGroup>
  </Flex>
 )
}
export default ChangesSubmitted
