"use client";

import PageLayout from "@/components/common/PageLayout";
import { Anchor, Text, Stack, Title } from "@mantine/core";
import Image from "next/image";
import { useEffect } from "react";

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function ErrorPage({ error, reset }: ErrorProps) {
  useEffect(() => {
    console.error(error);
  }, [error]);
  return (
    <PageLayout>
      <Stack w="100%" mih="66vh" justify="center" align="center" ta="center">
        <Image
          src={"/images/not-found.svg"}
          alt="not-found"
          width={200}
          height={200}
        />
        <Title>Oops, terjadi kes<PERSON>!</Title>
        {error.message && (
          <Text c="error" size="lg">
            Error: {error.message}
          </Text>
        )}
        <Anchor href="/" fw={500}>
          <PERSON><PERSON><PERSON> ke halaman utama
        </Anchor>
      </Stack>
    </PageLayout>
  );
}
