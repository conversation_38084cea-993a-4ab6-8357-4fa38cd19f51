import { Flex, Stack } from "@mantine/core";
import Balance from "./Balance";
import Customer<PERSON>hart from "./CustomerChart";
import useBreakpoints from "@/hooks/useBreakpoints";
import TodayInfo from "./TodayInfo";
import ShopStatistic from "./ShopStatistic";
import BestSellerProducts from "./BestSellerProducts";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import { useEffect } from "react";
import {
  fetchBestSellerProducts,
  fetchCustomerStatsOverview,
  fetchShopBalance,
  fetchShopStats,
  fetchTodayStats,
} from "@/store/slices/onlineShopSlices/dashboardSlice";

const ShopStatistics = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { tablet, mobile } = useBreakpoints();

  useEffect(() => {
    dispatch(fetchBestSellerProducts());
    dispatch(fetchShopStats());
    dispatch(fetchTodayStats());
    dispatch(fetchShopBalance());
    dispatch(fetchCustomerStatsOverview("topSpender"));
  }, []);

  return (
    <Flex w={"100%"} gap={"md"} direction={tablet || mobile ? "column" : "row"}>
      <Stack
        w={tablet || mobile ? "100%" : "66%"}
        gap={tablet || mobile ? "md" : "xl"}
      >
        <TodayInfo />
        <ShopStatistic />
        {!tablet && !mobile && <BestSellerProducts />}
      </Stack>
      <Stack
        w={tablet || mobile ? "100%" : "33%"}
        gap={tablet || mobile ? "md" : "xl"}
      >
        <Balance />
        <CustomerChart />
        {(tablet || mobile) && <BestSellerProducts />}
      </Stack>
    </Flex>
  );
};

export default ShopStatistics;
