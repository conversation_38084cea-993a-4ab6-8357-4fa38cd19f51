"use client";

import PageLayout from "@/components/common/PageLayout";
import ProductDetailForm from "@/components/section/OnlineShop/ProductDetailForm";
import { RootState, AppDispatch } from "@/store";
import { fetchProductDetail } from "@/store/slices/onlineShopSlices/productDetailSlice";
import { withAuth } from "@/utils/withAuth";
import { useParams } from "next/navigation";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Anchor, Center, Flex, Loader, Title } from "@mantine/core";

const UpdateProductDetalPage = () => {
  const dispatch: AppDispatch = useDispatch();
  const { productDetail, loading } = useSelector(
    (state: RootState) => state.productDetail
  );
  const { productId } = useParams<{ productId: string }>();

  useEffect(() => {
    if (productId) {
      dispatch(fetchProductDetail(productId));
    }
  }, [dispatch, productId]);

  return (
    <PageLayout title="Produk" navigationContent={{ title: "Ubah Produk" }}>
      {loading ? (
        <Center mt="30dvh">
          <Loader size="xl" type="dots" />
        </Center>
      ) : !productId || !productDetail ? (
        <Center mt="30dvh">
          <Flex direction="column" gap="xs" align={"center"}>
            <Title>Oops, Produk tidak ditemukan!</Title>
            <Anchor href="/shop/product">Kembali ke halaman produk</Anchor>
          </Flex>
        </Center>
      ) : (
        <ProductDetailForm product={productDetail} productId={productId} />
      )}
    </PageLayout>
  );
};

export default withAuth(UpdateProductDetalPage);
