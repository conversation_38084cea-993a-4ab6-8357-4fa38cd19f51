"use client";

import ButtonGroup from "@/components/common/ButtonGroup";
import ResponsiveModal from "@/components/common/ResponsiveModal";
import { ModalProps, Stack, Title, Text, Button, Box } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";

export interface RegistrationFullModalProps {
  show?: ModalProps["opened"];
}

const RegistrationFullModal: React.FC<RegistrationFullModalProps> = ({
  show,
}) => {
  const [opened, handlers] = useDisclosure(show);

  return (
    <ResponsiveModal
      opened={opened}
      onClose={handlers.close}
      withCloseButton={false}
      closeOnClickOutside={false}
      closeOnEscape={false}
      modalProps={{ size: "lg" }}
    >
      <Box ta="center" mt="xl">
        <Stack>
          <Title order={2}>Kuota Distributor Sudah Penuh</Title>
          <Text c="text">
            Jika Anda mendaftar sekarang, Anda akan masuk ke waitlist
            distributor dan mendapatkan kabar jika terdapat kuota kosong.
          </Text>
        </Stack>
        <ButtonGroup justify="center" w="100%" mt="4rem">
          <Button c="black" variant="subtle" onClick={handlers.close}>
            Kembali
          </Button>
          <Button onClick={handlers.close}>Terima</Button>
        </ButtonGroup>
      </Box>
    </ResponsiveModal>
  );
};

export default RegistrationFullModal;
