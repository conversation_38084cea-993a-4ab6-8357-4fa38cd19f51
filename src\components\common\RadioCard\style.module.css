.root {
  position: relative;
  padding: var(--mantine-spacing-md);
  transition: border-color 150ms ease, background-color 150ms ease;

  &[data-checked] {
    cursor: default;
    border-color: var(--mantine-primary-color-filled);
    background-color: var(--mantine-color-bg-filled);

    & :global(.mantine-RadioIndicator-indicator) {
      cursor: default;
    }
  }

  &:disabled {
    cursor: not-allowed;
    background-color: var(--mantine-color-gray-1);
    border-color: var(--mantine-color-gray-4);

    & :global(.mantine-RadioIndicator-indicator) {
      cursor: not-allowed;
    }
  }

  &:hover:not(:disabled) {
    background-color: var(--mantine-color-bg-filled);
  }
}
