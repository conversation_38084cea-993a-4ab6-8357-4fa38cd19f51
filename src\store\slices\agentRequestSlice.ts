import api from "@/lib/axios";
import { createSlice } from "@reduxjs/toolkit";
import { AppDispatch } from "..";
import { toast } from "sonner";
import {
  GET_AVAILABLE_DISTRIBUTORS,
  UPDATE_RESELECT_DISTRIBUTOR,
} from "@/utils/api-routes";
import { AvailableDistributor } from "@/utils/types/request";

interface RequestState {
  availableDistributors: AvailableDistributor[];
  error: string | undefined;
  loading: boolean;
}

const initialState: RequestState = {
  availableDistributors: [],
  error: undefined,
  loading: false,
};

const reqeustSlice = createSlice({
  name: "requestSlice",
  initialState,
  reducers: {
    setError(state, action) {
      state.error = action.payload;
      toast.error(action.payload);
    },
    setLoading(state, action) {
      state.loading = action.payload;
    },
    setAvailableDistributors(state, action) {
      state.availableDistributors = action.payload;
    },
  },
});

export const fetchAvailableDistributors =
  (province: string, city: string, district: string) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(reqeustSlice.actions.setLoading(true));
      const res = await api.get(
        GET_AVAILABLE_DISTRIBUTORS(province, city, district)
      );
      dispatch(reqeustSlice.actions.setAvailableDistributors(res.data.data));
    } catch (error: unknown) {
      dispatch(
        reqeustSlice.actions.setError(
          error instanceof Error
            ? error.message
            : "Error fetching available distributors"
        )
      );
    } finally {
      dispatch(reqeustSlice.actions.setLoading(false));
    }
  };

export const updateSelectedDistributor =
  (distributorUoc: string, requestId: number, onSuccess?: () => void) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(reqeustSlice.actions.setLoading(true));
      const res = await api.post(UPDATE_RESELECT_DISTRIBUTOR, {
        distributorID: distributorUoc,
        requestID: requestId,
      });
      if (res) {
        onSuccess?.();
      }
    } catch (error: unknown) {
      dispatch(
        reqeustSlice.actions.setError(
          error instanceof Error
            ? error.message
            : "Error updating selected distributor"
        )
      );
    } finally {
      dispatch(reqeustSlice.actions.setLoading(false));
    }
  };

export default reqeustSlice.reducer;
