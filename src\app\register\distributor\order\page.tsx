"use client";

import Footer from "@/components/common/Footer";
import DistributorOrder from "@/components/section/Register/Distributor/Order";
import useBreakpoints from "@/hooks/useBreakpoints";
import { RootState } from "@/store";
import { isEmpty } from "@/utils/common-functions";
import { Center, Stack, Image } from "@mantine/core";
import { useLocalStorage } from "@mantine/hooks";
import { useEffect } from "react";
import { useSelector } from "react-redux";

const RegisterDistributorOrderPage = () => {
  const { mobile } = useBreakpoints();
  const { accessToken } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (!isEmpty(accessToken)) {
      window.location.replace("/");
      return;
    }
  }, [accessToken]);

  return (
    <Center p={{ base: 12, sm: 16, md: 24 }}>
      <Stack w="100%" gap="xl">
        <Image
          radius="md"
          src={`/images/register/hero${mobile ? "-mobile" : ""}.png`}
          alt="hero"
        />
        <DistributorOrder />
        <Footer pt={50} />
      </Stack>
    </Center>
  );
};

export default RegisterDistributorOrderPage;
