"use client";

import PageLayout from "@/components/common/PageLayout";
import OverviewDetailSection from "@/components/section/OnlineShop/Overview/Detail";
import OverviewDetailHeader from "@/components/section/OnlineShop/Overview/Detail/Header";
import { DetailOverviewProvider } from "@/context/DetailOverviewContext";
import { RootState } from "@/store";
import { Segment } from "@/utils/types";
import { withAuth } from "@/utils/withAuth";
import { useMounted } from "@mantine/hooks";
import { notFound } from "next/navigation";
import { use } from "react";
import { useSelector } from "react-redux";

const StoreDetailOverviewPage = ({ params }: Segment<{ uoc: string }>) => {
  const mounted = useMounted();
  const { uoc } = use(params);
  const { data, error, loading } = useSelector(
    (state: RootState) => state.adminAgentDetails
  );

  if (error && mounted) notFound();

  return (
    <PageLayout title="Overview" navigationContent={OverviewDetailHeader(uoc)}>
      <DetailOverviewProvider slug={loading ? "" : data.slug}>
        <OverviewDetailSection />
      </DetailOverviewProvider>
    </PageLayout>
  );
};

export default withAuth(StoreDetailOverviewPage, ["admin"]);
