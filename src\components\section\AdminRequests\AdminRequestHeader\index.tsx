import { REQUEST_FILTERS, SORT_FILTERS } from "@/utils/constants";
import { Flex, Text, Select } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

interface AdminRequestHeaderProps {
  count?: number;
}

export const AdminRequestHeader = ({ count = 0 }: AdminRequestHeaderProps) => {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const params = useSearchParams();
  const defaultFilter = params.get("filter") || "all";
  const defaultSortBy = params.get("sortBy") || "desc";
  const [filter, setFilter] = useState<string | null>(defaultFilter);
  const [sortBy, setSortBy] = useState<string | null>(defaultSortBy);

  const router = useRouter();

  useEffect(() => {
    if (!filter || !sortBy) return;
    router.push(`/admin/request?filter=${filter}&sortBy=${sortBy}`);
  }, [filter, sortBy]);

  return (
    <Flex
      direction={isMobile ? "column-reverse" : "row-reverse"}
      align={isMobile ? "flex-start" : "center"}
      justify={"space-between"}
      gap={"xs"}
    >
      <Flex align={"center"} gap={"xs"}>
        <Select
          w="9em"
          placeholder={"Show All"}
          data={REQUEST_FILTERS}
          defaultValue="all"
          value={filter}
          allowDeselect={false}
          onChange={(value) => {
            setFilter(value);
          }}
        />
        <Select
          w="9em"
          placeholder={"Sort By"}
          data={SORT_FILTERS}
          defaultValue="desc"
          value={sortBy}
          allowDeselect={false}
          onChange={(value) => {
            setSortBy(value);
          }}
        />
      </Flex>
      <Text fw={500}>
        {count > 0
          ? `Anda memiliki ${count} request baru!`
          : "Belum ada request baru"}
      </Text>
    </Flex>
  );
};
