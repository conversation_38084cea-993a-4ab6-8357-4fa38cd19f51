import { COLORS } from "@/styles/color";
import { SPACES } from "@/styles/spacing";
import styled from "styled-components";

export const InputFileWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${SPACES.xs};
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: ease-in-out 0.2s;

  border: 1px dashed ${COLORS.secondary};
  border-radius: ${SPACES.xs};
  background-color: ${COLORS.background2};

  padding: ${SPACES.xl2} ${SPACES.xl};

  & > input {
    display: none;
  }

  & > img {
    cursor: pointer;
  }

  &:hover {
    background-color: ${COLORS.background3};
  }

  .underline {
    color: ${COLORS.accent};
    text-decoration: underline;
    cursor: pointer;
  }

  @media screen and (max-width: 768px) {
    padding: ${SPACES.lg} ${SPACES.xs};

    & > p {
      font-size: 0.75em;
      text-align: center;
    }
    & > img {
      width: 4em;
      height: auto;
    }
  }
`;
