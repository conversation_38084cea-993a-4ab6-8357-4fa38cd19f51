import InputGroup from "@/components/common/InputGroup";
import { FormEditFormContainer, FormInputRow } from "./style";
import Input from "@/components/common/Input";
import { Text, Input as IInput } from "@mantine/core";
import {
  AgentDistributorDetailsData,
  AgentDistributorDetailsForceUpdateData,
  AgentDistributorErrors,
} from "@/utils/types/distributor-agent";
import UploadedFileRow from "@/components/common/UploadedFileRow";
import { capitalize, validateAgentAttribute } from "@/utils/common-functions";
import { useEffect, useState } from "react";
import FileUploadInput from "@/components/common/FileUploadInput";

interface ForceEditFormProps {
  changes: AgentDistributorDetailsForceUpdateData;
  agentDetails: AgentDistributorDetailsData;
  handleChanges: (data: string | undefined, key: string) => void;
  setChangesValidity: (isValid: boolean) => void;
}

const ForceEditForm = ({
  changes,
  agentDetails,
  handleChanges,
  setChangesValidity,
}: ForceEditFormProps) => {
  const [errors, setErrors] = useState<AgentDistributorErrors>({});
  const handleUpdateErrors = (field: string, message: string) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      [field]: message,
    }));
  };

  useEffect(() => {
    const isValid = Object.values(errors).every((error) => error === "");
    setChangesValidity(isValid);
  }, [errors, setChangesValidity]);

  return (
    <FormEditFormContainer>
      <InputGroup label={`Nama ${capitalize(agentDetails.roleName)}`} mandatory>
        <Input
          placeholder={agentDetails.name}
          value={changes.name ?? agentDetails.name}
          onChange={(e) => {
            handleChanges(e.target.value, "name");
            validateAgentAttribute("name", e.target.value, handleUpdateErrors);
          }}
        />
        {errors.name && (
          <Text c={"error"} size="sm">
            {errors.name}
          </Text>
        )}
      </InputGroup>

      {agentDetails.roleName === "distributor" && (
        <InputGroup label={"NPWP"} mandatory>
          <Input
            placeholder={agentDetails.npwp}
            value={changes.npwp ?? agentDetails.npwp}
            onChange={(e) => {
              handleChanges(e.target.value, "npwp");
              validateAgentAttribute(
                "npwp",
                e.target.value,
                handleUpdateErrors
              );
            }}
          />
          {errors.npwp && (
            <Text c={"error"} size="sm">
              {errors.npwp}
            </Text>
          )}
        </InputGroup>
      )}

      <InputGroup label={"NIK"} mandatory>
        <Input
          placeholder={agentDetails.nik}
          value={changes.nik ?? agentDetails.nik}
          onChange={(e) => {
            handleChanges(e.target.value, "nik");
            validateAgentAttribute("nik", e.target.value, handleUpdateErrors);
          }}
        />
        {errors.nik && (
          <Text c={"error"} size="sm">
            {errors.nik}
          </Text>
        )}
      </InputGroup>

      <InputGroup label={"Alamat"} mandatory>
        <Input
          icon="info/location"
          placeholder={agentDetails.address}
          value={changes.address ?? agentDetails.address}
          onChange={(e) => {
            handleChanges(e.target.value, "address");
            validateAgentAttribute(
              "address",
              e.target.value,
              handleUpdateErrors
            );
          }}
        />
        {errors.address && (
          <Text c={"error"} size="sm">
            {errors.address}
          </Text>
        )}
      </InputGroup>

      <InputGroup label={"Alamat Warehouse"} mandatory>
        <Input
          icon="info/location"
          placeholder={agentDetails.warehouseAddress}
          value={changes.warehouseAddress ?? agentDetails.warehouseAddress}
          onChange={(e) => {
            handleChanges(e.target.value, "warehouseAddress");
            validateAgentAttribute(
              "warehouseAddress",
              e.target.value,
              handleUpdateErrors
            );
          }}
        />
        {errors.warehouseAddress && (
          <Text c={"error"} size="sm">
            {errors.warehouseAddress}
          </Text>
        )}
      </InputGroup>

      <InputGroup label={"Email"} mandatory>
        <Input
          icon="info/email"
          placeholder={agentDetails.email ?? "Email"}
          value={changes.email ?? agentDetails.email}
          onChange={(e) => {
            handleChanges(e.target.value, "email");
            validateAgentAttribute("email", e.target.value, handleUpdateErrors);
          }}
        />
        {errors.email && (
          <Text c={"error"} size="sm">
            {errors.email}
          </Text>
        )}
      </InputGroup>

      <FormInputRow>
        <InputGroup label={"Telepon Pribadi"} mandatory>
          <Input
            icon="info/phone"
            placeholder={agentDetails.ownerPhone}
            value={changes.phoneNumber ?? agentDetails.ownerPhone}
            onChange={(e) => {
              handleChanges(e.target.value, "phoneNumber");
              validateAgentAttribute(
                "phoneNumber",
                e.target.value,
                handleUpdateErrors
              );
            }}
          />
          {errors.phoneNumber && (
            <Text c={"error"} size="sm">
              {errors.phoneNumber}
            </Text>
          )}
        </InputGroup>

        <InputGroup label={"Telepon Admin"} mandatory>
          <Input
            icon="info/phone"
            placeholder={agentDetails.adminPhone}
            value={changes.adminPhoneNumber ?? agentDetails.adminPhone}
            onChange={(e) => {
              handleChanges(e.target.value, "adminPhoneNumber");
              validateAgentAttribute(
                "adminPhoneNumber",
                e.target.value,
                handleUpdateErrors
              );
            }}
          />
          {errors.adminPhoneNumber && (
            <Text c={"error"} size="sm">
              {errors.adminPhoneNumber}
            </Text>
          )}
        </InputGroup>
      </FormInputRow>

      <FormInputRow>
        <InputGroup label={"Link Tokopedia"}>
          <Input
            icon="social/tokopedia"
            placeholder={agentDetails.shop?.tokopedia ?? "Link Tokopedia"}
            value={changes.shops?.tokopedia ?? agentDetails.shop?.tokopedia}
            onChange={(e) => {
              handleChanges(e.target.value, "shops.tokopedia");
              validateAgentAttribute(
                "tokopedia",
                e.target.value,
                handleUpdateErrors
              );
            }}
          />
          {errors.tokopedia && (
            <Text c={"error"} size="sm">
              {errors.tokopedia}
            </Text>
          )}
        </InputGroup>

        <InputGroup label={"Link Shopee"}>
          <Input
            icon="social/shopee"
            placeholder={agentDetails.shop?.shopee ?? "Link Shopee"}
            value={changes.shops?.shopee ?? agentDetails.shop?.shopee}
            onChange={(e) => {
              handleChanges(e.target.value, "shops.shopee");
              validateAgentAttribute(
                "shopee",
                e.target.value,
                handleUpdateErrors
              );
            }}
          />
          {errors.shopee && (
            <Text c={"error"} size="sm">
              {errors.shopee}
            </Text>
          )}
        </InputGroup>
      </FormInputRow>

      <FormInputRow>
        <FileUploadInput
          required
          label="Upload SK Distributor"
          prefix={`Surat-Keterangan/${agentDetails.uoc}`}
          onChange={(uploadedFile: string) => {
            handleChanges(uploadedFile, "SK");
          }}
        />
        <IInput.Wrapper label="Uploaded SK Distributor">
          {changes.SK ? (
            <UploadedFileRow
              fileUrl={changes.SK}
              onDelete={() => {
                handleChanges(undefined, "SK");
              }}
            />
          ) : agentDetails.sk ? (
            <UploadedFileRow fileUrl={agentDetails.sk} />
          ) : (
            <Text c={"text-light"} fs={"italic"}>
              * Belum ada file yang di-upload *
            </Text>
          )}
        </IInput.Wrapper>
      </FormInputRow>
    </FormEditFormContainer>
  );
};

export default ForceEditForm;
