"use client";

import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { fetchUserProfile } from "@/store/slices/userProfileSlice";
import { logout, RoleType } from "@/store/slices/authSlice";
import { forbidden } from "next/navigation";

export const withAuth = <P extends object>(
  Component: React.FC<P>,
  allowedRoles: RoleType[] = []
) => {
  const WrappedComponent: React.FC<P> = (props) => {
    const dispatch: AppDispatch = useDispatch();
    const { accessToken, role, loggedOut } = useSelector(
      (state: RootState) => state.auth
    );

    useEffect(() => {
      const hasValidRole =
        role && ["admin", "distributor", "agent"].includes(role);
      const allowed =
        role && (allowedRoles.length === 0 || allowedRoles.includes(role));

      if (!allowed && accessToken) {
        forbidden();
      }

      if (!hasValidRole || !accessToken) {
        if (!loggedOut) {
          dispatch(logout());
        }
        return;
      }

      dispatch(fetchUserProfile());
    }, [accessToken, role, loggedOut, allowedRoles]);

    return accessToken ? <Component {...props} /> : null;
  };

  WrappedComponent.displayName = `withAuth(${
    Component.displayName || Component.name || "Component"
  })`;

  return WrappedComponent;
};
