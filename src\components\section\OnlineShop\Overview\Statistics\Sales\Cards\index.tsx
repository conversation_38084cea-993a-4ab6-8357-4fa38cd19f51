import { Radio, SimpleGrid, Skeleton } from "@mantine/core";
import InfoCard, { InfoCardProps } from "../../../InfoCard";
import Loop from "@/components/common/Loop";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import classes from "./style.module.css";
import { fetchSalesSummary } from "@/store/slices/onlineShopSlices/overviewSlice";
import {
  SalesChartCategories,
  SalesSummaryData,
} from "@/utils/types/online-shop";
import { useEffect } from "react";
import useOverviewFilters from "@/hooks/useOverviewFilters";

const STATS = (
  data: SalesSummaryData
): (InfoCardProps & { key: SalesChartCategories })[] => [
  {
    key: "sales",
    label: "Penjualan",
    value: data?.totalSales?.value,
    formatValueAs: "currency",
    description:
      "Total nilai dari pesanan siap dikirim dalam jangka waktu tertentu.",
    differences: data?.totalSales?.progressPercentage,
  },
  {
    key: "visitors",
    label: "Total Pengunjung",
    value: data?.totalVisits?.value,
    description: "Total pengunjung yang melihat halaman toko atau produk.",
    differences: data?.totalVisits?.progressPercentage,
  },
  {
    key: "productSeen",
    label: "Produk Dilihat",
    value: data?.productSeen?.value,
    description: "Total halaman toko atau produk dilihat.",
    differences: data?.productSeen?.value,
  },
  {
    key: "orders",
    label: "Pesanan",
    value: data?.totalOrders?.value,
    description: "Total jumlah pesanan siap dikirim.",
    differences: data?.totalOrders?.value,
  },
  {
    key: "conversionRate",
    label: "Tingkat Konversi",
    value: data?.conversionRate?.value,
    formatValueAs: "percentage",
    description:
      "Jumlah pembeli dengan pesanan siap dikirim dibagi jumlah pengunjung.",
    differences: data?.conversionRate?.value,
  },
  {
    key: "bestSellingProducts",
    label: "Produk Terlaris",
    value: data?.bestSellingProductVariant,
    description: "Kategori produk yang paling banyak dibeli.",
  },
];

const SalesStatisticsCards = ({ slug }: { slug?: string }) => {
  const dispatch: AppDispatch = useDispatch();
  const { salesSummary: data, loading } = useSelector(
    (state: RootState) => state.overview
  );
  const { time, salesChart, setSalesChart } = useOverviewFilters();

  const handleChange = (value: string | null) => {
    setSalesChart(value as SalesChartCategories);
  };

  useEffect(() => {
    if (time && !loading.salesSummary) {
      dispatch(fetchSalesSummary(time, slug));
    }
  }, [time, slug]);

  return (
    <Radio.Group value={salesChart} onChange={handleChange}>
      <SimpleGrid cols={{ base: 2, md: 3, xl: 6 }}>
        {loading.salesSummary ? (
          <Loop count={6}>
            <Skeleton h={145} />
          </Loop>
        ) : (
          STATS(data).map((stat) => (
            <Radio.Card
              disabled={loading.salesChart || loading.salesSummary}
              key={stat.key}
              value={stat.key}
              classNames={classes}
            >
              <InfoCard
                w="100%"
                h="100%"
                label={stat.label}
                value={stat.value}
                description={stat.description}
                formatValueAs={stat.formatValueAs}
                differences={stat.differences}
              />
            </Radio.Card>
          ))
        )}
      </SimpleGrid>
    </Radio.Group>
  );
};

export default SalesStatisticsCards;
