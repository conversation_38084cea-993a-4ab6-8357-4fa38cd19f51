import { Stack, Checkbox } from "@mantine/core";
import PrivacyPolicy from "../../PrivacyPolicy";
import { FormStepperProps } from "..";
import { useState } from "react";
import classes from "./style.module.css";

const UserAgreementForm: React.FC<FormStepperProps> = ({ onFormValid }) => {
  const [read, setRead] = useState(false);

  return (
    <Stack gap="xl">
      <PrivacyPolicy onRead={(read) => setRead(read)} />
      <Checkbox
        classNames={classes}
        disabled={!read}
        onChange={(target) => onFormValid(target.currentTarget.checked)}
        label="Saya setuju untuk mengikuti syarat & ketentuan dari <PERSON>."
        description="Saya memahami bahwa pelanggaran terhadap syarat & ketentuan ini dapat membuat NIK & NPWP saya diblacklist dari aplikasi distributor Hotto ke depannya."
      />
    </Stack>
  );
};

export default UserAgreementForm;
