import { SORT_FILTERS, SortFilterValue } from "@/utils/constants";
import { Group, Text, Select } from "@mantine/core";
import React, { useState } from "react";

export type UpdateLogsFilterValue = "" | "accept" | "reject";

export interface UpdateLogsDetailsHeaderProps {
  updateCount: number;
  onRequestFilterChange: (value: UpdateLogsFilterValue) => void;
  onSortFilterChange: (value: SortFilterValue) => void;
  sortFilter?: SortFilterValue;
  requestFilter?: UpdateLogsFilterValue;
}

const UpdateLogsDetailsHeader: React.FC<UpdateLogsDetailsHeaderProps> = ({
  updateCount,
  onRequestFilterChange,
  onSortFilterChange,
  sortFilter,
  requestFilter,
}) => {
  return (
    <Group align="center" justify="space-between">
      <Text>Terdapat {updateCount} update</Text>
      <Group gap="xs">
        <Select
          w="8em"
          data={[
            {
              value: "",
              label: "All",
            },
            {
              value: "accept",
              label: "Accepted",
            },
            {
              value: "reject",
              label: "Rejected",
            },
          ]}
          value={requestFilter}
          onChange={(value) =>
            onRequestFilterChange((value ?? "") as UpdateLogsFilterValue)
          }
        />
        <Select
          w="8em"
          data={SORT_FILTERS}
          value={sortFilter}
          onChange={(value) =>
            onSortFilterChange((value ?? "desc") as SortFilterValue)
          }
        />
      </Group>
    </Group>
  );
};

export default UpdateLogsDetailsHeader;
