import { Warning } from "@/utils/icons";
import { DistributorOrderItem } from "@/utils/types/distributor-register";
import { Paper, Text, Box, Stack, Anchor, Grid } from "@mantine/core";

const RejectAlert = ({ order }: { order: DistributorOrderItem }) => {
  const wa = process.env.NEXT_PUBLIC_HOTTO_ADMIN_WA || "-";
  const fileName = decodeURIComponent(
    order?.paymentProof?.split("/").pop() || ""
  );

  return (
    <Stack gap="xs">
      <Text fw={600}>Alasan penolakan</Text>
      <Text>“{order.rejectReason}”</Text>
      {order.paymentProof && (
        <Text>
          Bukti yang diupload:{" "}
          <Anchor href={order.paymentProof} target="_blank">
            {fileName}
          </Anchor>
        </Text>
      )}
      <Paper bg="error.1" p="sm" bd="1 solid error">
        <Grid columns={2} align="center">
          <Grid.Col span="content" style={{ align: "center" }}>
            <Warning size={28} color="var(--mantine-color-error-filled)" />
          </Grid.Col>
          <Grid.Col span="auto">
            <Box>
              <Text c="error" fw={500}>
                Silakan menghubungi admin kami untuk mendapatkan bantuan
              </Text>
              <Text c="text-black.9">
                Berikut adalah nomor yang dapat dihubungi (+62){wa}
              </Text>
            </Box>
          </Grid.Col>
        </Grid>
      </Paper>
    </Stack>
  );
};

export default RejectAlert;
