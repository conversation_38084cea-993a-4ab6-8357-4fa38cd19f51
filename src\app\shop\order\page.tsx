"use client";

import PageLayout from "@/components/common/PageLayout";
import OrderInputFilter from "@/components/section/OnlineShop/Order/Filter/Input";
import OrderStatusFilter from "@/components/section/OnlineShop/Order/Filter/Status";
import OrderList from "@/components/section/OnlineShop/Order/List";
import { withAuth } from "@/utils/withAuth";

const OrderPage = () => {
  return (
    <PageLayout title="Daftar Pesanan">
      <OrderStatusFilter />
      <OrderInputFilter />
      <OrderList />
    </PageLayout>
  );
};

export default withAuth(OrderPage);
