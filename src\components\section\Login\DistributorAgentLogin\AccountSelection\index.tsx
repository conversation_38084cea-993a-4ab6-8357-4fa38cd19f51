"use client";

import RadioCard from "@/components/common/RadioCard";
import {
  Box,
  Flex,
  Modal,
  Paper,
  Text,
  Title,
  Radio,
  Stack,
  ModalProps,
  RadioGroupProps,
  Button,
  ScrollArea,
  CheckIcon,
} from "@mantine/core";

export interface AccountSelectionWrapperProps {
  children: React.ReactNode;
}

export interface AccountSelectionListProps {
  name: string;
  address: string;
}

export interface AccountSelectionModalProps {
  data: AccountSelectionListProps[];
  opened: ModalProps["opened"];
  onClose: ModalProps["onClose"];
  value?: RadioGroupProps["value"];
  onChange?: RadioGroupProps["onChange"];
}

export const AccountSelectionWrapper = ({
  children,
}: AccountSelectionWrapperProps) => {
  return (
    <Paper p="md">
      <Stack gap="xl" w="100%">
        <AccountSelectionHeader />
        {children}
      </Stack>
    </Paper>
  );
};

export const AccountSelectionHeader = () => {
  return (
    <Box ta="center">
      <Title order={3}>Anda terdaftar memiliki beberapa akun</Title>
      <Text><PERSON>lakan pilih akun yang ingin Anda gunakan</Text>
    </Box>
  );
};

export const AccountSelectionList = ({
  name,
  address,
}: AccountSelectionListProps) => {
  return (
    <RadioCard
      value={name}
      key={name}
      withIndicator
      indicatorProps={{ icon: CheckIcon }}
    >
      <Title order={5}>{name}</Title>
      <Text c="text-light">{address}</Text>
    </RadioCard>
  );
};

const AccountSelectionModal = ({
  data,
  opened,
  onClose,
  value,
  onChange,
}: AccountSelectionModalProps) => {
  return (
    <Modal
      size="lg"
      centered
      scrollAreaComponent={ScrollArea.Autosize}
      {...{ opened, onClose }}
    >
      <AccountSelectionWrapper>
        <Radio.Group {...{ value, onChange }}>
          <Stack gap="xs">
            {data.map((account, index) => (
              <AccountSelectionList key={index} {...account} />
            ))}
          </Stack>
        </Radio.Group>
        <Flex justify="center">
          <Button
            disabled={!value}
            type="submit"
            onClick={onClose}
            w={{ base: "100%", sm: "auto" }}
          >
            Pilih
          </Button>
        </Flex>
      </AccountSelectionWrapper>
    </Modal>
  );
};

export default AccountSelectionModal;
