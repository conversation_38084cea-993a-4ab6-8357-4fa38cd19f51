import { COLORS } from "@/styles/color";
import { SPACES } from "@/styles/spacing";
import styled from "styled-components";

export const CityAgentsBoardContainer = styled.div`
  position: relative;
  width: 100%;
  flex: 1;
  overflow: scroll;

  &::-webkit-scrollbar-thumb {
    background-color: ${COLORS.disabled};
  }

  @media screen and (max-width: 768px) {
    padding-right: 0;
  }
`;

export const CityAgentsBoardWrapper = styled.div`
  position: static;
  width: max-content;
  height: 50vh;
  padding: ${SPACES.xl2} ${SPACES.xl} 0;

  display: flex;
  flex-direction: row;
  gap: ${SPACES.xl};

  &::-webkit-scrollbar-thumb {
    background-color: ${COLORS.disabled};
  }

  .show-all {
    cursor: pointer;
    transition: ease-in-out 0.33s;
    &:hover {
      color: ${COLORS.accent};
    }
  }
`;
