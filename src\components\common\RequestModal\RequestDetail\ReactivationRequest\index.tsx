import RequestOrderHistories from "@/components/section/AdminRequests/RequestOrdeHistories";
import { AppDispatch, RootState } from "@/store";
import { fetchRequestDetail } from "@/store/slices/adminRequestSlice";
import { RequestType } from "@/utils/types/request";
import { Flex, Text } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

interface RequestDetailContentProps {
  requestId: number;
}

const ReactivationRequestDetail = ({
  requestId,
}: RequestDetailContentProps) => {
  const dispatch: AppDispatch = useDispatch();
  const { reactivationRequest } = useSelector(
    (state: RootState) => state.adminRequest
  );

  useEffect(() => {
    dispatch(fetchRequestDetail(requestId, RequestType.Reactivation));
  }, [requestId]);

  const isMobile = useMediaQuery("(max-width: 768px)");
  return (
    <>
      {reactivationRequest && (
        <>
          <Flex
            direction={"column"}
            justify={"center"}
            align={"center"}
            gap={"xs"}
          >
            <Text fw={600}>{`${reactivationRequest.sourceRole || "-"} '${
              reactivationRequest.sourceAgent || "-"
            }'`}</Text>
          </Flex>
          <Flex
            mih={isMobile ? "33vh" : "fit-content"}
            mah={"33vh"}
            style={{ overflowY: "scroll" }}
            w={"100%"}
          >
            <RequestOrderHistories
              orderHistories={reactivationRequest.orderHistory}
            />
          </Flex>
        </>
      )}
    </>
  );
};

export default ReactivationRequestDetail;
