import Stepper from "@/components/common/Stepper";
import { Flex, Text, Title } from "@mantine/core";
import { DATA_CHANGE_REQUEST_STEPPER_DATA } from "..";
import useBreakpoints from "@/hooks/useBreakpoints";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

interface DataChangeModalHeaderProps {
  currStep: number;
  setCurrStep: (step: number) => void;
  nextStepValidation: () => boolean;
}

const DataChangeModalHeader = ({
  currStep,
  setCurrStep,
  nextStepValidation,
}: DataChangeModalHeaderProps) => {
  const { mobile } = useBreakpoints();
  const { role } = useSelector((state: RootState) => state.auth);

  const getStepTitle = () => {
    switch (currStep) {
      case 0:
        return role === "distributor"
          ? "Perubahan yang dibuat akan langsung mengubah data distributor & berdampak ke website Hotto."
          : "Perubahan yang dibuat akan berdampak ke website Hotto.";
      case 1:
        return "Berikut adalah perubahan yang ingin Anda buat";
      default:
        return "";
    }
  };

  return (
    <Flex
      direction={"column"}
      align={"center"}
      gap={"md"}
      w={mobile ? "80%" : "66%"}
      pb={"xs"}
    >
      <Title>Request Edit Data</Title>
      <Flex w={mobile ? "100%" : "75%"}>
        <Stepper
          active={currStep}
          steps={DATA_CHANGE_REQUEST_STEPPER_DATA(role)}
          onStepClick={(step: number) => {
            if (step < currStep || nextStepValidation()) {
              setCurrStep(step);
            }
          }}
        />
      </Flex>
      <Text
        size={mobile ? "sm" : "md"}
        c={"text-light"}
        fw={600}
        ta={"center"}
        h={"2.5em"}
      >
        {getStepTitle()}
      </Text>
    </Flex>
  );
};

export default DataChangeModalHeader;
