import { Flex } from "@mantine/core";
import { AvailableDistributor } from "@/utils/types/request";
import AvailableDistributorCard from "./AvailableDistributorCard";

interface AvailableDistributorsProps {
  availableDistributors: AvailableDistributor[];
  onSelect: (uoc: string) => void;
  selectedDistributor: string;
}

const AvailableDistributors = ({
  availableDistributors,
  onSelect,
  selectedDistributor,
}: AvailableDistributorsProps) => {
  return (
    <Flex direction={"column"} w={"100%"} gap={"xs"}>
      {availableDistributors.map((distributor) => (
        <AvailableDistributorCard
          key={distributor.uoc}
          distributor={distributor}
          selectedDistributor={selectedDistributor}
          onSelect={onSelect}
        />
      ))}
    </Flex>
  );
};

export default AvailableDistributors;
