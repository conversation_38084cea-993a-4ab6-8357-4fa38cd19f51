import { Warning } from "@/utils/icons";
import { Alert, Box, Paper, Text } from "@mantine/core";
import { RegisterData } from "@/utils/types/distributor-register";
import classes from "./style.module.css";

const DetailCard = ({ data }: { data: RegisterData }) => {
  const fullAddress = [
    data.warehouseAddress,
    data.warehouseKelurahan,
    data.warehouseDistrict,
    data.warehouseCity,
    data.warehouseProvince,
    data.warehousePostalCode,
  ].join(", ");

  return (
    <Box>
      <Text fz="h3" fw={500} mb="xs">
        Variasi Produk
      </Text>
      <Alert
        classNames={classes}
        variant="light"
        title="Anda diwajibkan untuk membeli produk minimum 120."
        icon={<Warning size={24} />}
      >
        Produk dengan jumlah {"<"} 120 pouch akan dikenakan biaya ongkir. Produk
        akan dikirimkan ke alamat warehouse Anda.
        <Paper mt="md" p="md" bg="bg-gray">
          <Text fz="lg" fw={600}>
            Detail Warehouse tersimpan
          </Text>
          <Text>
            {data.name}
            <br />
            {data.email} | {data.phoneNumber}
            <br />
            {fullAddress}
          </Text>
        </Paper>
      </Alert>
    </Box>
  );
};

export default DetailCard;
