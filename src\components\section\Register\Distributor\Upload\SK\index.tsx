import useBreakpoints from "@/hooks/useBreakpoints";
import { AppDispatch, RootState } from "@/store";
import {
  Center,
  Stack,
  Stepper,
  Title,
  Button,
  Text,
  Flex,
} from "@mantine/core";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import UploadSKForm from "./Form";
import SuccessUploadSK from "./Success";
import { submitNewSk } from "@/store/slices/distributorRegisterSlice";
import { MAX_WIDTH } from "@/utils/constants";

const RegisterUploadSK = ({ token }: { token: string }) => {
  const dispatch: AppDispatch = useDispatch();
  const { isLoading } = useSelector(
    (state: RootState) => state.distributorRegister
  );

  const { mobile } = useBreakpoints();

  const [active, setActive] = useState(0);
  const [continuable, setContinuable] = useState(false);

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [active]);

  const steps = [
    {
      label: "Upload Surat Kerja",
      step: (
        <>
          <Text c="text-light" mb="lg" ta="center">
            Isi form ini untuk menjadi partner distributor Hotto.
          </Text>
          <UploadSKForm
            onFormValid={(isValid) => {
              setContinuable(isValid);
            }}
          />
        </>
      ),
    },
    {
      label: "Menjadi Distributor",
      step: <SuccessUploadSK />,
    },
  ];

  return (
    <Center w="100%" maw={MAX_WIDTH} mx="auto">
      <Stack gap="xs" align="center">
        <Title ta="center" order={2}>
          Selamat, Aplikasi Anda sebagai Distributor Resmi Diterima!
        </Title>
        <Stack gap={32} w="100%">
          <Stepper
            active={active}
            //NOTE: sementara, karena ada perubahan design
            styles={{ steps: { display: "none" } }}
            allowNextStepsSelect={false}
          >
            {steps.map(({ label, step }, index) => (
              <Stepper.Step key={index} label={label}>
                {step}
              </Stepper.Step>
            ))}
          </Stepper>
          {active === 0 && (
            <Flex justify="center">
              <Button
                fullWidth={mobile}
                loading={isLoading}
                disabled={!continuable}
                onClick={() => {
                  dispatch(submitNewSk(token)).then(() => {
                    setActive(1);
                  });
                }}
              >
                Kirim
              </Button>
            </Flex>
          )}
        </Stack>
      </Stack>
    </Center>
  );
};

export default RegisterUploadSK;
