"use client";

import { store } from "@/store";
import { Suspense } from "react";
import { Provider } from "react-redux";
import { Toaster } from "sonner";

interface PageWrapperProps {
  children: React.ReactNode;
}

const PageWrapper = ({ children }: PageWrapperProps) => {
  return (
    <Provider store={store}>
      <Toaster richColors expand position="top-right" />
      <Suspense>{children}</Suspense>
    </Provider>
  );
};

export default PageWrapper;
