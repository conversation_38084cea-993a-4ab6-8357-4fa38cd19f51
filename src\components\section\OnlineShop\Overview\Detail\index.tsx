import SectionHeader from "@/components/common/SectionHeader";
import { getTodayDateTime } from "@/utils/common-functions";
import { Center, Loader, Stack } from "@mantine/core";
import OverviewStatisticFilters from "../Filters";
import useOverviewFilters from "@/hooks/useOverviewFilters";
import dynamic from "next/dynamic";
import DetailCustomerStatistics from "./Statistics/Customers";
const DetailSalesStatistics = dynamic(() => import("./Statistics/Sales"), {
  loading: () => Loading,
});

const Loading = (
  <Center>
    <Loader my="5em" />
  </Center>
);

const OverviewDetailSection = () => {
  const { stats } = useOverviewFilters();

  return (
    <Stack pb="lg">
      <SectionHeader
        title="Statistik"
        description={`Update terakhir: ${getTodayDateTime()}`}
      />
      <OverviewStatisticFilters />
      {stats === "sales" && <DetailSalesStatistics />}
      {stats === "demo" && <DetailCustomerStatistics />}
    </Stack>
  );
};

export default OverviewDetailSection;
