import Icon from "@/components/common/Icon";
import ReviewStatus from "@/components/common/ReviewStatus";
import useBreakpoints from "@/hooks/useBreakpoints";
import { formatDateToString, formatField } from "@/utils/common-functions";
import { UpdateLogData } from "@/utils/types/distributor-agent";
import { Flex, Box, Text, Stack } from "@mantine/core";
import React from "react";

export interface UpdateLogDetailProps {
  log: UpdateLogData;
}

const UpdateLogDetail = ({ log }: UpdateLogDetailProps) => {
  const { mobile } = useBreakpoints();

  return (
    <Flex justify="space-between" align="center">
      <Box>
        <Flex gap="xs" wrap="wrap" direction={{ base: "column", sm: "row" }}>
          <Text>{formatField(log.fieldChanged)}: </Text>
          <Flex gap="xs" wrap="wrap" align="center">
            <Text
              c="text"
              td="line-through"
              style={{ wordBreak: "break-word", whiteSpace: "normal" }}
            >
              {log.old !== "" ? log.old : "-"}
            </Text>
            <Icon icon="/arrow/right" size={12} />
            <Text style={{ wordBreak: "break-word", whiteSpace: "normal" }}>
              {log.new}
            </Text>
          </Flex>
        </Flex>
        <Text c="text">Alasan: {log.reason}</Text>
      </Box>
      <Flex gap={5} direction="column" align="flex-end">
        {/* TODO: add request proof button and modal */}
        <ReviewStatus
          status={
            log.status === 0
              ? "rejected"
              : log.status === 1
              ? "accepted"
              : "forced"
          }
        />
        {!mobile && (
          <Text c="text" size="sm">
            Diubah pada {formatDateToString(log.date!)}
          </Text>
        )}
      </Flex>
    </Flex>
  );
};

export default UpdateLogDetail;
