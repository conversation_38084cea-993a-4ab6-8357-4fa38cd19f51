import api from "@/lib/axios";
import { GET_USER_DETAILS } from "@/utils/api-routes";
import { createSlice } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { AppDispatch, RootState } from "..";
import { UserProfileData, UserProfileResponse } from "@/utils/types/auth";
import { AxiosResponse } from "axios";
import { isEmpty } from "@/utils/common-functions";

interface UserProfileState {
  loading: boolean;
  error?: string;
  data: UserProfileData;
}

const initialState: UserProfileState = {
  loading: false,
  error: undefined,
  data: {} as UserProfileData,
};

const userProfileSlice = createSlice({
  name: "userProfile",
  initialState,
  reducers: {
    setError(state, action) {
      state.error = action.payload;
      toast.error(action.payload);
    },
    setLoading(state, action) {
      state.loading = action.payload;
    },
    setData(state, action) {
      state.data = action.payload;
    },
  },
});

export const fetchUserProfile =
  (force: boolean = false) =>
  async (dispatch: AppDispatch, getState: () => RootState) => {
    // prevent refecth if data is already fetched
    if (!isEmpty(getState().userProfile.data) && !force) return;
    try {
      dispatch(userProfileSlice.actions.setLoading(true));
      const res: AxiosResponse<UserProfileResponse> = await api.get(
        GET_USER_DETAILS
      );
      if (res.data.status_code === 200 && res.data.data) {
        dispatch(userProfileSlice.actions.setData(res.data.data));
      } else {
        dispatch(userProfileSlice.actions.setError(res.data.message));
      }
    } catch {
      dispatch(userProfileSlice.actions.setError("Failed get user details."));
    } finally {
      dispatch(userProfileSlice.actions.setLoading(false));
    }
  };

export default userProfileSlice.reducer;
