import {
  DonutChart as Chart,
  DonutChartProps as ChartProps,
} from "@mantine/charts";
import {
  Box,
  Flex,
  BoxProps,
  Skeleton,
  Stack,
  Text,
  FlexProps,
  Group,
} from "@mantine/core";
import ChartLegend from "./Legend";
import { isEmpty } from "@/utils/common-functions";

export interface DonutChartProps extends BoxProps {
  data: {
    name: string;
    value: number;
  }[];
  chartLabel?: string;
  chartDescription?: string;
  legendLabel?: string;
  legendPosition?: "top" | "bottom" | "left" | "right";
  chartProps?: Omit<ChartProps, "data">;
  isPrice?: boolean;
  loading?: boolean;
}

const DonutChart = ({
  data = [],
  chartLabel,
  chartDescription,
  legendLabel,
  legendPosition,
  chartProps,
  isPrice,
  loading,
  ...props
}: DonutChartProps) => {
  const shades = ["#6560B0", "#7373BE", "#8E93CC", "#B3B7DD", "#D2D4EB"];

  const vertical = legendPosition === "top" || legendPosition === "bottom";

  const chartDatas = data
    .sort((a, b) => b.value - a.value)
    .map((item, index) => ({
      ...item,
      color: shades[Math.min(4, index)],
      most: item.value === Math.max(...data.map((item) => item.value)),
    }));

  const getDirection = (): FlexProps["direction"] => {
    switch (legendPosition) {
      case "top":
        return "column-reverse";
      case "left":
        return "row-reverse";
      case "right":
        return "row";
      default:
        return "column";
    }
  };

  if (loading) {
    return (
      <Group justify={"center"}>
        <Skeleton circle width={250} height={250} />
        <Skeleton width={300} height={220} />
      </Group>
    );
  }

  return (
    <Flex
      direction={getDirection()}
      gap={vertical ? "md" : "xl"}
      justify={"center"}
      align={"center"}
      {...props}
    >
      {!isEmpty(data) ? (
        <>
          <Box pos={"relative"}>
            <Chart
              data={chartDatas}
              thickness={40}
              strokeWidth={0}
              size={250}
              tooltipDataSource="segment"
              {...chartProps}
            />
            <Stack
              pos={"absolute"}
              top={"50%"}
              left={"50%"}
              justify={"center"}
              align={"center"}
              gap={0}
              ta={"center"}
              style={{
                transform: "translate(-50%, -50%)",
                zIndex: -1,
              }}
            >
              {chartLabel && (
                <Text fw={500} c={"text"} size={"sm"}>
                  {chartLabel}
                </Text>
              )}
              {chartDescription && (
                <Text fw={700} size={"xl"}>
                  {chartDescription}
                </Text>
              )}
            </Stack>
          </Box>
          {legendLabel && (
            <ChartLegend
              data={chartDatas}
              label={legendLabel}
              isPrice={isPrice}
              vertical={vertical}
            />
          )}
        </>
      ) : (
        <Text size={"sm"} c={"text"} p={"xl"}>
          {`Tidak ada data untuk ${legendLabel}`}
        </Text>
      )}
    </Flex>
  );
};

export default DonutChart;
