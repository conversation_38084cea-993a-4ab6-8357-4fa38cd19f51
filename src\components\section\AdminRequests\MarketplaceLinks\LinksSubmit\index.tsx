import useBreakpoints from "@/hooks/useBreakpoints";
import { Box, Paper, Group, Button, Checkbox } from "@mantine/core";
import { useElementSize } from "@mantine/hooks";
import { useEffect, useState } from "react";

export interface LinksSubmitProps {
  disabled?: boolean;
}

const LinksSubmit: React.FC<LinksSubmitProps> = ({ disabled }) => {
  const { ref, height } = useElementSize();
  const [checked, setChecked] = useState(false);
  const { mobile } = useBreakpoints();

  useEffect(() => {
    setChecked(false);
  }, [disabled]);

  return (
    <Box>
      <Box h={height + 30} />
      <Paper
        ref={ref}
        pos="fixed"
        bottom={0}
        left={0}
        miw="100%"
        p={{ base: "sm", md: "md" }}
        radius={0}
        style={{ borderTop: "1px solid var(--mantine-color-bg-filled)" }}
      >
        {/* NOTE: temporary `ml` until sidebar revamped */}
        <Group justify="space-between" ml={{ md: 295 }}>
          <Checkbox
            required
            checked={checked}
            disabled={disabled}
            name="confirmation"
            label="Semua link sudah saya periksa dan dapat dipertanggungjawabkan."
            onChange={(e) => setChecked(e.currentTarget.checked)}
          />
          <Button
            type="submit"
            fullWidth={mobile}
            disabled={!checked || disabled}
            mb={{ base: "sm", md: 0 }}
          >
            Submit
          </Button>
        </Group>
      </Paper>
    </Box>
  );
};

export default LinksSubmit;
