import Image from "next/image";
import { ImageOptionContainer } from "./style";

interface ProductImageContainerProps {
  image: string;
  selected: boolean;
  onClick: () => void;
}

const ProductImageContainer = ({
  image,
  selected,
  onClick,
}: ProductImageContainerProps) => {
  return (
    <ImageOptionContainer onClick={onClick} selected={selected}>
      <Image src={image} alt="product" width={200} height={200} />
    </ImageOptionContainer>
  );
};

export default ProductImageContainer;
