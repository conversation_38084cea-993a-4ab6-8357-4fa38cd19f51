import Dot from "@/components/icons/Dot";
import { isEmpty } from "@/utils/common-functions";
import { Flex, NumberFormatter, Paper, Stack, Text } from "@mantine/core";

interface ChartLegendProps {
  data: {
    name: string;
    value: number;
    color: string;
  }[];
  label: string;
  isPrice?: boolean;
  vertical?: boolean;
}

const ChartLegend = ({
  data,
  label,
  isPrice = false,
  vertical = false,
}: ChartLegendProps) => {
  return (
    <Paper bg={"bg"} w={vertical ? "100%" : "fit-content"}>
      <Stack gap={"md"} w={"100%"} p={"lg"}>
        <Stack gap={"xs"}>
          <Text size="sm" c={"text-light"}>
            {label}
          </Text>
          <Flex gap={"xs"} align={"center"}>
            <Dot color={data[0].color} fontSize={12} />
            <Text size="md" fw={700}>
              {data[0].name}
            </Text>

            <Text c={"text-light"} size="sm" fw={500}>
              {isPrice ? (
                <>
                  (
                  <NumberFormatter value={data[0].value} />)
                </>
              ) : (
                `(${data[0].value})`
              )}
            </Text>
          </Flex>
        </Stack>
        <Stack gap={"xs"}>
          <Text size="sm" c={"text-light"}>{`Other ${label}`}</Text>
          {!isEmpty(data) ? (
            data.slice(0, 3).map((item, index) => (
              <Flex key={index} gap={"xs"} align={"center"}>
                <Dot color={data[index].color} fontSize={8} />
                <Text size="sm" fw={700}>
                  {item.name}
                </Text>
                <Text c={"text-light"} size="sm" fw={500}>
                  {isPrice ? (
                    <>
                      (
                      <NumberFormatter value={item.value} />)
                    </>
                  ) : (
                    `(${item.value})`
                  )}
                </Text>
              </Flex>
            ))
          ) : (
            <Text c="text-light" size="sm">
              -
            </Text>
          )}
        </Stack>
      </Stack>
    </Paper>
  );
};

export default ChartLegend;
