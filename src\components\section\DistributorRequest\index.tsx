import { Flex } from "@mantine/core";
import RequestReceived from "./RequestReceived";
import RequestSent from "./RequestSent";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { useEffect } from "react";
import {
  fetchReceivedRequests,
  fetchSentRequests,
} from "@/store/slices/distributorRequestSlice";
import RequestFilter from "./RequestFilter";
import { useSearchParams } from "next/navigation";
import useBreakpoints from "@/hooks/useBreakpoints";

const DistributorRequest = () => {
  const dispatch: AppDispatch = useDispatch();
  const searchParams = useSearchParams();
  const filter = searchParams.get("filter");
  const { role } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (filter === "sent") {
      dispatch(fetchSentRequests());
    } else if (filter === "received") {
      dispatch(fetchReceivedRequests());
    } else {
      dispatch(fetchSentRequests());
      dispatch(fetchReceivedRequests());
    }
  }, [filter]);

  const { tablet, mobile } = useBreakpoints();
  return (
    <Flex
      direction={"column"}
      w={mobile || tablet ? "100%" : "55%"}
      gap={"md"}
      pb={mobile || tablet ? "3em" : "md"}
    >
      {<RequestFilter />}

      {(filter === "received" || filter === "all") && <RequestReceived />}
      {(filter === "sent" || filter === "all") && <RequestSent />}
    </Flex>
  );
};

export default DistributorRequest;
