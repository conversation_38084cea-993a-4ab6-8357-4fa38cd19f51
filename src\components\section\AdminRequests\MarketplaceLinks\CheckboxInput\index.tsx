"use client";

import {
  AdminRequestLinksByIslandData,
  AdminRequestLinksByIslandLinks,
  AdminSubmitInvalidLinksReasonsData,
} from "@/utils/types/request";
import { Checkbox, Flex, TextInput } from "@mantine/core";
import { useEffect, useRef, useState } from "react";

interface CheckboxInputProps {
  uoc: AdminRequestLinksByIslandData["uoc"];
  linkToCheck: AdminRequestLinksByIslandLinks;
  onChange: (
    data: AdminSubmitInvalidLinksReasonsData,
    remove?: boolean
  ) => void;
}

const CheckboxInput: React.FC<CheckboxInputProps> = ({
  linkToCheck: { id, url, isValid, type, reason },
  uoc,
  onChange,
}) => {
  const [checked, setChecked] = useState<boolean>(isValid);
  const [value, setValue] = useState<string>(reason);
  const timeoutId = useRef<number | null>(null);

  useEffect(() => {
    if (reason || reason !== "") {
      onChange({
        id,
        userID: uoc,
        type,
        url,
        reason,
      });
    }
  }, [reason]);

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newChecked = e.currentTarget.checked;
    setChecked(newChecked);
    if (value && value !== "") {
      onChange({ id, userID: uoc, type, url, reason: value }, newChecked);
    }
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.currentTarget.value;
    const remove = !newValue || newValue === "";
    setValue(newValue);
    if (timeoutId.current) clearTimeout(timeoutId.current);
    timeoutId.current = window.setTimeout(() => {
      if (!checked) {
        onChange({ id, userID: uoc, type, url, reason: newValue }, remove);
      }
    }, 500);
  };

  return (
    <Flex gap="sm" align="center">
      <Checkbox checked={checked} onChange={handleCheckboxChange} />
      <TextInput
        required
        disabled={checked}
        value={value}
        onChange={handleTextChange}
      />
    </Flex>
  );
};

export default CheckboxInput;
