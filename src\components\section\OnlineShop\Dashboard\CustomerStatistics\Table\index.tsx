import {
  Flex,
  Stack,
  Table,
  Skeleton,
  Text,
  NumberFormatter,
} from "@mantine/core";
import { CustomerData } from "@/utils/types/online-shop";
import Pagination from "@/components/common/Pagination";
import { useEffect, useState } from "react";
import { fetchCustomerStatsList } from "@/store/slices/onlineShopSlices/dashboardSlice";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { ITEMS_PER_PAGE } from "@/utils/constants";
import { formatDateToString } from "@/utils/common-functions";

const TABLE_HEADERS = [
  "Username",
  "Produk dibeli terkahir",
  "Tanggal terakhir pembelian",
  "Alamat",
  "Total Bought",
  "Total Spend",
];

const CustomerStatisticsTable = () => {
  const {
    customerStatsList,
    loading: { customerStatsList: loading },
  } = useSelector((state: RootState) => state.dashboard);
  const dispatch = useDispatch<AppDispatch>();

  const [page, setPage] = useState(1);

  useEffect(() => {
    dispatch(fetchCustomerStatsList(page));
  }, [page]);

  const tableStyle = {
    borderTop: "1px solid var(--mantine-color-bg-gray-filled)",
    borderBottom: "1px solid var(--mantine-color-bg-gray-filled)",
  };

  const headerStyle = {
    borderInlineEnd: "1px solid var(--mantine-color-bg-filled)",
  };

  const loadingRows = Array(5)
    .fill(null)
    .map((_, index) => (
      <Table.Tr key={index}>
        {Array(6)
          .fill(null)
          .map((_, cellIndex) => (
            <Table.Td key={cellIndex}>
              <Skeleton height={20} />
            </Table.Td>
          ))}
      </Table.Tr>
    ));

  const tableRows = customerStatsList?.customerStats?.length ? (
    customerStatsList.customerStats.map((data, index) => (
      <Table.Tr key={index}>
        <Table.Td>{data.name}</Table.Td>
        <Table.Td>{data.lastBoughtProduct || "-"}</Table.Td>
        <Table.Td>
          {data.lastOrderDate ? formatDateToString(data.lastOrderDate) : "-"}
        </Table.Td>
        <Table.Td>{data.address || "-"}</Table.Td>
        <Table.Td>{data.totalOrders || "-"}</Table.Td>
        <Table.Td>
          {data.totalSpend ? <NumberFormatter value={data.totalSpend} /> : "-"}
        </Table.Td>
      </Table.Tr>
    ))
  ) : (
    <Table.Tr>
      <Table.Td colSpan={6} style={{ textAlign: "center" }}>
        <Text c="text-light" p="xl" mih={"16em"}>
          Oops, Gagal memuat data pelanggan, coba lagi nanti!
        </Text>
      </Table.Td>
    </Table.Tr>
  );

  return (
    <Stack>
      <Table.ScrollContainer minWidth={"900px"}>
        <Table
          withColumnBorders
          verticalSpacing="md"
          horizontalSpacing="md"
          style={tableStyle}
        >
          <Table.Thead>
            <Table.Tr c="text-light">
              {TABLE_HEADERS.map((header, index) => (
                <Table.Th key={index} style={headerStyle}>
                  {header}
                </Table.Th>
              ))}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{loading ? loadingRows : tableRows}</Table.Tbody>
        </Table>
      </Table.ScrollContainer>

      <Flex justify="flex-end" w="100%">
        <Pagination
          total={Math.ceil(customerStatsList?.totalItems || 1 / ITEMS_PER_PAGE)}
          page={page}
          onChange={setPage}
          disabled={loading}
        />
      </Flex>
    </Stack>
  );
};

export default CustomerStatisticsTable;
