export interface AdminBroadcast {
  id: number;
  title: string;
  body: string;
  type: BroadcastType;
  documents: string[];
  isDraft: boolean;
  isAllRecipient: boolean;
  recipients: BroadcastRecipient[] | string[];
  createdAt: string;
  updatedAt?: string;
}

export interface AdminPastBroadcast extends AdminBroadcast {
  recipientsDetail: BroadcastRecipient[];
}
export interface CreateBroadcast {
  title: string;
  body: string;
  documents: string[];
  type: BroadcastType;
  isAllRecipient: boolean;
  recipients: string[];
}

export interface BroadcastRecipient {
  uoc: string;
  name: string;
  role: string;
}

export enum BroadcastType {
  WARNING = "warning_letter",
  PUBLIC_ANNOUNCMENT = "public_announcement",
  PRIVATE_ANNOUNCMENT = "private_announcement",
  PROMOTION = "promotion",
}
