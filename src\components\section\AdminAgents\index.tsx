"use client";

import dynamic from "next/dynamic";
import { Flex } from "@mantine/core";
import AgentsHeaderRow from "./AgentsHeaderRow";
import CityAgentsBoard from "./CityAgentsBoard";
import { useEffect } from "react";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { fetchIslands } from "@/store/slices/adminAgentListSlice";
import { padding } from "@/components/common/PageLayout";
import IslandSelections from "@/components/common/IslandSelections";

const AdminAgents = () => {
  const dispatch: AppDispatch = useDispatch();
  const { loading } = useSelector((state: RootState) => state.adminAgentList);

  useEffect(() => {
    dispatch(fetchIslands());
  }, []);

  return (
    <Flex direction={"column"} h={"100%"} mah={"100%"}>
      <AgentsHeaderRow />
      <Flex mx={`-${padding}`} h={"100%"} pos={"relative"} direction={"column"}>
        <IslandSelections disabled={loading} />
        <CityAgentsBoard />
      </Flex>
    </Flex>
  );
};

export default AdminAgents;
