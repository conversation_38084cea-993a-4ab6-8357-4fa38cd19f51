import api from "@/lib/ecommerce";
import {
  CREATE_PRODUCT,
  GET_PRODUCT_DETAIL,
  GET_PRODUCT_IMAGES,
  UPDATE_PRODUCT_DETAIL,
} from "@/utils/api-routes";
import { createSlice } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { ProductDetail } from "@/utils/types/online-shop";
import { AppDispatch } from "@/store";
import { AxiosError } from "axios";

interface ProductDetailState {
  loading: boolean;
  loadingImages: boolean;
  error?: string;
  productDetail?: ProductDetail;
  images: string[];
}

const initialState: ProductDetailState = {
  loading: false,
  loadingImages: false,
  error: undefined,
  productDetail: undefined,
  images: [],
};

const productDetailSlice = createSlice({
  name: "productDetail",
  initialState,
  reducers: {
    setError(state, action) {
      state.error = action.payload;
      toast.error(action.payload);
    },
    setLoading(state, action) {
      state.loading = action.payload;
    },
    setLoadingImages(state, action) {
      state.loadingImages = action.payload;
    },
    setProductDetail(state, action) {
      state.productDetail = action.payload;
    },
    setImages(state, action) {
      state.images = action.payload.images;
    },
  },
});

export const fetchProductDetail =
  (productId: string) => async (dispatch: AppDispatch) => {
    try {
      dispatch(productDetailSlice.actions.setLoading(true));
      const res = await api.get(GET_PRODUCT_DETAIL(productId));
      if (res.data.status_code === 200 && res.data.data) {
        dispatch(productDetailSlice.actions.setProductDetail(res.data.data));
      } else {
        dispatch(productDetailSlice.actions.setError(res.data.message));
      }
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        dispatch(
          productDetailSlice.actions.setError(error.response?.data.message)
        );
      } else {
        dispatch(
          productDetailSlice.actions.setError(
            "Gagal mendapatkan detail produk."
          )
        );
      }
    } finally {
      dispatch(productDetailSlice.actions.setLoading(false));
    }
  };

export const createProduct =
  (product: ProductDetail, onSuccess?: () => void) =>
  async (dispatch: AppDispatch) => {
    dispatch(productDetailSlice.actions.setLoading(true));
    try {
      const res = await api.post(CREATE_PRODUCT, product);
      if (res.data.status_code === 201) {
        toast.success("Product created successfully");
        onSuccess?.();
      } else {
        dispatch(productDetailSlice.actions.setError(res.data.message));
      }
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        dispatch(
          productDetailSlice.actions.setError(error.response?.data.message)
        );
      } else {
        dispatch(productDetailSlice.actions.setError("Gagal membuat produk."));
      }
    } finally {
      dispatch(productDetailSlice.actions.setLoading(false));
    }
  };

export const updateProductDetail =
  (productId: string, productDetail: ProductDetail, onSuccess?: () => void) =>
  async (dispatch: AppDispatch) => {
    dispatch(productDetailSlice.actions.setLoading(true));
    try {
      const res = await api.put(UPDATE_PRODUCT_DETAIL(productId), {
        name: productDetail.name,
        description: productDetail.description,
        status: productDetail.status,
        images: productDetail.images,
        minimumOrder: productDetail.minimumOrder,
        totalStocks: productDetail.totalStocks,
        unitPrice: productDetail.unitPrice,
      });

      if (res.data.status_code === 200) {
        toast.success("Product updated successfully");
        onSuccess?.();
      } else {
        dispatch(productDetailSlice.actions.setError(res.data.message));
      }
    } catch (error: unknown) {
      dispatch(
        productDetailSlice.actions.setError(
          error instanceof AxiosError
            ? error.response?.data.message
            : "Gagal memperbarui produk."
        )
      );
    } finally {
      dispatch(productDetailSlice.actions.setLoading(false));
    }
  };

export const fetchProductImages = () => async (dispatch: AppDispatch) => {
  try {
    dispatch(productDetailSlice.actions.setLoadingImages(true));
    const res = await api.get(GET_PRODUCT_IMAGES);
    if (res.data.status_code === 200 && res.data.data) {
      dispatch(productDetailSlice.actions.setImages(res.data.data));
    } else {
      dispatch(productDetailSlice.actions.setError(res.data.message));
    }
  } catch (error: unknown) {
    if (error instanceof AxiosError) {
      dispatch(
        productDetailSlice.actions.setError(error.response?.data.message)
      );
    } else {
      dispatch(
        productDetailSlice.actions.setError("Gagal mengambil gambar produk.")
      );
    }
  } finally {
    dispatch(productDetailSlice.actions.setLoadingImages(false));
  }
};

export default productDetailSlice.reducer;
