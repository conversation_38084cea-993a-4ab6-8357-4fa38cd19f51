"use client";

import {
  AppShell,
  AppShellFooterProps,
  AppShellHeaderProps,
  AppShellMainProps,
  AppShellNavbarProps,
  AppShellProps,
  MantineSpacing,
} from "@mantine/core";
import { useDisclosure, useElementSize, useHeadroom } from "@mantine/hooks";
import { isEmpty } from "@/utils/common-functions";
import Navbar from "./Navbar";
import Header from "./Header";
import NavigationTitle, { NavigationTitleProps } from "./NavigationTitle";
import { memo } from "react";
import dynamic from "next/dynamic";
const ResetPasswordForm = dynamic(
  () => import("@/components/section/ResetPasswordForm"),
  { ssr: false }
);

export interface UseHeadroomInput {
  fixedAt?: number;
  onPin?: () => void;
  onFix?: () => void;
  onRelease?: () => void;
}

export interface PageLayoutProps extends AppShellProps {
  title?: string;
  navigationContent?: NavigationTitleProps;
  headerContent?: React.ReactNode;
  footerContent?: React.ReactNode;
  autoHideHeader?: boolean;
  autoHideHeaderConfig?: UseHeadroomInput;
  headerProps?: Partial<AppShellHeaderProps>;
  navbarProps?: Partial<AppShellNavbarProps>;
  mainProps?: Partial<AppShellMainProps>;
  footerProps?: Partial<AppShellFooterProps>;
}

export const padding: MantineSpacing = "lg";

const PageLayout: React.FC<PageLayoutProps> = ({
  title = "Hotto ADMS",
  navigationContent,
  headerContent,
  footerContent,
  autoHideHeader,
  autoHideHeaderConfig = { fixedAt: 120 },
  headerProps,
  navbarProps,
  mainProps,
  footerProps,
  children,
  ...props
}) => {
  const { ref, height } = useElementSize();
  const pinned = useHeadroom(autoHideHeaderConfig);
  const [opened, { toggle }] = useDisclosure(false);

  return (
    <>
      <ResetPasswordForm />
      <AppShell
        layout="alt"
        padding={padding}
        header={{ height: 90, collapsed: autoHideHeader ? !pinned : false }}
        navbar={{
          width: "18.5em",
          breakpoint: "sm",
          collapsed: { mobile: !opened },
        }}
        {...props}
      >
        <Header
          opened={opened}
          toggle={toggle}
          title={title}
          headerContent={headerContent}
          {...headerProps}
        />
        <Navbar opened={opened} toggle={toggle} {...navbarProps} />
        <AppShell.Main
          h="100dvh"
          pb={footerContent ? height + 50 : 0}
          {...mainProps}
        >
          {navigationContent ? (
            <NavigationTitle {...navigationContent} />
          ) : null}
          {children}
        </AppShell.Main>
        {!isEmpty(footerContent) ? (
          <AppShell.Footer p={padding} ref={ref} {...footerProps}>
            {footerContent}
          </AppShell.Footer>
        ) : null}
      </AppShell>
    </>
  );
};

export default memo(PageLayout);
