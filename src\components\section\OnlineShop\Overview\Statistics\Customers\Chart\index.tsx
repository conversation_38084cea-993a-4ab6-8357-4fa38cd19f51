import DonutChart from "@/components/common/DonutChart";
import SectionHeader from "@/components/common/SectionHeader";
import useBreakpoints from "@/hooks/useBreakpoints";
import useOverviewFilters from "@/hooks/useOverviewFilters";
import { AppDispatch, RootState } from "@/store";
import { fetchCustomerChartData } from "@/store/slices/onlineShopSlices/overviewSlice";
import { formatDate, formatNumber, isEmpty } from "@/utils/common-functions";
import { AreaChart } from "@mantine/charts";
import { Box, Center, Skeleton, Text } from "@mantine/core";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import ChartTooltip, { ChartTooltipProps } from "../../ChartTooltip";

const CustomersStatisticsChart = ({ slug }: { slug?: string }) => {
  const dispatch: AppDispatch = useDispatch();
  const {
    customerChartData: data,
    customerSummary,
    loading: { customerChart: loading },
  } = useSelector((state: RootState) => state.overview);
  const { time, customerChart } = useOverviewFilters();
  const { mobile } = useBreakpoints();

  const empty = (isEmpty(data) || isEmpty(data.content)) && !loading;
  const areaChart = !["topArea", "bestSellingHours"].includes(customerChart);

  const getTimeLabel = (): string => {
    return time === "daily"
      ? "Harian"
      : time === "weekly"
      ? "Mingguan"
      : "Bulanan";
  };

  const getStatsLabel = (): string => {
    switch (customerChart) {
      case "topArea":
        return "Top Area";
      case "repeatCustomersPercentage":
        return "Persentase Pembeli Berulang";
      case "bestSellingHours":
        return "Jam Penjualan Terlaris";
      case "averageRating":
        return "Average Rating";
    }
  };

  const getChartDescription = (): string => {
    switch (customerChart) {
      case "topArea":
        return customerSummary.topArea;
      case "bestSellingHours":
        return customerSummary.bestSellingHours;
      default:
        return "";
    }
  };

  const CHART_DATA =
    data?.content?.map((item) => ({
      name: areaChart ? formatDate(item.label) : item.label,
      value: item.value,
    })) || [];

  useEffect(() => {
    if (time && customerChart && !isEmpty(customerSummary) && !loading) {
      dispatch(fetchCustomerChartData(time, customerChart, slug));
    }
  }, [time, customerChart, isEmpty(customerSummary)]);

  return (
    <Box my="xl">
      <SectionHeader
        mb="xs"
        title={`Grafik ${getStatsLabel()} ${getTimeLabel()}`}
      />
      <Center>
        {empty ? (
          <Text my={(areaChart ? 175 : 250) / 2}>
            Data grafik belum dapat ditampilkan, silahkan gunakan filter lain.
          </Text>
        ) : (
          <Skeleton height={areaChart ? 175 : "auto"} visible={loading}>
            {areaChart ? (
              <AreaChart
                h={175}
                data={CHART_DATA}
                dataKey="name"
                series={[{ name: "value", color: "primary.6" }]}
                tooltipProps={{
                  content: ({ label, payload }) => (
                    <ChartTooltip
                      title={getStatsLabel()}
                      label={label}
                      payload={payload as ChartTooltipProps["payload"]}
                      formatValueAs={
                        ["repeatCustomersPercentage", "averageRating"].includes(
                          customerChart
                        )
                          ? "percentage"
                          : undefined
                      }
                    />
                  ),
                }}
                curveType="linear"
                gridAxis="none"
                withYAxis={false}
                withDots={false}
                tooltipAnimationDuration={300}
                fillOpacity={0.5}
              />
            ) : (
              <DonutChart
                data={CHART_DATA}
                chartLabel={getStatsLabel()}
                chartDescription={getChartDescription()}
                legendLabel={getStatsLabel()}
                legendPosition={mobile ? "bottom" : "right"}
                isPrice
              />
            )}
          </Skeleton>
        )}
      </Center>
    </Box>
  );
};

export default CustomersStatisticsChart;
