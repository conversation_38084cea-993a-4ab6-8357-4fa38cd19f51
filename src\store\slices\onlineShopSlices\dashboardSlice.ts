import {
  BestSellerProduct,
  CustomerStatsList,
  CustomerStatsOverview,
  ShopStats,
  TodayStats,
} from "@/utils/types/online-shop";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { toast } from "sonner";

import api from "@/lib/ecommerce";

import {
  BEST_SELLER_PRODUCTS,
  CUSTOMER_STATS_LIST,
  CUSTOMER_STATS_OVERVIEW,
  DOWNLOAD_CUSTOMER_STATS,
  SHOP_BALANCE,
  SHOP_STATS,
  TODAY_STATS,
} from "@/utils/api-routes";
import { AppDispatch } from "@/store";

interface DashboardState {
  shopStats?: ShopStats;
  todayStats?: TodayStats;
  bestSellerProducts?: BestSellerProduct[];
  shopBalance?: number;
  customerStatsOverview?: CustomerStatsOverview;
  customerStatsList?: CustomerStatsList;
  loading: DashboardLoading;
  error?: string;
}

interface DashboardLoading {
  shopStats: boolean;
  todayStats: boolean;
  bestSellerProducts: boolean;
  shopBalance: boolean;
  customerStatsOverview: boolean;
  customerStatsList: boolean;
  customerStatsDownload: boolean;
}

const initialState: DashboardState = {
  shopStats: undefined,
  todayStats: undefined,
  bestSellerProducts: undefined,
  shopBalance: undefined,

  loading: {
    shopStats: false,
    todayStats: false,
    bestSellerProducts: false,
    shopBalance: false,
    customerStatsOverview: false,
    customerStatsList: false,
    customerStatsDownload: false,
  },

  error: undefined,
};

const dashboardSlice = createSlice({
  name: "dashboard",
  initialState,
  reducers: {
    setShopStats(state, action: PayloadAction<ShopStats>) {
      state.shopStats = action.payload;
    },
    setTodayStats(state, action: PayloadAction<TodayStats>) {
      state.todayStats = action.payload;
    },
    setBestSellerProducts(state, action: PayloadAction<BestSellerProduct[]>) {
      state.bestSellerProducts = action.payload;
    },
    setShopBalance(state, action: PayloadAction<number>) {
      state.shopBalance = action.payload;
    },
    setCustomerStatsOverview(
      state,
      action: PayloadAction<CustomerStatsOverview>
    ) {
      state.customerStatsOverview = action.payload;
    },
    setCustomerStatsList(state, action: PayloadAction<CustomerStatsList>) {
      state.customerStatsList = action.payload;
    },
    setLoading(
      state,
      action: PayloadAction<{ type: keyof DashboardLoading; loading: boolean }>
    ) {
      state.loading[action.payload.type] = action.payload.loading;
    },
    setError(state, action: PayloadAction<string>) {
      toast.error(action.payload);
      state.error = action.payload;
    },
  },
});

export const fetchTodayStats = () => async (dispatch: AppDispatch) => {
  try {
    dispatch(
      dashboardSlice.actions.setLoading({ type: "todayStats", loading: true })
    );
    const res = await api.get(TODAY_STATS);
    if (res) {
      dispatch(dashboardSlice.actions.setTodayStats(res.data.data));
    }
  } catch {
    dispatch(
      dashboardSlice.actions.setError("Gagal mendapatkan data untuk hari ini.")
    );
  } finally {
    dispatch(
      dashboardSlice.actions.setLoading({ type: "todayStats", loading: false })
    );
  }
};

export const fetchShopStats = () => async (dispatch: AppDispatch) => {
  try {
    dispatch(
      dashboardSlice.actions.setLoading({ type: "shopStats", loading: true })
    );
    const res = await api.get(SHOP_STATS);
    if (res) {
      dispatch(dashboardSlice.actions.setShopStats(res.data.data));
    }
  } catch {
    dispatch(
      dashboardSlice.actions.setError(
        "Gagal mendapatkan data untuk stats toko."
      )
    );
  } finally {
    dispatch(
      dashboardSlice.actions.setLoading({ type: "shopStats", loading: false })
    );
  }
};

export const fetchBestSellerProducts = () => async (dispatch: AppDispatch) => {
  try {
    dispatch(
      dashboardSlice.actions.setLoading({
        type: "bestSellerProducts",
        loading: true,
      })
    );
    const res = await api.get(BEST_SELLER_PRODUCTS);
    if (res) {
      dispatch(dashboardSlice.actions.setBestSellerProducts(res.data.data));
    }
  } catch {
    dispatch(
      dashboardSlice.actions.setError("Gagal mendapatkan produk terlaris.")
    );
  } finally {
    dispatch(
      dashboardSlice.actions.setLoading({
        type: "bestSellerProducts",
        loading: false,
      })
    );
  }
};

export const fetchShopBalance = () => async (dispatch: AppDispatch) => {
  try {
    dispatch(
      dashboardSlice.actions.setLoading({ type: "shopBalance", loading: true })
    );
    const res = await api.get(SHOP_BALANCE);
    if (res) {
      dispatch(dashboardSlice.actions.setShopBalance(res.data.data.balance));
    }
  } catch {
    dispatch(dashboardSlice.actions.setError("Gagal mendapatkan saldo toko."));
  } finally {
    dispatch(
      dashboardSlice.actions.setLoading({ type: "shopBalance", loading: false })
    );
  }
};

export const fetchCustomerStatsOverview =
  (filter: "topSpender" | "demography" = "topSpender") =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(
        dashboardSlice.actions.setLoading({
          type: "customerStatsOverview",
          loading: true,
        })
      );
      const res = await api.get(CUSTOMER_STATS_OVERVIEW(filter));
      if (res) {
        dispatch(
          dashboardSlice.actions.setCustomerStatsOverview(res.data.data)
        );
      }
    } catch {
      dispatch(
        dashboardSlice.actions.setError(
          "Gagal mendapatkan data untuk stats customer."
        )
      );
    } finally {
      dispatch(
        dashboardSlice.actions.setLoading({
          type: "customerStatsOverview",
          loading: false,
        })
      );
    }
  };

export const fetchCustomerStatsList =
  (page: number, filter: "topSpender" | "demography" = "topSpender") =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(
        dashboardSlice.actions.setLoading({
          type: "customerStatsList",
          loading: true,
        })
      );
      const res = await api.get(CUSTOMER_STATS_LIST(page, filter));
      if (res) {
        dispatch(dashboardSlice.actions.setCustomerStatsList(res.data.data));
      }
    } catch {
      dispatch(
        dashboardSlice.actions.setError(
          "Gagal mendapatkan data untuk stats customer."
        )
      );
    } finally {
      dispatch(
        dashboardSlice.actions.setLoading({
          type: "customerStatsList",
          loading: false,
        })
      );
    }
  };

export const downloadCustomerStats =
  (filter: "topSpender" | "demography" = "topSpender") =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(
        dashboardSlice.actions.setLoading({
          type: "customerStatsDownload",
          loading: true,
        })
      );
      const res = await api.get(DOWNLOAD_CUSTOMER_STATS(filter));
      if (res) {
        const blob = new Blob([res.data], { type: "application/csv" });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `customer-stats-${filter}.csv`;
        a.click();
      }
    } catch {
      dispatch(
        dashboardSlice.actions.setError(
          "Gagal mengunduh data customer, coba lagi nanti."
        )
      );
    } finally {
      dispatch(
        dashboardSlice.actions.setLoading({
          type: "customerStatsDownload",
          loading: false,
        })
      );
    }
  };

export default dashboardSlice.reducer;
