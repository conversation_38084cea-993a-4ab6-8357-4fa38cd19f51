import { ProductItem, ProductsResponse } from "@/utils/types/online-shop";
import { createSlice } from "@reduxjs/toolkit";
import api from "@/lib/ecommerce";
import {
  GET_PRODUCTS,
  PRODUCTS_URL,
  UPDATE_PRODUCT_STATUS,
} from "@/utils/api-routes";
import { AxiosResponse } from "axios";
import { toast } from "sonner";
import { SortFilterValue } from "@/utils/constants";
import { AppDispatch, RootState } from "@/store";

interface ProductListState {
  products: ProductItem[];
  selectedProducts: number[];
  status: "all" | "active" | "deactive";
  order: SortFilterValue;
  loading: boolean;
  statusLoading: boolean;
}

const initialState: ProductListState = {
  products: [],
  selectedProducts: [],
  status: "all",
  order: "desc",
  loading: false,
  statusLoading: false,
};

const productListSlice = createSlice({
  name: "productList",
  initialState,
  reducers: {
    setProducts(state, action) {
      state.products = action.payload;
    },
    setSelectedProducts(state, action) {
      state.selectedProducts = action.payload;
    },
    setStatus(state, action) {
      state.status = action.payload;
    },
    setOrder(state, action) {
      state.order = action.payload;
    },
    setLoading(state, action) {
      state.loading = action.payload;
    },
    setStatusLoading(state, action) {
      state.statusLoading = action.payload;
    },
  },
});

export const fetchProducts =
  () => async (dispatch: AppDispatch, getState: () => RootState) => {
    const status =
      getState().productList.status === "all"
        ? undefined
        : getState().productList.status === "active"
        ? true
        : false;
    const order = getState().productList.order;
    try {
      dispatch(productListSlice.actions.setSelectedProducts([]));
      dispatch(productListSlice.actions.setLoading(true));
      const res: AxiosResponse<ProductsResponse> = await api.get(
        GET_PRODUCTS(status, order)
      );
      if (res.data.data) {
        dispatch(productListSlice.actions.setProducts(res.data.data.products));
      }
    } catch (error: unknown) {
      toast.error(
        "Gagal mendapatkan daftar produk. Silahkan muat ulang halaman dan coba lagi."
      );
    } finally {
      dispatch(productListSlice.actions.setLoading(false));
    }
  };

export const updateProductStatus =
  (id: number, status: boolean) => async (dispatch: AppDispatch) => {
    try {
      dispatch(productListSlice.actions.setStatusLoading(true));
      await api.patch(UPDATE_PRODUCT_STATUS(id), { status });
      toast.success("Status produk berhasil diubah.");
      return Promise.resolve(null);
    } catch (error: unknown) {
      toast.error(
        "Gagal mengubah status produk. Silahkan muat ulang halaman dan coba lagi."
      );
      return Promise.reject(error);
    } finally {
      dispatch(productListSlice.actions.setStatusLoading(false));
    }
  };

export const deleteProducts =
  () => async (dispatch: AppDispatch, getState: () => RootState) => {
    const products = getState().productList.selectedProducts;
    try {
      dispatch(productListSlice.actions.setLoading(true));
      if (products.length > 0) {
        await api.delete(PRODUCTS_URL, {
          data: { ids: products },
        });
        toast.success("Produk berhasil dihapus.");
        dispatch(fetchProducts());
        return Promise.resolve(null);
      } else {
        toast.warning("Tidak ada data yang dapat dihapus.");
      }
    } catch (error: unknown) {
      toast.error(
        "Gagal menghapus produk. Silahkan muat ulang halaman dan coba lagi."
      );
      return Promise.reject(error);
    } finally {
      dispatch(productListSlice.actions.setLoading(false));
    }
  };

export const { setSelectedProducts, setStatus, setOrder } =
  productListSlice.actions;

export default productListSlice.reducer;
