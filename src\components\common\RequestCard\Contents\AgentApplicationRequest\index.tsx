import { RequestCardData, RequestType } from "@/utils/types/request";
import { Divider, Flex, Text, Button } from "@mantine/core";
import RequestCardHeader from "../../RequestCardHeader";
import { useMediaQuery } from "@mantine/hooks";
import { useState } from "react";
import ReviewStatus from "@/components/common/ReviewStatus";
import RequestModal from "@/components/common/RequestModal";
import { RootState } from "@/store";
import { useSelector } from "react-redux";

const AgentApplicationRequestCard = ({ ...request }: RequestCardData) => {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [openedModal, setOpenedModal] = useState(false);
  const { role: userRole } = useSelector((state: RootState) => state.auth);

  const level = request.metadatas[0].requestStep || 0;

  const levelLabel = () => {
    switch (level) {
      case 1:
        return "Mencapai tahap 1: Review Aplikasi Agent Baru";
      case 2:
        return "Mencapai tahap 2: Aplikasi Di-review Admin Hotto";
      case 3:
        return "Konfirmasi Pembayaran";
      default:
        return "-";
    }
  };

  const showViewButton = (): boolean => {
    if (
      (userRole === "admin" && level === 2) ||
      (userRole === "distributor" && (level === 1 || level === 3))
    ) {
      if (request.metadatas[0].status === "pending") {
        return true;
      }
    }
    return false;
  };

  return (
    <Flex direction={"column"}>
      <RequestModal
        opened={openedModal}
        onClose={() => setOpenedModal(false)}
        requestId={request.id}
        type={RequestType.Application}
        status={"accept"} // set as accept, can be reject via the modal
        drawerSize="90vh"
        applicationStep={level}
        role={"Agent"}
      />
      <RequestCardHeader
        createdAt={request.createdAt}
        leftText={`Pendaftaran Agent Baru! ${levelLabel()}`}
      />
      <Divider />
      {request.metadatas[0] ? (
        <Flex
          direction={isMobile ? "column" : "row"}
          align={isMobile ? "flex-start" : "center"}
          justify={"space-between"}
          p={"lg"}
          gap={"lg"}
        >
          <Flex direction={"column"} gap={"xs"}>
            <Text
              fw={500}
            >{`Nama Agent: ${request.metadatas[0].oldValue}`}</Text>
          </Flex>

          <Flex w={isMobile ? "100%" : "fit-content"} justify={"flex-end"}>
            {showViewButton() && (
              <Button onClick={() => setOpenedModal(true)}>View</Button>
            )}
            {(request.metadatas[0].status === "accept" ||
              request.metadatas[0].status === "reject") && (
              <ReviewStatus status={`${request.metadatas[0].status}`} />
            )}
          </Flex>
        </Flex>
      ) : (
        <Text c={"text-light"} p={"lg"} fw={500}>
          Tidak ada detail data
        </Text>
      )}
    </Flex>
  );
};

export default AgentApplicationRequestCard;
