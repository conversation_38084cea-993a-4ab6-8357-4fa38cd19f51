import Icon from "@/components/common/Icon";
import useBreakpoints from "@/hooks/useBreakpoints";
import { formatDateToString } from "@/utils/common-functions";
import { NotificationType } from "@/utils/types/notification";
import { Flex, Spoiler, Text } from "@mantine/core";
import Link from "next/link";

interface BroadcastNotificationCardContentProps {
  title: string;
  type: NotificationType;
  description: string;
  documents?: string[];
  date: string;
}

const BroadcastNotificationCardContent = ({
  title,
  type,
  description,
  documents = [],
  date,
}: BroadcastNotificationCardContentProps) => {
  const { mobile } = useBreakpoints();

  return (
    <Flex
      direction={mobile ? "column" : "row"}
      justify={"space-between"}
      gap={mobile ? "xs" : "md"}
      p={"md"}
    >
      <Flex
        gap={"md"}
        justify={"flex-start"}
        align={"flex-start"}
        w={"100%"}
        style={{
          overflow: "hidden",
        }}
      >
        <Icon
          icon={`broadcast/${
            type === NotificationType.WarningLetter ? "warning" : "announce"
          }`}
          size={20}
        />
        <Flex direction={"column"} w={"100%"}>
          <Spoiler
            maxHeight={mobile ? 48 : 84}
            showLabel="See all"
            hideLabel="See less"
            pb={"xs"}
            variant="subtle"
          >
            <Text fw={500}>{title}</Text>
            <Text size="sm" c={"text-light"}>
              {description}
            </Text>
            {documents &&
              documents.length > 0 &&
              documents.map((doc, index) => (
                <Flex gap={"xs"} align={"center"} mt={"xs"} key={index}>
                  <Icon size={20} icon="info/document-fill" />
                  <Text
                    size="sm"
                    c={"black"}
                    fw={500}
                    lineClamp={1}
                    maw={"100%"}
                    td={"underline"}
                    component="a"
                    href={doc}
                    target="_blank"
                  >
                    {doc}
                  </Text>
                </Flex>
              ))}
          </Spoiler>
        </Flex>
      </Flex>
      <Flex
        direction={"column"}
        ta={mobile ? "left" : "right"}
        align={mobile ? "flex-start" : "flex-end"}
      >
        <Text fw={300} c={"text-light"} size="sm">
          Sent
        </Text>
        <Text fw={500} c={"text-light"} size="sm">
          {formatDateToString(date, "number")}
        </Text>
      </Flex>
    </Flex>
  );
};

export default BroadcastNotificationCardContent;
