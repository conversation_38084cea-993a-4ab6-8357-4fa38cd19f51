import RequestCard from "@/components/common/RequestCard";
import { RootState } from "@/store";
import { RequestType } from "@/utils/types/request";
import { Divider, Flex, Text, Title } from "@mantine/core";
import { useSelector } from "react-redux";

const RequestReceived = () => {
  const { inRequests } = useSelector((state: RootState) => state.request);

  return (
    <Flex direction={"column"} w={"100%"} gap={"md"} pb={"md"}>
      <Title order={5}>Request Diterima</Title>
      <Divider />
      {inRequests.length > 0 ? (
        <Flex direction={"column"} gap={"xs"}>
          {inRequests.map((value, index) => (
            <RequestCard key={index} {...value} />
          ))}
        </Flex>
      ) : (
        <Text c={"text-light"}>Belum ada request diterima</Text>
      )}
    </Flex>
  );
};

export default RequestReceived;
