import RequestCardHeader from "@/components/common/RequestCard/RequestCardHeader";
import useBreakpoints from "@/hooks/useBreakpoints";
import { Divider, Flex } from "@mantine/core";
import AgentRequestDataChangeContent from "./AgentRequestDataChangeContent";
import React from "react";
import { NotificationMetadata } from "@/utils/types/notification";

interface AgentDataChangeNotificationCardContentProps {
  metadata: NotificationMetadata[];
  updatedAt: string;
  createdAt: string;
  sourceAgent: string;
  reason?: string;
}

const AgentDataChangeNotificationCardContent = ({
  metadata,
  updatedAt,
  createdAt,
  sourceAgent = "-",
  reason = "-",
}: AgentDataChangeNotificationCardContentProps) => {
  const { mobile } = useBreakpoints();

  return (
    <Flex direction={"column"}>
      <RequestCardHeader
        leftText={`Agen ${sourceAgent} ingin mengubah data`}
        rightText="Last update request"
        updatedAt={updatedAt}
        createdAt={createdAt}
        rightItalic={false}
        size="sm"
      />
      <Divider />
      {metadata.map((data, index) => {
        return (
          <React.Fragment key={data.field}>
            {index > 0 && mobile && <Divider variant="dashed" />}
            <AgentRequestDataChangeContent
              key={data.field}
              field={data.field}
              oldValue={data.oldValue}
              newValue={data.newValue}
              reason={reason}
              requestId={data.id || 0}
              status={data.status}
              submittedComplaint={data.complain}
            />
          </React.Fragment>
        );
      })}
    </Flex>
  );
};

export default AgentDataChangeNotificationCardContent;
