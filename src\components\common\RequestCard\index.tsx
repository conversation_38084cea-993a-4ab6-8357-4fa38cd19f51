import { Paper } from "@mantine/core";
import CheckLinkRequestContent from "./Contents/CheckLink";
import DataChangeRequestContent from "./Contents/DataChangeRequest";
import AgentStatusReactivationContent from "./Contents/AgentStatusReactivation";
import ForceDataChangeRequestContent from "./Contents/ForceDataChangeRequest";
import AgentApplicationRequestCard from "./Contents/AgentApplicationRequest";
import { RequestCardData, RequestType } from "@/utils/types/request";
import DistributorCheckLinkRequestContent from "./Contents/DistributorCheckLink";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import DistributorAgentTerminationRequestCard from "./Contents/DistributorAgentTermination";
import DistributorDataChangeRequestContent from "./Contents/DistributorDataChangeRequest";
import DistributorApplicationRequestCard from "./Contents/DistributorApplicationRequest";
import AgentReapplicationRequestCard from "./Contents/AgentReapplicationRequest";
import ChooseNewDistributorRequestCard from "./Contents/ChooseNewDistributor";
import AgentTerminationRequestCard from "./Contents/AgentTermination";

const RequestCard = ({ ...request }: RequestCardData) => {
  const { role } = useSelector((state: RootState) => state.auth);

  const renderRequestCardContent = () => {
    switch (request.requestType) {
      case RequestType.DistributorCheckMarketplaceLinks:
        return <DistributorCheckLinkRequestContent {...request} />;
      case RequestType.AdminCheckLinks:
        return <CheckLinkRequestContent {...request} />;
      case RequestType.DataChange:
        if (role === "admin") {
          return <DataChangeRequestContent {...request} />;
        } else {
          return <DistributorDataChangeRequestContent {...request} />;
        }
      case RequestType.ForceDataChange:
        return <ForceDataChangeRequestContent {...request} />;
      case RequestType.Reactivation:
        return <AgentStatusReactivationContent {...request} />;
      case RequestType.Termination:
        if (role === "admin") {
          return <AgentTerminationRequestCard {...request} />;
        } else {
          return <DistributorAgentTerminationRequestCard {...request} />;
        }

      case RequestType.Application: // agent or distributor
        return request.requesterRole === "Agent" ? (
          <AgentApplicationRequestCard {...request} />
        ) : (
          <DistributorApplicationRequestCard {...request} />
        );
      case RequestType.ChangeDistributor:
        if (role === "distributor") {
          return <AgentReapplicationRequestCard {...request} />;
        } else if (role === "agent") {
          return <ChooseNewDistributorRequestCard {...request} />;
        }
      default:
        return null;
    }
  };

  return (
    <Paper radius="md" w={"100%"} bg="bg">
      {renderRequestCardContent()}
    </Paper>
  );
};

export default RequestCard;
