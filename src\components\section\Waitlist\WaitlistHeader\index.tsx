import { Button, <PERSON>lex, <PERSON>ack, Text } from "@mantine/core";
import { useState } from "react";
import ModifyKuotaModal from "./ModifyKuotaModal";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
interface WaitlistHeaderProps {
  role: "agent" | "distributor";
}

const WaitlistHeader = ({ role }: WaitlistHeaderProps) => {
  const [openModal, setOpenModal] = useState(false);
  const { agentWaitlist, distributorWaitlist } = useSelector(
    (state: RootState) => state.waitlist
  );

  const kuota =
    role === "agent" ? agentWaitlist?.kuota : distributorWaitlist?.kuota;

  const radius =
    role === "agent" ? agentWaitlist?.radius : distributorWaitlist?.radius;

  return (
    <Stack gap={"sm"}>
      <ModifyKuotaModal
        opened={openModal}
        onClose={() => setOpenModal(false)}
        role={role}
        currentKuota={kuota || 0}
        currentRadius={radius || 0}
      />
      <Flex gap={"2px"}>
        <Text fw={500}>
          Kuota {role}: {`${kuota || 0}`}
        </Text>
        <Text c={"text-light"}>per {radius || 0}km</Text>
      </Flex>
      <Button
        w={"fit-content"}
        onClick={() => setOpenModal(true)}
      >{`Tambah Kuota ${role}`}</Button>
    </Stack>
  );
};

export default WaitlistHeader;
