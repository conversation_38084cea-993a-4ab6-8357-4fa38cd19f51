import { Stack, Text, Title, StackProps } from "@mantine/core";

interface PlaceholderProps extends StackProps {
  label: string;
  description?: string;
  empty?: boolean;
}

const Placeholder: React.FC<PlaceholderProps> = ({
  label,
  description = "",
  empty,
  ...props
}) => {
  return (
    <Stack ta="center" gap="sm" {...props}>
      <Title order={2}>
        Oops,{" "}
        {empty
          ? `data ${label} kosong!`
          : "data yang anda cari tidak ditemukan!"}
      </Title>
      <Text fw={500} c="text">
        {empty ? description : "Mohon gunakan kata kunci lain"}
      </Text>
    </Stack>
  );
};

export default Placeholder;
