import { HistoriesData } from "@/utils/types/distributor-agent";
import { Divider, Flex, Paper, Text } from "@mantine/core";
import React from "react";
import OrderHistory from "../../AdminAgentDistributor/OrderHistory";

interface RequestOrderHistoriesProps {
  orderHistories: HistoriesData[];
}

const RequestOrderHistories = ({
  orderHistories,
}: RequestOrderHistoriesProps) => {
  return (
    <Paper radius={"8px"} bg={"bg"} p={"md"} w={"100%"} h={"fit-content"}>
      <Flex direction={"column"} w={"100%"} gap={"sm"}>
        <Text fw={600}>Order History</Text>
        {orderHistories &&
          orderHistories.slice(0, 2).map((log, i) => (
            <React.Fragment key={i}>
              <OrderHistory history={log} />
              {i === 0 && orderHistories.length > 1 && <Divider />}
            </React.Fragment>
          ))}
        {(!orderHistories || orderHistories.length === 0) && (
          <Text ta={"center"} c={"text-light"} py={"md"}>
            Belum ada pembelian
          </Text>
        )}
      </Flex>
    </Paper>
  );
};

export default RequestOrderHistories;
