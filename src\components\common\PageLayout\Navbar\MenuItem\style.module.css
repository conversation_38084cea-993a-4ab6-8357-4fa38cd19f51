.root {
  color: white;
  border-radius: var(--mantine-radius-md);
  padding: var(--mantine-spacing-md);

  &:hover,
  &[data-active] {
    background-color: var(--mantine-primary-color-filled);
  }

  &[data-flip-icon] {
    & svg {
      -webkit-transform: scaleX(-1);
      transform: scaleX(-1);
    }
  }
}

.label {
  font-weight: 500;
  font-size: var(--mantine-h4-font-size);
}

.root[data-nested] .label {
  margin-left: 2.2rem;
}
