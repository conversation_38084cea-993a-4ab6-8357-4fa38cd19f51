import api from '@/lib/axios'
import { GET_AGENT_LIST_IN_DISTRIBUTOR } from '@/utils/api-routes'
import { createSlice } from '@reduxjs/toolkit'
import { toast } from 'sonner'
import { AppDispatch } from '..'
import {
 AgentListData,
 AgentListResponse,
} from '@/utils/types/distributor-agent'

const adminAgentListInDistributorSlice = createSlice({
 name: 'adminAgentListInDistributor',
 initialState: {
  loading: false,
  error: undefined,
  data: {} as AgentListData,
 },
 reducers: {
  setError(state, action) {
   state.error = action.payload
   toast.error(action.payload)
  },
  setLoading(state, action) {
   state.loading = action.payload
  },
  setData(state, action) {
   state.data = action.payload
  },
 },
})

export const fetchAgentListInDistributor =
 (uoc: string) => async (dispatch: AppDispatch) => {
  try {
   dispatch(adminAgentListInDistributorSlice.actions.setLoading(true))
   const res: { data: AgentListResponse } = await api.get(
    GET_AGENT_LIST_IN_DISTRIBUTOR(uoc)
   )
   if (res.data.status_code === 200 && res.data.data) {
    dispatch(adminAgentListInDistributorSlice.actions.setData(res.data.data))
   } else {
    dispatch(
     adminAgentListInDistributorSlice.actions.setError(res.data.message)
    )
   }
  } catch {
   dispatch(
    adminAgentListInDistributorSlice.actions.setError('Failed get agent list.')
   )
  } finally {
   dispatch(adminAgentListInDistributorSlice.actions.setLoading(false))
  }
 }

export default adminAgentListInDistributorSlice.reducer
