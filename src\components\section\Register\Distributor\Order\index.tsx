import {
  DISTRIBUTOR_REGISTRATION_DATA_VAR,
  MAX_WIDTH,
} from "@/utils/constants";
import { Stack, Title, Text, Box, Button } from "@mantine/core";
import React, { useEffect, useMemo, useState } from "react";
import DetailCard from "./DetailCard";
import ProductSelection from "./ProductSelection";
import ShippingSelection from "./ShippingSelection";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import dynamic from "next/dynamic";
import {
  setOrderProducts,
  setOrderShippingOption,
} from "@/store/slices/distributorRegisterSlice";
import ButtonGroup from "@/components/common/ButtonGroup";
import { isEmpty } from "@/utils/common-functions";
import { useDisclosure } from "@mantine/hooks";
import { RegisterData } from "@/utils/types/distributor-register";
const ConfirmationModal = dynamic(() => import("./ConfirmationModal"));

const DistributorOrder = () => {
  const dispacth: AppDispatch = useDispatch();
  const {
    order: { products, shippingOption },
  } = useSelector((state: RootState) => state.distributorRegister);
  const [info, setInfo] = useState<RegisterData | null>(null);
  const [opened, handlers] = useDisclosure(false);

  const disabled = useMemo(
    () => isEmpty(products) || isEmpty(shippingOption) || isEmpty(info?.id),
    [products.length, shippingOption]
  );

  useEffect(() => {
    try {
      const storedInfo = localStorage.getItem(
        DISTRIBUTOR_REGISTRATION_DATA_VAR
      );
      const info: RegisterData = storedInfo ? JSON.parse(storedInfo) : null;

      if (isEmpty(info)) {
        window.location.replace("/register/distributor");
        return;
      }

      setInfo(info);
    } catch (error) {
      console.error("Error parsing registration data:", error);
      window.location.replace("/register/distributor");
    }
  }, []);

  return (
    <>
      <Stack maw={MAX_WIDTH} mx="auto" gap="xl3">
        <Box ta="center">
          <Title>Masukkan order pertamamu!</Title>
          <Text mt="lg">
            Langkah terakhir di proses registrasi! Silakan masukkan informasi
            produk yang ingin Anda beli sebagai pesanan pertamamu.
          </Text>
        </Box>
        {!isEmpty(info) && <DetailCard data={info!} />}
        <ProductSelection
          onChange={(value) => {
            dispacth(setOrderProducts(value));
            dispacth(setOrderShippingOption(""));
          }}
        />
        {!isEmpty(info) && (
          <ShippingSelection
            user={info!}
            onChange={(value) => dispacth(setOrderShippingOption(value))}
          />
        )}
        <ButtonGroup mt="xl" justify="center" w="100%">
          <Button
            disabled={disabled || shippingOption === "Expedisi Lain"}
            onClick={handlers.open}
          >
            Submit
          </Button>
        </ButtonGroup>
      </Stack>
      <ConfirmationModal
        userId={info?.uniqueUserCode}
        opened={opened}
        onClose={handlers.close}
      />
    </>
  );
};

export default DistributorOrder;
