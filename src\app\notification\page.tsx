"use client";
import dynamic from "next/dynamic";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";

import PageLayout from "@/components/common/PageLayout";
import TerminatedDistributorWarning from "@/components/common/TerminatedDistributorWarning";
import { withAuth } from "@/utils/withAuth";
import { RootState } from "@/store";
import { Text } from "@mantine/core";

const NotificationList = dynamic(
  () => import("@/components/section/NotificationList"),
  { ssr: false, loading: () => <Text>Loading notification...</Text> }
);

const NotificationPage = () => {
  const [showWarning, setShowWarning] = useState(false);
  const { role } = useSelector((state: RootState) => state.auth);
  const { notificationMetadatas } = useSelector(
    (state: RootState) => state.distributorNotification
  );

  useEffect(() => {
    if (
      role === "agent" &&
      notificationMetadatas?.some(
        (metadata) => metadata.name === "ForceTerminated"
      )
    ) {
      setShowWarning(true);
    }
  }, [role, notificationMetadatas]);

  return (
    <PageLayout
      title="Notifikasi"
      headerContent={
        showWarning && (
          <TerminatedDistributorWarning
            onClose={() => setShowWarning(false)}
            requestId={0}
          />
        )
      }
    >
      <NotificationList />
    </PageLayout>
  );
};

export default withAuth(NotificationPage);
