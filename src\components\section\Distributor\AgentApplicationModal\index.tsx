"use client";

import useBreakpoints from "@/hooks/useBreakpoints";
import { <PERSON><PERSON>, <PERSON>dalProps, Drawer } from "@mantine/core";
import { SubmitReceiveAgentPayload } from "@/utils/types/distributor-agent";
import ModalContent from "./ModalContent";

export interface AgentApplicationFormProps extends SubmitReceiveAgentPayload {
  confirmation: boolean;
}

export interface AgentApplicationModalProps {
  opened: ModalProps["opened"];
  onClose: ModalProps["onClose"];
  onSubmit: (data: SubmitReceiveAgentPayload) => void;
}

const AgentApplicationModal: React.FC<AgentApplicationModalProps> = ({
  opened,
  onClose,
  onSubmit,
}) => {
  const { mobile } = useBreakpoints();

  return !mobile ? (
    <Modal opened={opened} onClose={onClose} centered>
      <ModalContent onClose={onClose} onSubmit={onSubmit} />
    </Modal>
  ) : (
    <Drawer
      opened={opened}
      onClose={onClose}
      h="fit-content"
      radius={"8px 8px 0 0"}
      position="bottom">
      <ModalContent onClose={onClose} onSubmit={onSubmit} />
    </Drawer>
  );
};

export default AgentApplicationModal;
