import { Stack, Center, Loader } from "@mantine/core";
import dynamic from "next/dynamic";
import OverviewStatisticFilters from "./Filters";
import SectionHeader from "@/components/common/SectionHeader";
import { getTodayDateTime } from "@/utils/common-functions";
import useOverviewFilters from "@/hooks/useOverviewFilters";
const SalesStatistics = dynamic(() => import("./Statistics/Sales"), {
  ssr: false,
  loading: () => Loading,
});
const CustomerStatistics = dynamic(() => import("./Statistics/Customers"), {
  ssr: false,
  loading: () => Loading,
});

const Loading = (
  <Center>
    <Loader my="5em" />
  </Center>
);

const OverviewSection = () => {
  const { stats } = useOverviewFilters();

  return (
    <Stack pb="lg">
      <SectionHeader
        title="Statistik"
        description={`Update terakhir: ${getTodayDateTime()}`}
      />
      <OverviewStatisticFilters />
      {stats === "sales" && <SalesStatistics />}
      {stats === "demo" && <CustomerStatistics />}
    </Stack>
  );
};

export default OverviewSection;
