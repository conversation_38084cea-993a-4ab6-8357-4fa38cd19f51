import { Drawer, Flex, Input, Modal, Text, Button } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import { capitalize } from "@/utils/common-functions";
import InputGroup from "@/components/common/InputGroup";
import ButtonGroup from "@/components/common/ButtonGroup";
import Icon from "@/components/common/Icon";
import { createComplaint } from "@/store/slices/distributorNotificationSlice";

interface ComplaintModalProps {
  opened: boolean;
  onClose: () => void;
  requestId: number;
  sourceAgent: string;
  field?: string;
  oldValue?: string;
  newValue?: string;
  reason?: string;
  modalSize?: string;
  drawerSize?: string;
}

const ComplaintModal = ({
  opened,
  onClose,
  requestId,
  sourceAgent,
  field = "-",
  oldValue = "-",
  newValue = "-",
  reason = "-",
  modalSize = "lg",
  drawerSize = "lg",
}: ComplaintModalProps) => {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const dispatch: AppDispatch = useDispatch();

  const [complaint, setComplaint] = useState("");
  useEffect(() => {
    setComplaint("");
  }, [opened]);

  const handleClose = () => {
    onClose();
    setComplaint("");
  };

  const handleSubmit = () => {
    dispatch(createComplaint(complaint, requestId, handleClose));
  };

  const renderModalContent = () => (
    <Flex
      direction="column"
      align="center"
      justify="center"
      gap="sm"
      p={isMobile ? 0 : "lg"}
    >
      <h2 style={{ textAlign: "center" }}>Ajukan Complaint</h2>
      <Text c={"text-light"} fw={500} ta={"center"} maw={"80%"}>
        Complaint akan menjadi pertimbangan untuk pembuatan keputusan Admin
        Hotto
      </Text>
      <Flex direction={"column"} justify={"center"} align={"center"}>
        <Text fw={600}>{`Agent '${
          sourceAgent || "-"
        }' ingin mengubah data`}</Text>
        <Flex wrap={"wrap"} gap={"xs"} align={"center"} justify={"center"}>
          <Text>{`${capitalize(field)}:`}</Text>
          <Text>{`${oldValue}`}</Text>
          <Icon icon="arrow/right" size={12} />
          <Text>{`${newValue}`}</Text>
        </Flex>
        <Text fw={400} c={"text-light"}>{`Alasan: ${reason}`}</Text>
      </Flex>

      <InputGroup label={"Complaint"} mandatory>
        <Input
          type="text"
          placeholder={
            "Jelaskan mengapa Anda tidak setuju dengan update data Agent ini"
          }
          value={complaint}
          onChange={(e) => setComplaint(e.target.value)}
        />
      </InputGroup>

      <ButtonGroup mt={isMobile ? 0 : "xl"}>
        <Button variant="subtle" onClick={handleClose}>
          Kembali
        </Button>
        <Button onClick={handleSubmit} disabled={!complaint}>
          Kirim
        </Button>
      </ButtonGroup>
    </Flex>
  );

  return isMobile ? (
    <Drawer
      opened={opened}
      onClose={handleClose}
      radius="8px 8px 0 0"
      size={drawerSize}
      position="bottom"
    >
      {opened && renderModalContent()}
    </Drawer>
  ) : (
    <Modal
      opened={opened}
      onClose={onClose}
      radius={8}
      size={modalSize}
      centered
    >
      {opened && renderModalContent()}
    </Modal>
  );
};

export default ComplaintModal;
