import { AgentCardDataSimple } from "@/utils/types";
import { Flex, Text } from "@mantine/core";

interface AgentDistributorSelectOptionProps {
  datas: AgentCardDataSimple[];
  checked?: boolean;
  option: {
    label: string;
    value: string;
  };
}
const AgentDistribtuorSelectOption = ({
  datas: datas,
  checked = false,
  option,
}: AgentDistributorSelectOptionProps) => {
  return (
    <Flex direction={"column"}>
      <Text c={checked ? "primary" : "black"}>{option.label}</Text>
      <Flex c={"text-light"} gap={"4px"}>
        {datas.find((item) => item.uoc === option.value)?.email && (
          <Text size="xs">
            {datas.find((item) => item.uoc === option.value)?.email}
          </Text>
        )}
        {datas.find((item) => item.uoc === option.value)?.adminPhone && (
          <Text size="xs">
            {"· " + datas.find((item) => item.uoc === option.value)?.adminPhone}
          </Text>
        )}
        {datas.find((item) => item.uoc === option.value)?.ownerPhone && (
          <Text size="xs">
            {"· " + datas.find((item) => item.uoc === option.value)?.ownerPhone}
          </Text>
        )}
      </Flex>
    </Flex>
  );
};

export default AgentDistribtuorSelectOption;
