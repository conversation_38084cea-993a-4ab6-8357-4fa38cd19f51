import ActivationStatus from "@/components/common/ActivationStatus";
import BackButton from "@/components/common/BackButton";
import Icon from "@/components/common/Icon";
import { AgentDistributorDetailsData } from "@/utils/types/distributor-agent";
import {
  Stack,
  Text,
  Divider,
  ActionIcon,
  Group,
  Flex,
  SimpleGrid,
} from "@mantine/core";
import SimpleInfo from "../../SimpleInfo";
import { useDisclosure } from "@mantine/hooks";
import ForceEditModal from "../../ForceEditModal";
import useBreakpoints from "@/hooks/useBreakpoints";
import { ROLE_VAR } from "@/utils/constants";
import PHKDistributor from "../../PHKDistributor";
import { toast } from "sonner";

export interface BasicInfoProps {
  data: AgentDistributorDetailsData;
}

const BasicInfo = ({ data }: BasicInfoProps) => {
  const [opened, { open, close }] = useDisclosure(false);
  const [isDeleting, { open: openDelete, close: closeDelete }] =
    useDisclosure(false);
  const role = localStorage.getItem(ROLE_VAR) || "";
  const { mobile } = useBreakpoints();

  const handleDelete = () => {
    if (data.roleName === "distributor") {
      openDelete();
    } else {
      toast.error("Only distributor can be terminated.");
    }
  };

  const ActionButtons = role === "admin" && (
    <Flex gap="sm">
      {data.roleName === "distributor" && (
        <ActionIcon
          variant="transparent"
          aria-label="Delete"
          onClick={handleDelete}
        >
          <Icon icon="action/delete" />
        </ActionIcon>
      )}
      <ActionIcon variant="transparent" aria-label="Edit" onClick={open}>
        <Icon icon="action/edit" />
      </ActionIcon>
    </Flex>
  );

  return (
    <Stack gap="xs">
      <PHKDistributor
        opened={isDeleting}
        onClose={closeDelete}
        agentDetails={data}
      />
      <ForceEditModal opened={opened} onClose={close} agentDetails={data} />
      <Group justify="space-between">
        <Group>
          <BackButton />
          <Text size="xl" fw={600}>
            {data.name}
          </Text>
          <ActivationStatus status={data.status} withBorder />
        </Group>
        {!mobile && ActionButtons}
      </Group>
      <Divider size="sm" />
      <SimpleGrid cols={{ base: 1, sm: 2 }}>
        <Stack>
          {data.roleName !== "agent" && (
            <>
              <Group justify="space-between">
                <SimpleInfo title="NPWP" description={data.npwp} />
                {mobile && ActionButtons}
              </Group>
              <SimpleInfo title="NIK" description={data.nik} />
              <SimpleInfo
                title="KTP"
                description={data.npwp}
                icon="info/location"
              />
            </>
          )}
          <Group justify="space-between">
            <SimpleInfo
              title="Warehouse"
              description={data.warehouseAddress}
              icon="info/location"
            />
            {mobile && data.roleName === "agent" && ActionButtons}
          </Group>
          <SimpleInfo
            title="Email"
            description={data.email}
            icon="info/email"
          />
          <SimpleInfo
            title="Pemilik"
            description={data.ownerPhone}
            icon="info/phone"
          />
          <SimpleInfo
            title="Admin"
            description={data.adminPhone}
            icon="info/phone"
          />
        </Stack>
        <Stack>
          <SimpleInfo
            icon="social/tokopedia"
            description={data.shop?.tokopedia}
          />
          <SimpleInfo icon="social/shopee" description={data.shop?.shopee} />
          <SimpleInfo icon="social/tiktok" description={data.shop?.tiktok} />
          <SimpleInfo icon="info/document" description={data.sk} />
        </Stack>
      </SimpleGrid>
    </Stack>
  );
};

export default BasicInfo;
