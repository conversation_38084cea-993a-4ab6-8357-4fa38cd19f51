import DonutChart from "@/components/common/DonutChart";
import SectionHeader from "@/components/common/SectionHeader";
import useBreakpoints from "@/hooks/useBreakpoints";
import useOverviewFilters from "@/hooks/useOverviewFilters";
import { AppDispatch, RootState } from "@/store";
import { fetchSalesChartData } from "@/store/slices/onlineShopSlices/overviewSlice";
import { formatDate, isEmpty, parseToRupiah } from "@/utils/common-functions";
import { AreaChart } from "@mantine/charts";
import { Box, Center, Skeleton, Text } from "@mantine/core";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import ChartTooltip, { ChartTooltipProps } from "../../ChartTooltip";

const SalesStatisticsChart = ({ slug }: { slug?: string }) => {
  const dispatch: AppDispatch = useDispatch();
  const {
    salesChartData: data,
    salesSummary,
    loading: { salesChart: loading },
  } = useSelector((state: RootState) => state.overview);
  const { time, salesChart } = useOverviewFilters();
  const { mobile } = useBreakpoints();

  const areaChart = salesChart !== "bestSellingProducts";
  const empty = (isEmpty(data) || isEmpty(data.content)) && !loading;

  const getTimeLabel = (): string => {
    return time === "daily"
      ? "Harian"
      : time === "weekly"
      ? "Mingguan"
      : "Bulanan";
  };

  const getStatsLabel = (): string => {
    switch (salesChart) {
      case "sales":
        return "Penjualan";
      case "visitors":
        return "Total Pengunjung";
      case "productSeen":
        return "Produk Dilihat";
      case "orders":
        return "Pesanan";
      case "conversionRate":
        return "Tingkat Konversi";
      case "bestSellingProducts":
        return "Produk Terlaris";
    }
  };

  const CHART_DATA =
    data?.content?.map((item) => ({
      name: areaChart ? formatDate(item.label) : item.label,
      value: item.value,
    })) || [];

  useEffect(() => {
    if (time && salesChart && !isEmpty(salesSummary) && !loading) {
      dispatch(fetchSalesChartData(time, salesChart, slug));
    }
  }, [time, salesChart, isEmpty(salesSummary), slug]);

  return (
    <Box>
      <SectionHeader
        mb="xs"
        title={`Grafik ${getStatsLabel()} ${getTimeLabel()}`}
      />
      <Center>
        {empty ? (
          <Text my={(areaChart ? 175 : 250) / 2}>
            Data grafik belum dapat ditampilkan, silahkan gunakan filter lain.
          </Text>
        ) : (
          <Skeleton height={areaChart ? 175 : "auto"} visible={loading}>
            {areaChart ? (
              <AreaChart
                h={175}
                data={CHART_DATA}
                dataKey="name"
                series={[{ name: "value", color: "primary.6" }]}
                tooltipProps={{
                  content: ({ label, payload }) => (
                    <ChartTooltip
                      title={getStatsLabel()}
                      label={label}
                      payload={payload as ChartTooltipProps["payload"]}
                      formatValueAs={
                        salesChart === "sales" ? "currency" : undefined
                      }
                    />
                  ),
                }}
                curveType="linear"
                gridAxis="none"
                withYAxis={false}
                withDots={false}
                tooltipAnimationDuration={300}
                fillOpacity={0.5}
              />
            ) : (
              <DonutChart
                data={CHART_DATA}
                chartLabel={getStatsLabel()}
                chartDescription={parseToRupiah(data.totalSales)}
                legendLabel={getStatsLabel()}
                legendPosition={mobile ? "bottom" : "right"}
                isPrice
              />
            )}
          </Skeleton>
        )}
      </Center>
    </Box>
  );
};

export default SalesStatisticsChart;
