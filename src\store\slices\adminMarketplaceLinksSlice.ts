import api from "@/lib/axios";
import {
  GET_ISLANDS_WITH_STATUS,
  GET_LINKS_BY_ISLAND,
  SUBMIT_INVALID_LINKS_REASONS_BY_ISLAND,
} from "@/utils/api-routes";
import { createSlice } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { AppDispatch, RootState } from "..";
import {
  AdminRequestLinksByIslandData,
  AdminRequestLinksByIslandResponse,
  AdminSubmitInvalidLinksReasonsData,
  AdminSubmitInvalidLinksReasonsPayload,
} from "@/utils/types/request";
import { AxiosResponse } from "axios";
import {
  IslandsWithStatusData,
  IslandsWithStatusResponse,
} from "@/utils/types";
import { submitInvalidLinksReasonsSchema } from "@/utils/common-functions";
import * as yup from "yup";

export interface AdminMarketplaceLinksState {
  loading: boolean;
  error?: string;
  data: AdminRequestLinksByIslandData[];
  islands: IslandsWithStatusData[];
  invalidLinksReasons: AdminSubmitInvalidLinksReasonsData[];
}

export const initialState: AdminMarketplaceLinksState = {
  loading: false,
  error: undefined,
  data: [],
  islands: [],
  invalidLinksReasons: [],
};

const adminMarketplaceLinksSlice = createSlice({
  name: "adminMarketplaceLinks",
  initialState,
  reducers: {
    setError(state, action) {
      state.error = action.payload;
      toast.error(action.payload);
    },
    setLoading(state, action) {
      state.loading = action.payload;
    },
    setData(state, action) {
      state.data = action.payload;
    },
    setIslands(state, action) {
      state.islands = action.payload;
    },
    setInvalidLinksReasons(state, action) {
      state.invalidLinksReasons = action.payload;
    },
  },
});

export const fetchAllLinks =
  (island: string) => async (dispatch: AppDispatch) => {
    try {
      dispatch(adminMarketplaceLinksSlice.actions.setLoading(true));
      const res: AxiosResponse<AdminRequestLinksByIslandResponse> =
        await api.get(GET_LINKS_BY_ISLAND(island));
      if (res.status === 200 && res.data.data) {
        dispatch(adminMarketplaceLinksSlice.actions.setData(res.data.data));
      } else {
        dispatch(adminMarketplaceLinksSlice.actions.setError(res.data.message));
        toast.error(res.data.message);
      }
    } catch {
      const message = `Failed to get links for ${island} island.`;
      dispatch(adminMarketplaceLinksSlice.actions.setError(message));
      toast.error(message);
    } finally {
      dispatch(adminMarketplaceLinksSlice.actions.setLoading(false));
    }
  };

export const fetchIslandsWithStatus = () => async (dispatch: AppDispatch) => {
  try {
    dispatch(adminMarketplaceLinksSlice.actions.setLoading(true));
    const res: AxiosResponse<IslandsWithStatusResponse> = await api.get(
      GET_ISLANDS_WITH_STATUS
    );
    if (res.status === 200 && res.data.data) {
      dispatch(adminMarketplaceLinksSlice.actions.setIslands(res.data.data));
    } else {
      dispatch(adminMarketplaceLinksSlice.actions.setError(res.data.message));
      toast.error(res.data.message);
    }
  } catch {
    const message = "Failed to get islands.";
    dispatch(adminMarketplaceLinksSlice.actions.setError(message));
    toast.error(message);
  } finally {
    dispatch(adminMarketplaceLinksSlice.actions.setLoading(false));
  }
};

export const setInvalidLinksReasons =
  (data: AdminSubmitInvalidLinksReasonsData, remove?: boolean) =>
  async (dispatch: AppDispatch, getState: () => RootState) => {
    const state = getState();
    const existingLinks = state.adminMarketplaceLinks.invalidLinksReasons;
    const notEmpty = existingLinks && existingLinks.length > 0;
    let newData;
    if (notEmpty) {
      const existingIndex = existingLinks.findIndex(
        (link) => link.id === data.id
      );
      if (existingIndex !== -1) {
        newData = [...existingLinks];
        newData[existingIndex] = data;
      } else {
        newData = [...existingLinks, data];
      }
      if (remove) {
        newData = existingLinks.filter((link) => link.id !== data.id);
      }
    } else {
      newData = [data];
    }
    dispatch(
      adminMarketplaceLinksSlice.actions.setInvalidLinksReasons(newData)
    );
  };

export const resetInvalidLinksReasons = () => async (dispatch: AppDispatch) => {
  dispatch(
    adminMarketplaceLinksSlice.actions.setInvalidLinksReasons(undefined)
  );
};

export const submitInvalidLinksReasons =
  (island: string, data: AdminSubmitInvalidLinksReasonsPayload) =>
  async (dispatch: AppDispatch) => {
    // remove id property before submit
    const dataToSubmit: AdminSubmitInvalidLinksReasonsPayload = {
      invalidLinks: data.invalidLinks.map(({ id, ...rest }) => rest),
    };
    try {
      await submitInvalidLinksReasonsSchema.validate(dataToSubmit, {
        abortEarly: false,
      });
      dispatch(adminMarketplaceLinksSlice.actions.setLoading(true));
      await api.put(
        SUBMIT_INVALID_LINKS_REASONS_BY_ISLAND(island),
        dataToSubmit
      );
      toast.success("Link berhasil di submit untuk " + island);
      dispatch(resetInvalidLinksReasons());
      dispatch(fetchAllLinks(island));
      dispatch(fetchIslandsWithStatus());
    } catch (error) {
      if (error instanceof yup.ValidationError) {
        toast.error(`Validasi gagal: ${error.errors.join(", ")}`);
      } else {
        toast.error("Failed to submit invalid links reasons.");
      }
    } finally {
      dispatch(adminMarketplaceLinksSlice.actions.setLoading(false));
    }
  };

export default adminMarketplaceLinksSlice.reducer;
