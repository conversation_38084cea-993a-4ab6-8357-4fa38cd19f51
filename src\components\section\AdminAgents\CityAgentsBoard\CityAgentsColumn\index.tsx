import { Flex, Text } from "@mantine/core";
import AgentCard from "./AgentCard";
import { AgentCardDataDetailed } from "@/utils/types";
import { useState } from "react";

interface CityAgentsColumnProps {
  name: string;
  count: number;
  agents?: AgentCardDataDetailed[];
}

const CityAgentsColumn = ({
  name,
  count,
  agents = [],
}: CityAgentsColumnProps) => {
  const [isShowAllAgents, setIsShowAllAgents] = useState(false);
  const handleShowAll = () => {
    setIsShowAllAgents(true);
  };
  return (
    <Flex
      className="agent-card-column"
      direction={"column"}
      align={"center"}
      gap={"md"}
      maw={"min(480px, 88vw)"}
      miw={"min(480px, 88vw)"}
    >
      <Flex
        direction={"row"}
        justify={"space-between"}
        gap={"sm"}
        miw={"100%"}
        align={"center"}
      >
        <Text fw={500} size="lg">
          {name}
        </Text>
        <Text fw={500} size="md">
          {count}
        </Text>
      </Flex>
      {agents.slice(0, isShowAllAgents ? agents.length : 2).map((agent) => (
        <AgentCard key={agent.uoc} variant="detailed" data={agent} />
      ))}

      {!isShowAllAgents && count > 2 && (
        <Text className="show-all" onClick={handleShowAll} fw={500}>
          Show All
        </Text>
      )}
    </Flex>
  );
};

export default CityAgentsColumn;
