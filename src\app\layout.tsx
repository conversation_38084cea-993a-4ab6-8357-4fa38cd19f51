import "./globals.css";
import type { Metadata, Viewport } from "next";
import StyledComponentsRegistry from "@/lib/styledComponentsRegistry";
import PageWrapper from "@/components/common/PageWrapper";
import {
  ColorSchemeScript,
  mantineHtmlProps,
  MantineProvider,
} from "@mantine/core";
import { theme } from "@/styles/theme";
import { NuqsAdapter } from "nuqs/adapters/next/app";

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export const metadata: Metadata = {
  title: "Hotto ADMS",
  description: "Hotto Admin Management System",
  icons: {
    icon: "/favicon.ico",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" {...mantineHtmlProps}>
      <head>
        <ColorSchemeScript defaultColorScheme="light" />
      </head>
      <body>
        <StyledComponentsRegistry>
          <MantineProvider defaultColorScheme="light" theme={theme}>
            <NuqsAdapter>
              <PageWrapper>{children}</PageWrapper>
            </NuqsAdapter>
          </MantineProvider>
        </StyledComponentsRegistry>
      </body>
    </html>
  );
}
