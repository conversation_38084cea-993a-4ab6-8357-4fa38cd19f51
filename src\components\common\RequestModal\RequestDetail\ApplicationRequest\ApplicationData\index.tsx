import { NewApplicationData } from "@/utils/types/request";
import {
  Box,
  Button,
  Group,
  Paper,
  Stack,
  Text,
  TextInput,
} from "@mantine/core";
import DataField from "./DataField";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { isEmpty, parseToRupiah } from "@/utils/common-functions";
import { useState } from "react";
import { useAdminRequestContext } from "@/context/AdminRequestContext";
import ButtonGroup from "@/components/common/ButtonGroup";
import { setFirstOrderToUpdate } from "@/store/slices/adminRequestSlice";

interface FieldsProps {
  label: string;
  value?: string | string[];
  isPdf?: boolean;
  hidden?: boolean;
}

type FieldData = NewApplicationData["newAgent"];

const getAgentFields = (data: FieldData) => [
  [
    { label: "NIK", value: data.nik },
    { label: "Nama Agent", value: data.name },
    { label: "<PERSON>ama<PERSON>", value: data.address },
    { label: "Email", value: data.email },
    { label: "Telepon Pribadi", value: data.phone },
    { label: "Telepon Admin", value: data.adminPhone },
  ],
];

const getDistributorDetailFields = (data: FieldData) => [
  [
    { label: "NIK", value: data.nik },
    { label: "NPWP", value: data.npwp },
    { label: "Nama Distributor", value: data.name },
    { label: "Email", value: data.email },
    { label: "Telepon Pribadi", value: data.phone },
    { label: "Telepon Admin", value: data.adminPhone },
    { label: "Alamat KTP", value: data.address },
    { label: "Alamat Warehouse", value: data.warehouseAddress },
    { label: "Link Tokopedia", value: data.shops?.tokopedia },
    { label: "Link Shopee", value: data.shops?.shopee },
    { label: "Link TikTok", value: data.shops?.tiktok },
    { label: "Foto KTP", value: [data.ktp!, data.selfieKtp!] },
  ],
];

const getDistributorPaymentProofFields = (data: FieldData) =>
  data.firstOrder?.map((order) => [
    {
      label: "id",
      value: order.orderId.toString(),
      hidden: true,
    },
    {
      label: "cost",
      value: order.shippingCost.toString(),
      hidden: true,
    },
    {
      label: "Nominal Seharusnya",
      value: parseToRupiah(order.totalPayment),
    },
    {
      label: "Notes",
      value: order.notes,
    },
    {
      label: "Bukti Transfer",
      value: [order.paymentProof], //put in an array so it rendered as image
    },
  ]) || [];

const getDistributorWorkLetterFields = (data: FieldData) => [
  [
    { label: "Nama Distributor", value: data.name },
    { label: "Foto KTP", value: [data.ktp!, data.selfieKtp!] },
    { label: "Uploaded Surat Kerja", value: data.sk, isPdf: true },
  ],
];

const FieldComponent = ({
  field,
  fields,
  index,
  type,
  active,
  loading,
  handleNext,
  handlePrev,
}: {
  field: FieldsProps[];
  fields: Array<FieldsProps[]>;
  index: number;
  type: string;
  active: number;
  loading: boolean;
  handleNext: (id: number, cost: number, reason?: string) => void;
  handlePrev: () => void;
}) => {
  const [reason, setReason] = useState("");
  const order = index + 1;
  const payment = field.some((item) => item.label === "cost");
  const id = Number(field.find((item) => item.label === "id")?.value || 0);
  const cost = Number(field.find((item) => item.label === "cost")?.value || 0);

  if (active !== index) return null;

  return (
    <Stack key={`order-${order}`}>
      {payment && (
        <Text mt="sm" ta="center" fw={500}>
          Order {order} dari {fields.length}
        </Text>
      )}
      <Paper bg="bg" p="md" w="100%">
        <Text fw={500} fz="h4" mb="sm">
          {payment ? "Uploaded Bukti Pembayaran" : `Detail ${type}`}
        </Text>
        <Stack gap="sm">
          {field.map(({ label, value, isPdf, hidden }, index) => {
            if (hidden) return null;
            const order = index + 1;
            return (
              <DataField
                key={`${order} - ${label}`}
                label={label}
                value={value}
                hideDivider={order >= field.length}
                isPdf={isPdf}
              />
            );
          })}
        </Stack>
      </Paper>
      {payment && (
        <Box>
          <TextInput
            value={reason}
            label="Alasan"
            onChange={(e) => setReason(e.target.value)}
          />
          <ButtonGroup mt="xl" justify="space-between" w="100%">
            <Button
              c="black"
              loading={loading}
              variant="subtle"
              onClick={handlePrev}
            >
              {active > 0 ? "Sebelumnya" : "Kembali"}
            </Button>
            <Group
              gap="xs"
              justify="end"
              w={{ base: "100%", sm: "fit-content" }}
            >
              <Button
                color="error"
                loading={loading}
                disabled={isEmpty(reason)}
                onClick={() => handleNext(id, cost, reason)}
              >
                Reject
              </Button>
              <Button
                loading={loading}
                disabled={!isEmpty(reason)}
                onClick={() => handleNext(id, cost)}
              >
                Terima
              </Button>
            </Group>
          </ButtonGroup>
        </Box>
      )}
    </Stack>
  );
};

const ApplicationModalData: React.FC<
  NewApplicationData & { isPhkDistributor?: boolean }
> = ({ type, newAgent: data, isPhkDistributor = false }) => {
  const dispatch: AppDispatch = useDispatch();
  const { applicationRequest, firstOrdersToUpdate, loading } = useSelector(
    (state: RootState) => state.adminRequest
  );
  const [active, setActive] = useState(0);
  const { submitRequest, closeModal } = useAdminRequestContext();
  const step = applicationRequest?.requestStep;

  const getFields = (): Array<FieldsProps[]> => {
    if (type === "Agent") return getAgentFields(data);
    if (isPhkDistributor) return getDistributorDetailFields(data);
    if (step === 3) return getDistributorPaymentProofFields(data);
    if (step === 4) return getDistributorWorkLetterFields(data);
    return getDistributorDetailFields(data);
  };

  const fields = getFields();

  const handleNext = async (id: number, cost: number, reason?: string) => {
    dispatch(
      setFirstOrderToUpdate({
        orderId: id,
        shippingCost: cost,
        rejectReason: reason || "",
      })
    );

    if (active + 1 < fields.length) {
      setActive(active + 1);
      return;
    }

    const hasRejectReason =
      firstOrdersToUpdate.some((order) => !isEmpty(order.rejectReason)) ||
      !isEmpty(reason);
    submitRequest?.(hasRejectReason ? "reject" : "accept");
  };

  const handlePrev = () => {
    if (active > 0) {
      setActive(active - 1);
      return;
    }
    closeModal?.();
  };

  return fields.map((field, index) => (
    <FieldComponent
      key={index}
      field={field}
      fields={fields}
      index={index}
      type={type}
      active={active}
      loading={loading}
      handleNext={handleNext}
      handlePrev={handlePrev}
    />
  ));
};

export default ApplicationModalData;
