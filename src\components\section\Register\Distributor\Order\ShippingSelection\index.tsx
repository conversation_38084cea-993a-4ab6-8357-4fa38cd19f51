import RadioCard from "@/components/common/RadioCard";
import { AppDispatch, RootState } from "@/store";
import { fetchShippingOptions } from "@/store/slices/distributorRegisterSlice";
import {
  isEmpty,
  isJabodetabek,
  parseToRupiah,
} from "@/utils/common-functions";
import {
  RegisterData,
  ValidDeliveryOptions,
} from "@/utils/types/distributor-register";
import { Stack, Text, Radio, Box, TextInput, SimpleGrid } from "@mantine/core";
import { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

interface Courier {
  label: ValidDeliveryOptions | (string & {});
  price: number;
  description?: string;
  disabled?: boolean;
}

const getCourierOptions = (
  user: RegisterData,
  deliveryCost: number
): Courier[] => [
  {
    label: "Truck Hotto",
    price: -1,
    description: !isJabodetabek(user.city) ? "Khusus Jabodetabek" : undefined,
    disabled: !isJ<PERSON><PERSON><PERSON>bek(user.city),
  },
  {
    label: "J&T Cargo",
    price: -1,
  },
  {
    label: "Central Cargo",
    price: -1,
  },
  {
    label: "Expedisi Lain",
    price: -1,
    description: "Anda dapat memilih expedisi sesuai yang Anda mau",
  },
];

const ShippingSelection = ({
  user,
  onChange,
}: {
  user: RegisterData;
  onChange?: (id: string) => void;
}) => {
  const dispatch: AppDispatch = useDispatch();
  const {
    shippingOptionList,
    productList,
    order: { products },
  } = useSelector((state: RootState) => state.distributorRegister);
  const [value, setValue] = useState("");
  const [selected, setSelected] = useState("");
  const deliveryCost = Number(process.env.NEXT_PUBLIC_DELIVERY_COST || 0);

  const handleSelect = (newValue: string) => {
    setSelected(newValue);
    setValue("");
    onChange?.(newValue !== "other" ? newValue : value);
  };

  const handleChange = (newValue: string) => {
    setValue(newValue);
    onChange?.(newValue);
  };

  const freeDelivery = useMemo((): boolean => {
    if (products.length === 1) return true;

    const secondProduct = productList.find(
      ({ id }) => id.toString() === products[1]?.id.toString()
    );

    return secondProduct?.name.includes("120 Pouch") || false;
  }, [productList, products]);

  useEffect(() => {
    if (isEmpty(shippingOptionList)) {
      dispatch(fetchShippingOptions(user));
    }

    // Reset selected courier when products change
    setSelected("");
  }, [shippingOptionList, products]);

  const COURIERS = getCourierOptions(user, deliveryCost);

  return (
    <Stack>
      <Box>
        <Text fz="h3" fw={500}>
          Pengiriman Produk
        </Text>
        <Text c="text-light">
          Silakan pilih salah satu metode pengiriman produk.
        </Text>
      </Box>
      <Radio.Group value={selected} onChange={handleSelect}>
        <SimpleGrid cols={{ base: 1, sm: 2 }}>
          {COURIERS.map((courier) => (
            <RadioCard
              disabled={courier.disabled}
              indicatorProps={{ variant: "outline" }}
              value={courier.label}
              key={courier.label}
              withIndicator
              h="100%"
            >
              <Text fz="h4" fw={500}>
                {courier.label}
              </Text>
              <Text c="text-light">
                {courier.description ||
                  "Estimasi harga: " +
                    (freeDelivery
                      ? "FREE"
                      : courier.price < 0
                      ? "TBA"
                      : parseToRupiah(courier.price))}
              </Text>
            </RadioCard>
          ))}
        </SimpleGrid>
      </Radio.Group>
      {selected === "Expedisi Lain" && (
        <TextInput
          required
          value={value}
          label="Nama Ekspedisi"
          onChange={(e) => handleChange(e.target.value)}
        />
      )}
    </Stack>
  );
};

export default ShippingSelection;
