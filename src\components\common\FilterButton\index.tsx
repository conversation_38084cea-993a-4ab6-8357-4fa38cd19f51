import { Button } from "@mantine/core";
import useBreakpoints from "@/hooks/useBreakpoints";

interface FilterButtonProps {
  item: {
    label: string;
    value: string;
  };
  activeFilter: string;
  handleFilter: (value: string) => void;
}

const FilterButton = ({
  item,
  activeFilter,
  handleFilter,
}: FilterButtonProps) => {
  const { mobile } = useBreakpoints();

  return (
    <Button
      key={item.value}
      size={mobile ? "xs" : "sm"}
      variant={activeFilter === item.value ? "light" : "transparent"}
      c={"black"}
      onClick={() => handleFilter(item.value)}
      styles={{
        root: {
          border: `1px solid ${
            activeFilter === item.value
              ? "var(--mantine-color-primary-filled)"
              : "var(--mantine-color-bg-filled)"
          }`,
        },
      }}
    >
      {item.label}
    </Button>
  );
};

export default FilterButton;
