import { Anchor, Flex } from "@mantine/core";
import Icon from "../Icon";
import IconButton from "../IconButton";

interface UploadedFileRowProps {
  fileUrl: string;
  onDelete?: () => void;
}

const UploadedFileRow = ({ fileUrl, onDelete }: UploadedFileRowProps) => {
  return (
    <Flex justify={"space-between"} align={"center"} gap={"xs"}>
      <Flex
        gap={"xs"}
        align={"center"}
        style={{
          overflowX: "hidden",
        }}
      >
        <Icon icon="info/document-fill" size={20} />
        <Anchor
          fw={500}
          style={{
            overflowX: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
          href={fileUrl}
          target="_blank"
        >
          {fileUrl}
        </Anchor>
      </Flex>
      {onDelete && (
        <IconButton icon="action/delete" size={20} onClick={onDelete} />
      )}
    </Flex>
  );
};

export default UploadedFileRow;
