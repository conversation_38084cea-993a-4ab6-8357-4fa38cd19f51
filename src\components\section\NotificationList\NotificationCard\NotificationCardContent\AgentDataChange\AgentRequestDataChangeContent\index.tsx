import UpdateLog from "@/components/section/AdminAgentDistributor/UpdateLog";
import { Flex, Text, Button } from "@mantine/core";
import { useState } from "react";
import ReviewStatus from "@/components/common/ReviewStatus";
import useBreakpoints from "@/hooks/useBreakpoints";
import ComplaintModal from "@/components/section/NotificationList/NotificationCard/NotificationCardContent/AgentDataChange/ComplaintModal";

interface AgentRequestDataChangeContentProps {
  requestId: number;
  field: string;
  oldValue: string;
  newValue: string;
  reason?: string;
  status?: "pending" | "accept" | "reject";
  submittedComplaint?: string;
  sourceAgent?: string;
}

const AgentRequestDataChangeContent = ({
  requestId,
  field,
  oldValue,
  newValue,
  reason = "-",
  status = "pending",
  submittedComplaint = undefined,
  sourceAgent = "",
}: AgentRequestDataChangeContentProps) => {
  const [openedModal, setOpenedModal] = useState(false);
  const { mobile } = useBreakpoints();

  return (
    <>
      <ComplaintModal
        opened={openedModal}
        onClose={() => setOpenedModal(false)}
        field={field}
        oldValue={oldValue}
        newValue={newValue}
        requestId={requestId}
        sourceAgent={sourceAgent}
      />
      <Flex
        direction={mobile ? "column" : "row"}
        justify={"space-between"}
        p={"lg"}
        gap={"xl"}
      >
        <Flex maw={"100%"}>
          <UpdateLog
            log={{
              fieldChanged: field,
              old: oldValue,
              new: newValue,
              reason: reason,
            }}
            fw={500}
          />
        </Flex>
        <Flex w={mobile ? "100%" : "fit-content"} justify={"flex-end"}>
          {status === "pending" ? (
            submittedComplaint?.trim() ? (
              <Text fw={400} fs={"italic"}>
                Complaint diajukan
              </Text>
            ) : (
              <Button onClick={() => setOpenedModal(true)}>
                Ajukan Complaint
              </Button>
            )
          ) : (
            <ReviewStatus status={status} />
          )}
        </Flex>
      </Flex>
    </>
  );
};

export default AgentRequestDataChangeContent;
