import { Text } from "@mantine/core";
import { InputGroupWrapper } from "./style";

interface InputGroupProps {
  label: string;
  children: React.ReactNode;
  mandatory?: boolean;
  error?: string;
  w?: string;
  caption?: string | React.ReactNode;
}

const InputGroup = ({
  label,
  children,
  mandatory = false,
  error,
  w,
  caption,
}: InputGroupProps) => {
  return (
    <InputGroupWrapper w={w}>
      <label htmlFor={label}>
        {label}
        {mandatory && <span className="star">*</span>}
      </label>
      {children}
      {caption &&
        (typeof caption === "string" ? (
          <Text size="sm">{caption}</Text>
        ) : (
          caption
        ))}
      {error && (
        <Text c="error" size="sm">
          {error}
        </Text>
      )}
    </InputGroupWrapper>
  );
};

export default InputGroup;
