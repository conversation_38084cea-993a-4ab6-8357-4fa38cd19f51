import {
  AppShell,
  AppShellHeaderProps,
  Box,
  Burger,
  Flex,
  Group,
  Title,
} from "@mantine/core";
import { padding } from "..";
import UserProfilePopover from "../../UserProfilePopover";
import { isEmpty } from "@/utils/common-functions";
import { memo } from "react";

export interface HeaderProps extends Partial<AppShellHeaderProps> {
  title?: string;
  opened: boolean;
  toggle: () => void;
  headerContent?: React.ReactNode;
}

const Header: React.FC<HeaderProps> = ({
  title,
  opened,
  toggle,
  headerContent,
  ...props
}) => {
  return (
    <AppShell.Header p={padding} {...props}>
      <Flex h="100%" w="100%" align="center">
        <Group h="100%" w="100%" justify="space-between">
          <Title fz={{ base: "h3", sm: "h2" }}>{title}</Title>
          <Burger opened={opened} onClick={toggle} hiddenFrom="sm" size="sm" />
          <UserProfilePopover visibleFrom="sm" />
        </Group>
      </Flex>
      {!isEmpty(headerContent) ? (
        <Box mt={padding} mx={`-${padding}`}>
          {headerContent}
        </Box>
      ) : null}
    </AppShell.Header>
  );
};

export default memo(Header);
