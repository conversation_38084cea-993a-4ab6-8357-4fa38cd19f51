import Icon from "@/components/common/Icon";
import ReviewStatus from "@/components/common/ReviewStatus";
import { formatDateToString, formatField } from "@/utils/common-functions";
import { UpdateLogData } from "@/utils/types/distributor-agent";
import { Flex, Box, Text } from "@mantine/core";
import React from "react";

export interface UpdateLogProps {
  log: UpdateLogData;
  fw?: number;
  align?: "left" | "right" | "center";
}

const UpdateLog = ({ log, align = "left", fw = 500 }: UpdateLogProps) => {
  return (
    <Flex justify="space-between" align="center" wrap="wrap" maw={"100%"}>
      <Box maw={"100%"}>
        <Flex
          gap="xs"
          wrap="wrap"
          align={"center"}
          justify={
            align === "center"
              ? "center"
              : align === "right"
              ? "flex-end"
              : "flex-start"
          }
        >
          <Text fw={fw || 400}>{formatField(log.fieldChanged)}: </Text>
          <Text
            fw={fw || 400}
            c="text"
            td="line-through"
            style={{ wordBreak: "break-word", whiteSpace: "normal" }}
          >
            {log.old !== "" ? log.old : "-"}
          </Text>
          <Icon icon="/arrow/right" size={12} />
          <Text
            fw={fw || 400}
            style={{ wordBreak: "break-word", whiteSpace: "normal" }}
          >
            {log.new}
          </Text>
        </Flex>
        {log.reason && (
          <Text c="text" ta={align || "left"} fw={fw || 400}>
            Alasan: {log.reason}
          </Text>
        )}
        {log.date && (
          <Text c="text" ta={align || "left"} fw={fw || 400}>
            Diubah pada {formatDateToString(log.date)}
          </Text>
        )}
      </Box>
      {log.status && (
        <ReviewStatus
          status={
            log.status === 0
              ? "rejected"
              : log.status === 1
              ? "accepted"
              : "forced"
          }
        />
      )}
    </Flex>
  );
};

export default UpdateLog;
