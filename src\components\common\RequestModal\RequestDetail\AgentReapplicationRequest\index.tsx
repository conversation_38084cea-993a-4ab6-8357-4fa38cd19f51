import { AppDispatch, RootState } from "@/store";
import { fetchRequestDetail } from "@/store/slices/adminRequestSlice";
import { RequestType } from "@/utils/types/request";

import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import ApplicationModalData from "../ApplicationRequest/ApplicationData";

interface RequestDetailContentProps {
  requestId: number;
}
const AgentReapplicationRequestDetail = ({
  requestId,
}: RequestDetailContentProps) => {
  const dispatch: AppDispatch = useDispatch();

  const { applicationRequest } = useSelector(
    (state: RootState) => state.adminRequest
  );

  useEffect(() => {
    dispatch(fetchRequestDetail(requestId, RequestType.Application));
  }, [requestId]);

  return (
    <>
      {applicationRequest?.newAgent && (
        <ApplicationModalData
          type="Agent"
          newAgent={applicationRequest.newAgent}
          requestStep={1}
          isPhkDistributor={true}
        />
      )}
    </>
  );
};

export default AgentReapplicationRequestDetail;
