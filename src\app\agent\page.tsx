"use client";

import PageLayout from "@/components/common/PageLayout";
import AgentApplicationAlert from "@/components/section/Distributor/AgentApplicationAlert";
import AgentApplicationModal from "@/components/section/Distributor/AgentApplicationModal";
import AgentsList from "@/components/section/Distributor/AgentsList";
import { AppDispatch, RootState } from "@/store";
import { submitReceiveAgent } from "@/store/slices/distributorAgentsSlice";
import { SubmitReceiveAgentPayload } from "@/utils/types/distributor-agent";
import { withAuth } from "@/utils/withAuth";
import { useDisclosure } from "@mantine/hooks";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "sonner";

const AgentsPage = () => {
  const [opened, handlers] = useDisclosure(false);
  const dispatch: AppDispatch = useDispatch();
  const { data: user } = useSelector((state: RootState) => state.userProfile);
  const { agentList } = useSelector(
    (state: RootState) => state.distributorAgents
  );

  const handeFormSubmit = (data: SubmitReceiveAgentPayload) => {
    dispatch(submitReceiveAgent(data)).then(() => handlers.close());
  };

  return (
    <PageLayout title="Agents">
      <AgentApplicationAlert
        isReceivingAgent={user.isReceivingAgent}
        quota={999}
        quotaUsed={agentList.length}
        onCompletionClick={handlers.open}
        onApplicationChange={(open) => {
          //TODO: integrate with API
          toast.info(open ? "Application opened" : "Application closed");
        }}
      />
      <AgentApplicationModal
        opened={opened}
        onClose={handlers.close}
        onSubmit={handeFormSubmit}
      />
      <AgentsList onApplicationFormClick={handlers.open} />
    </PageLayout>
  );
};

export default withAuth(AgentsPage);
