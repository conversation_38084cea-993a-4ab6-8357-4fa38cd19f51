"use client";

import { Paper, Text, Stack, List, Checkbox } from "@mantine/core";
import classes from "../../../Forms/UserAgreement/style.module.css";

interface ShippingPolicyProps {
  onFormValid: (isValid: boolean) => void;
}

const POLICIES = [
  "Proses konfirmasi pengiriman hingga pengiriman ke alamat Anda akan berlangsung selama kurang lebih 7 hari kerja.",
  "Kami akan memberikan foto produk yang akan dikirim saat invoice sudah dibuat.",
  "Kerusakan & kehilangan di luar tanggung jawab kantor pusat.",
  "Keterlambatan pengiriman merupakan sepenuhnya tanggung jawab ekspedisi.",
  "Kantor pusat hanya bertanggung jawab sampai resi diberikan kepada distributor.",
];

const ShippingPolicy: React.FC<ShippingPolicyProps> = ({ onFormValid }) => {
  return (
    <Stack gap="xl">
      <Paper
        withBorder
        p="lg"
        bg="bg"
        ta={{ sm: "justify" }}
        style={{ borderColor: "var(--mantine-color-gray-5)" }}
      >
        <Stack gap="sm">
          <Text fw={700} fz="h3">
            Kebijakan Pengiriman Hotto
          </Text>
          <List withPadding mr="lg">
            {POLICIES.map((info, index) => (
              <List.Item key={index}>{info}</List.Item>
            ))}
          </List>
        </Stack>
      </Paper>
      <Checkbox
        classNames={classes}
        onChange={(target) => onFormValid(target.currentTarget.checked)}
        label="Saya setuju untuk mengikuti syarat & ketentuan pengiriman dari Hotto."
        description="Saya memahami bahwa kantor pusat Hotto hanya dapat membantu sesuai dengan kebijakan yang ada."
      />
    </Stack>
  );
};

export default ShippingPolicy;
