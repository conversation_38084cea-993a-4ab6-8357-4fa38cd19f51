"use client";

import dynamic from "next/dynamic";

import PageLayout from "@/components/common/PageLayout";

const DistributorRequest = dynamic(
  () => import("@/components/section/DistributorRequest"),
  { ssr: false }
);

import useBreakpoints from "@/hooks/useBreakpoints";
import { AppDispatch, RootState } from "@/store";
import { fetchDraftRequests } from "@/store/slices/distributorRequestSlice";
import { DraftType } from "@/utils/types";
import { withAuth } from "@/utils/withAuth";
import { Flex } from "@mantine/core";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import DraftList from "@/components/common/DraftList";

const RequestPage = () => {
  const { draftRequests } = useSelector((state: RootState) => state.request);
  const dispatch: AppDispatch = useDispatch();

  useEffect(() => {
    dispatch(fetchDraftRequests());
  }, []);

  const { mobile, tablet } = useBreakpoints();
  return (
    <PageLayout title="Request">
      <Flex
        gap={"xl"}
        direction={mobile ? "column-reverse" : "row"}
        justify={"space-between"}
        w={"100%"}
        wrap={tablet || mobile ? "wrap-reverse" : "nowrap"}
        pb={mobile || tablet ? "3em" : "0"}
      >
        <DistributorRequest />
        <DraftList drafts={draftRequests} draftType={DraftType.Request} />
      </Flex>
    </PageLayout>
  );
};

export default withAuth(RequestPage);
