import { useSearchParams } from "next/navigation";
import CityAgentsColumn from "./CityAgentsColumn";
import { CityAgentsBoardContainer, CityAgentsBoardWrapper } from "./style";
import { useEffect } from "react";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { fetchProvincesByIsland } from "@/store/slices/adminAgentListSlice";
import { Center, Loader } from "@mantine/core";
import { isEmpty } from "@/utils/common-functions";

const CityAgentsBoard = () => {
  const dispatch: AppDispatch = useDispatch();
  const { provinces, loading } = useSelector(
    (state: RootState) => state.adminAgentList
  );

  const searchParams = useSearchParams();
  const island = searchParams?.get("island");

  useEffect(() => {
    if (!isEmpty(island)) {
      dispatch(fetchProvincesByIsland(island as string));
    }
  }, [island]);

  if (loading) {
    return (
      <Center my={200}>
        <Loader />
      </Center>
    );
  }

  return (
    <CityAgentsBoardContainer>
      {provinces && (
        <CityAgentsBoardWrapper>
          {provinces.map((province, index) => (
            <CityAgentsColumn
              key={index}
              name={province.name}
              count={province.countAgent}
              agents={province.agents}
            />
          ))}
        </CityAgentsBoardWrapper>
      )}
    </CityAgentsBoardContainer>
  );
};

export default CityAgentsBoard;
