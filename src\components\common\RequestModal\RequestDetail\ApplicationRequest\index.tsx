import Stepper from "@/components/common/Stepper";
import { AppDispatch, RootState } from "@/store";
import { fetchRequestDetail } from "@/store/slices/adminRequestSlice";
import { RequestType } from "@/utils/types/request";
import { Box, Flex, Text } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import ApplicationModalData from "./ApplicationData";
import ApplicationTransferEvidence from "./ApplicationTransferEvidence";

const NEW_APPLICATION_MODAL_STEPS = [
  {
    index: 0,
    label: "Approval Distributor",
  },
  {
    index: 1,
    label: "Approval Admin",
  },
  {
    index: 2,
    label: "Konfirmasi",
  },
];

interface RequestDetailContentProps {
  requestId: number;
  applicationStep?: number;
}

const ApplicationRequestDetail = ({
  requestId,
  applicationStep = 0,
}: RequestDetailContentProps) => {
  const dispatch: AppDispatch = useDispatch();
  const { applicationRequest } = useSelector(
    (state: RootState) => state.adminRequest
  );

  useEffect(() => {
    if (applicationStep > 0) {
      dispatch(fetchRequestDetail(requestId, RequestType.Application));
    }
  }, [requestId, applicationStep]);

  const renderContentHeading = () => {
    switch (applicationStep) {
      case 1:
        return (
          <Text c={"text-light"} maw={"90%"} ta={"center"} fw={600}>
            Jika Anda menyetujui aplikasi ini, admin pusat akan me-review
            kembali aplikasi calon agent ini.
          </Text>
        );
      case 2:
        return (
          <Text fw={600} ta={"center"} maw={"90%"}>{`Distributor '${
            applicationRequest?.sourceAgent || "-"
          }' ingin mendaftarkan agent baru:`}</Text>
        );
      case 3:
        return (
          <Text c={"text-light"} maw={"90%"} ta={"center"} fw={600}>
            Periksa bukti pembayaran dari calon Agent ini.
          </Text>
        );
      default:
        return null;
    }
  };

  const isMobile = useMediaQuery("(max-width: 768px)");
  return (
    <>
      <Flex direction={"column"} gap={"md"} align={"center"} w={"100%"}>
        <Box w={isMobile ? "100%" : "80%"}>
          <Stepper
            active={applicationStep - 1}
            steps={NEW_APPLICATION_MODAL_STEPS}
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            onStepClick={(number) => {
              // NOTE: because you can't move from 1 step to another
              return;
            }}
          />
        </Box>
        {applicationRequest && (
          <Flex
            mah={isMobile ? "40vh" : "fit-content"}
            w={"100%"}
            style={{ overflowY: "scroll" }}
            direction={"column"}
            gap={"md"}
            align={"center"}
            pb={"md"}
            className="scrollable-form"
          >
            {renderContentHeading()}
            {(applicationStep === 1 || applicationStep === 2) && (
              <ApplicationModalData {...applicationRequest} />
            )}
            {applicationStep === 3 && (
              <ApplicationTransferEvidence
                evidenceImageUrl={applicationRequest.transferEvidence}
              />
            )}
          </Flex>
        )}
      </Flex>
    </>
  );
};

export default ApplicationRequestDetail;
