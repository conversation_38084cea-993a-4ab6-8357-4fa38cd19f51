import ButtonGroup from "@/components/common/ButtonGroup";
import Loop from "@/components/common/Loop";
import { getTodayDateTime } from "@/utils/common-functions";
import { Delivery } from "@/utils/icons";
import {
  Stack,
  Text,
  Paper,
  Group,
  Box,
  Flex,
  Image,
  NumberFormatter,
  Button,
} from "@mantine/core";

interface ComplaintResolutionProps {
  onClose?: () => void;
}

const ComplaintResolution = ({ onClose }: ComplaintResolutionProps) => {
  return (
    <Stack>
      <Text c="text-light" ta="center">
        Customer tidak menerima produk.
      </Text>
      <Paper bg="bg" p="sm">
        <Group gap="xs">
          <Delivery color="var(--mantine-color-primary-filled)" />
          <Text>Ekspedisi Terpilih</Text>
        </Group>
        <Text c="text-light">J&T Cargo - Nomor Resi JNT123567</Text>
      </Paper>
      <Stack gap="xs">
        <Text c="text-light">Produk dikirimkan</Text>
        <Loop count={3}>
          <Flex gap="md">
            <Image
              w={100}
              h={100}
              width={100}
              height={100}
              radius="md"
              fit="cover"
              alt="Product Image"
              bd="2px solid var(--mantine-primary-color-filled)"
            />
            <Stack justify="space-between">
              <Box>
                <Text fw={500}>
                  Hotto Purto Multigrain dengan Ubi Ungu 1 Pouch
                </Text>
                <Text>Variasi: 1 Pouch</Text>
              </Box>
              <Text c="text">
                <NumberFormatter value={295000} />
              </Text>
            </Stack>
          </Flex>
        </Loop>
      </Stack>
      <Box>
        <Text>
          Tanggal produk dinyatakan terkirim: <strong>01 Apr 2025</strong>
        </Text>
        <Text c="text-light">
          Customer menyatakan bahwa hingga hari ini ({getTodayDateTime(false)}),
          produk belum diterima.
        </Text>
      </Box>
      <Box>
        <Text>
          Silakan menghubungi ekspedisi & customer untuk resolusi komplain ini.
        </Text>
        <Text c="text-light">Nomor telepon customer: 081234567890</Text>
      </Box>
      <Paper bg="error.0" p="sm" mx="-lg" radius="8 8 0 0">
        <Text c="error">
          Hanya klik resolusi jika komplain telah disolusikan dengan customer
          ini. Resolusi dapat dilakukan H+1 setelah complaint masuk.
        </Text>
      </Paper>
      <ButtonGroup justify="space-between">
        <Button variant="subtle" onClick={onClose}>
          Kembali
        </Button>
        <Button>Resolusi</Button>
      </ButtonGroup>
    </Stack>
  );
};

export default ComplaintResolution;
