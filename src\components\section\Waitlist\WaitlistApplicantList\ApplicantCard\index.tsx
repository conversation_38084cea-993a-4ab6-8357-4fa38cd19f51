import { Button, Flex, <PERSON>, Stack, Text } from "@mantine/core";
import ActivationStatus from "@/components/common/ActivationStatus";
import Icon from "@/components/common/Icon";
import { AppDispatch } from "@/store";
import { useDispatch } from "react-redux";
import { ApplicantData } from "@/utils/types/distributor-agent";
import { setSelectedApplicant } from "@/store/slices/waitlistSlice";

interface ApplicantCardProps {
  applicant: ApplicantData;
}

const ApplicantCard = ({ applicant }: ApplicantCardProps) => {
  const dispatch: AppDispatch = useDispatch();

  return (
    <Paper bg={`bg`} p={"xl"} w="100%">
      <Flex justify="space-between" gap={"sm"} align={"center"}>
        <Stack gap="xs">
          <Flex align="center" gap="sm">
            <Text fw={700}>{applicant.name}</Text>
            <ActivationStatus status={applicant.status as 0 | 1 | 2} />
          </Flex>
          <Flex direction="row" gap="xs" align="center">
            <Icon size={20} icon="info/location" />
            <Text size="sm" c={"text-light"}>
              {applicant.warehouseAddress}
            </Text>
          </Flex>
        </Stack>
        <Button
          variant="transparent"
          onClick={() => {
            dispatch(setSelectedApplicant(applicant));
          }}
        >
          View Detail
        </Button>
      </Flex>
    </Paper>
  );
};

export default ApplicantCard;
