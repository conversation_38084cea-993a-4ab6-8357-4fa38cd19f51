"use client";

import { Stack, Divider, Text, Skeleton } from "@mantine/core";
import { Fragment, useEffect } from "react";
import Links from "./Links";
import LinksSubmit from "./LinksSubmit";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import {
  fetchAllLinks,
  resetInvalidLinksReasons,
  submitInvalidLinksReasons,
} from "@/store/slices/adminMarketplaceLinksSlice";
import { useSearchParams } from "next/navigation";
import { randomNumber } from "@/utils/common-functions";
import { AdminSubmitInvalidLinksReasonsPayload } from "@/utils/types/request";
import { toast } from "sonner";

const MarketplaceLinks: React.FC = () => {
  const dispatch: AppDispatch = useDispatch();
  const searchParams = useSearchParams();
  const island = searchParams?.get("island");

  const { data, loading, error, invalidLinksReasons } = useSelector(
    (state: RootState) => state.adminMarketplaceLinks
  );

  const isLoading = loading || error !== undefined;
  const dataLength = data.length;
  const noLinkToSubmit =
    !invalidLinksReasons || invalidLinksReasons.length === 0;

  const onSubmit = () => {
    if (noLinkToSubmit) {
      toast.error("Please select at least 1 link to submit");
    } else {
      const data: AdminSubmitInvalidLinksReasonsPayload = {
        invalidLinks: invalidLinksReasons,
      };
      dispatch(submitInvalidLinksReasons(island!, data));
    }
  };

  useEffect(() => {
    if (island && island !== "") {
      dispatch(resetInvalidLinksReasons());
      dispatch(fetchAllLinks(island));
    }
  }, [island]);

  const LinksSkeleton = () => {
    return (
      <Stack gap="sm">
        {Array.from({ length: 30 }).map((_, index) => {
          return (
            <Skeleton
              key={index}
              height={25}
              radius="xl"
              width={`${randomNumber(30, 80)}%`}
            />
          );
        })}
      </Stack>
    );
  };

  return (
    <Stack
      component="form"
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit();
      }}>
      {dataLength === 0 ? (
        <Text>Belum ada link yang dapat diperiksa</Text>
      ) : isLoading ? (
        <LinksSkeleton />
      ) : (
        data.map(({ links, uoc, userName, userRole }, index) => (
          <Fragment key={index}>
            <Links
              userRole={userRole}
              userName={userName}
              uoc={uoc}
              links={links}
            />
            {index !== dataLength - 1 && <Divider size="sm" />}
          </Fragment>
        ))
      )}
      {dataLength > 0 && <LinksSubmit disabled={noLinkToSubmit || isLoading} />}
    </Stack>
  );
};

export default MarketplaceLinks;
