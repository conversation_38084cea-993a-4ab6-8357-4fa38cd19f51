import type { SVGProps } from "react";

export default function Caret({
  direction = "up",
  ...props
}: SVGProps<SVGSVGElement> & { direction?: "up" | "down" | "left" | "right" }) {
  const transform = {
    up: "translateY(-2px) scaleY(1)",
    down: "scaleY(-1)",
    left: "rotate(90deg) scaleY(-1)",
    right: "rotate(90deg) scaleY(1)",
  }[direction];

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="14"
      height="14"
      viewBox="0 0 16 16"
      style={{
        transform,
        transition: "transform 0.2s ease-out",
        transformOrigin: "center center",
      }}
      {...props}
    >
      <path
        fill="currentColor"
        d="M14.25 10.6875L12.35 12.5875L9.5 9.73752L6.65 12.5875L4.75 10.6875L9.5 5.93752L14.25 10.6875Z"
      />
    </svg>
  );
}
