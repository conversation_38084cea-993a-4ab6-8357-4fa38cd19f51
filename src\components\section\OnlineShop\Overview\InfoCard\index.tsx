import {
  formatNumber,
  formatPercentage,
  getPercentageColor,
} from "@/utils/common-functions";
import { Info } from "@/utils/icons";
import { Card, Group, Text, CardProps, Stack, HoverCard } from "@mantine/core";

export interface InfoCardProps extends CardProps {
  label: string;
  value: string | number;
  description?: string;
  formatValueAs?: "currency" | "percentage" | "rating";
  differences?: number;
}

const InfoCard: React.FC<InfoCardProps> = ({
  label,
  value,
  description,
  formatValueAs,
  differences,
  ...props
}) => {
  return (
    <Card bg="bg" padding="lg" w="100%" {...props}>
      <Group wrap="nowrap" justify="space-between" mb="xl" gap={5}>
        <Text fw={500}>{label}</Text>
        {description !== undefined && (
          <HoverCard
            withArrow
            arrowSize={10}
            width={280}
            shadow="md"
            position="top"
          >
            <HoverCard.Target>
              <Info size={18} style={{ cursor: "help" }} />
            </HoverCard.Target>
            <HoverCard.Dropdown style={{ cursor: "help" }}>
              <Text fw={500}>{label}</Text>
              <Text size="sm">{description}</Text>
            </HoverCard.Dropdown>
          </HoverCard>
        )}
      </Group>
      <Stack gap={0} justify="center" h="100%">
        <Text fw={600} fz="h4">
          {typeof value === "number" && formatValueAs
            ? formatNumber(Number(value), formatValueAs)
            : value}
        </Text>
        {differences !== undefined && (
          <Text fz="h5" c={getPercentageColor(differences)}>
            {formatPercentage(differences)}
          </Text>
        )}
      </Stack>
    </Card>
  );
};

export default InfoCard;
