import ButtonGroup from "@/components/common/ButtonGroup";
import { StepData } from "@/components/common/Stepper";
import { Flex, Button } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import DataChangeModalHeader from "./ModalHeader";
import { useEffect, useState } from "react";
import {
  AgentDistributorDetailsForceUpdateData,
  UserAgentDetail,
} from "@/utils/types/distributor-agent";
import { capitalize, checkObjectHasValue } from "@/utils/common-functions";
import ChangesSubmitted from "@/components/section/AdminAgentDistributor/ForceEditModal/ForceEditModalContents/ChangesSubmitted";
import DataChangeForm from "./DataChangeForm";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import {
  createDataChangeRequest,
  updateDataChangeRequest,
} from "@/store/slices/distributorRequestSlice";

export const DATA_CHANGE_REQUEST_STEPPER_DATA = (
  role: string = "distributor"
): StepData[] => [
  {
    index: 0,
    label: `Informasi ${capitalize(role)}`,
  },
  {
    index: 1,
    label: "Konfirmasi",
  },
];

interface DataChangeModalContentProps {
  onClose: () => void;
  id?: number;
  agentDetails: UserAgentDetail;
  currStep: number;
  setCurrStep: (step: number) => void;
  changes: AgentDistributorDetailsForceUpdateData;
  existingReason?: string;
  handleChanges: (data: string | undefined, key: string) => void;
}

const DataChangeModalContent = ({
  onClose,
  id,
  agentDetails,
  currStep,
  setCurrStep,
  changes,
  existingReason,
  handleChanges,
}: DataChangeModalContentProps) => {
  const [reason, setReason] = useState<string>(existingReason ?? "");

  useEffect(() => {
    setReason(existingReason ?? "");
  }, [existingReason]);

  const [isChangesValid, setIsChangesValid] = useState(false);
  const dispatch: AppDispatch = useDispatch();

  const checkNextStepEligibility = (): boolean => {
    switch (currStep) {
      case 0:
        return isChangesValid && checkObjectHasValue(changes);
      case 1:
        return reason.length > 0;
      default:
        return false;
    }
  };

  const handleSubmitButtonClick = () => {
    if (currStep < DATA_CHANGE_REQUEST_STEPPER_DATA().length - 1) {
      setCurrStep(currStep + 1);
    } else {
      dispatch(createDataChangeRequest(changes, reason, false, id));
      onClose();
    }
  };

  const handleSaveAsDraftButtonClick = () => {
    if (id) {
      dispatch(updateDataChangeRequest(id, changes, reason, true));
    } else {
      dispatch(createDataChangeRequest(changes, reason, true));
    }
    onClose();
  };

  const isMobile = useMediaQuery("(max-width: 768px)");

  const renderStepContent = () => {
    switch (currStep) {
      case 0:
        return (
          <DataChangeForm
            changes={changes}
            agentDetails={agentDetails}
            handleChanges={handleChanges}
            setChangesValidity={setIsChangesValid}
          />
        );
      case 1:
        return (
          <ChangesSubmitted
            changes={changes}
            reason={reason}
            setReason={setReason}
          />
        );
      default:
        return null;
    }
  };
  return (
    <Flex direction={"column"} gap={"sm"} align={"center"} w={"100%"}>
      <DataChangeModalHeader
        currStep={currStep}
        setCurrStep={setCurrStep}
        nextStepValidation={checkNextStepEligibility}
      />
      <Flex
        mih={"48vh"}
        mah={"48vh"}
        style={{ overflowY: "scroll", overflowX: "hidden" }}
        className="scrollable-form"
        w={"100%"}
      >
        {renderStepContent()}
      </Flex>

      <ButtonGroup justify={"space-between"} w={"100%"}>
        {isMobile ? (
          <Button variant="subtle" onClick={handleSaveAsDraftButtonClick}>
            Simpan sebagai draft
          </Button>
        ) : (
          <Button variant="subtle" onClick={onClose}>
            Kembali
          </Button>
        )}
        <ButtonGroup>
          {!isMobile && (
            <Button variant="subtle" onClick={handleSaveAsDraftButtonClick}>
              Simpan sebagai draft
            </Button>
          )}
          <Button
            onClick={handleSubmitButtonClick}
            disabled={!checkNextStepEligibility()}
          >
            {currStep < DATA_CHANGE_REQUEST_STEPPER_DATA().length - 1
              ? "Selanjutnya"
              : "Kirim"}
          </Button>
        </ButtonGroup>
      </ButtonGroup>
    </Flex>
  );
};

export default DataChangeModalContent;
