"use client";

import dynamic from "next/dynamic";
import useBreakpoints from "@/hooks/useBreakpoints";
import { ScrollArea, DrawerProps, ModalProps } from "@mantine/core";
const Modal = dynamic(() => import("@mantine/core").then((mod) => mod.Modal));
const Drawer = dynamic(() => import("@mantine/core").then((mod) => mod.Drawer));

type Omitted =
  | "opened"
  | "onClose"
  | "children"
  | "withCloseButton"
  | "closeOnClickOutside"
  | "closeOnEscape";

export interface ResponsiveModalProps extends React.PropsWithChildren {
  opened: (ModalProps | DrawerProps)["opened"];
  onClose: (ModalProps | DrawerProps)["onClose"];
  withCloseButton?: (ModalProps | DrawerProps)["withCloseButton"];
  closeOnClickOutside?: (ModalProps | DrawerProps)["closeOnClickOutside"];
  closeOnEscape?: (ModalProps | DrawerProps)["closeOnEscape"];
  modalProps?: Omit<ModalProps, Omitted>;
  drawerProps?: Omit<DrawerProps, Omitted>;
  withAutosizeScrollArea?: boolean;
  renderChildrenOnlyWhenOpened?: boolean;
  loading?: boolean;
}

/**
 * A responsive modal component that renders either a Drawer (on mobile) or a Modal (on desktop).
 *
 * @param {React.ReactNode} children - The content to be rendered inside the modal.
 * @param {boolean} opened - Whether the modal is open or closed.
 * @param {Function} onClose - Function to call when the modal is closed.
 * @param {boolean} [withCloseButton] - Whether to show a close button.
 * @param {boolean} [closeOnClickOutside] - Whether to close the modal when clicking outside.
 * @param {boolean} [closeOnEscape] - Whether to close the modal when pressing the escape key.
 * @param {Object} [modalProps] - Additional props to pass to the Modal component.
 * @param {Object} [drawerProps] - Additional props to pass to the Drawer component.
 * @param {boolean} [renderChildrenOnlyWhenOpened] - If true, only render children when the modal is opened.
 * @returns {React.ReactElement} The responsive modal component.
 */
const ResponsiveModal: React.FC<ResponsiveModalProps> = ({
  children,
  opened,
  onClose,
  withCloseButton,
  closeOnClickOutside,
  closeOnEscape,
  modalProps,
  drawerProps,
  withAutosizeScrollArea,
  renderChildrenOnlyWhenOpened,
  loading,
}) => {
  const { mobile } = useBreakpoints();

  const handleClose = () => {
    if (!loading) onClose();
  };

  const sharedProps = {
    opened,
    onClose: handleClose,
    withCloseButton,
    closeOnClickOutside,
    closeOnEscape,
    scrollAreaComponent: withAutosizeScrollArea
      ? ScrollArea.Autosize
      : undefined,
  };

  return mobile ? (
    <Drawer
      position="bottom"
      radius="8px 8px 0 0"
      {...sharedProps}
      {...drawerProps}
    >
      {renderChildrenOnlyWhenOpened ? opened && children : children}
    </Drawer>
  ) : (
    <Modal centered radius={8} {...sharedProps} {...modalProps}>
      {renderChildrenOnlyWhenOpened ? opened && children : children}
    </Modal>
  );
};

export default ResponsiveModal;
