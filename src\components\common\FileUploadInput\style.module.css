.root {
  transition: all 0.15s ease-in-out;
  background-color: var(--mantine-color-bg-filled);
  border: 1px dashed var(--mantine-color-text-light-filled);

  &:hover {
    background-color: var(--mantine-color-primary-0);
  }

  &[data-accept] {
    color: var(--mantine-color-success-filled);
    background-color: var(--mantine-color-success-1);
    border-color: var(--mantine-color-success-filled);
  }

  &[data-reject] {
    color: var(--mantine-color-red-filled);
    background-color: var(--mantine-color-red-1);
    border-color: var(--mantine-color-red-filled);
  }
}
