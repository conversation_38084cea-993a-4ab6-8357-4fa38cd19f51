"use client";

import {
  <PERSON>,
  Stack,
  Text,
  Title,
  <PERSON><PERSON>,
  Stepper,
  Tooltip,
} from "@mantine/core";
import { useEffect, useState } from "react";
import classes from "@/styles/stepper.module.css";
import dynamic from "next/dynamic";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { submitRegister } from "@/store/slices/distributorRegisterSlice";
import ButtonGroup from "@/components/common/ButtonGroup";
import VerificationInfoForm from "./VerificationInfo";
import { DISTRIBUTOR_REGISTRATION_DATA_VAR } from "@/utils/constants";
import { isEmpty } from "@/utils/common-functions";
const PersonalInfoForm = dynamic(() => import("./PersonalInfo"));
const DetailInfoForm = dynamic(() => import("./DetailInfo"));
const UserAgreementForm = dynamic(() => import("./UserAgreement"));

export interface FormStepperProps {
  onFormValid: (isValid: boolean) => void;
  nextClicked?: boolean;
  role?: "distributor" | "agent";
}

const RegisterForms: React.FC = () => {
  const dispatch: AppDispatch = useDispatch();
  const { isLoading } = useSelector(
    (state: RootState) => state.distributorRegister
  );
  const [active, setActive] = useState(0);
  const [continuable, setContinuable] = useState(false);
  const [nextClicked, setNextClicked] = useState(false);
  const nextStep = () =>
    setActive((current) => (current < maxStep ? current + 1 : current));
  const prevStep = () =>
    setActive((current) => (current > 0 ? current - 1 : current));

  const handleNext = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    if (continuable) {
      nextStep();
      setContinuable(false);
      setNextClicked(false);
    } else {
      setNextClicked(true);
    }
  };

  const handleSubmit = async () => {
    const data = await dispatch(submitRegister());
    if (!isEmpty(data) && !data.isWaitlisted) {
      localStorage.setItem(
        DISTRIBUTOR_REGISTRATION_DATA_VAR,
        JSON.stringify(data)
      );
    }
  };

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [active]);

  const STEPS = [
    {
      label: "Informasi Pribadi",
      step: (
        <PersonalInfoForm
          nextClicked={nextClicked}
          onFormValid={(isValid) => {
            setContinuable(isValid);
          }}
        />
      ),
    },
    {
      label: "Menjadi Distributor",
      step: (
        <DetailInfoForm
          nextClicked={nextClicked}
          onFormValid={(isValid) => {
            setContinuable(isValid);
          }}
        />
      ),
    },
    {
      label: "Verifikasi Identitas",
      step: (
        <VerificationInfoForm
          onFormValid={(isValid) => {
            setContinuable(isValid);
          }}
        />
      ),
    },
    {
      label: "Syarat & Ketentuan",
      step: (
        <UserAgreementForm
          onFormValid={(isValid) => {
            setContinuable(isValid);
          }}
        />
      ),
    },
  ];

  const maxStep = STEPS.length - 1;

  return (
    <Center w="100%">
      <Stack gap="xl" align="center">
        <Title ta="center" order={2}>
          Jadilah Mitra Kami dan Hadirkan Gaya Hidup Sehat untuk Semua!
        </Title>
        <Stack gap={32} w="100%" maw={{ base: "100%", sm: 700 }}>
          <Stepper
            active={active}
            classNames={classes}
            onStepClick={setActive}
            allowNextStepsSelect={false}
          >
            {STEPS.map(({ label, step }, index) => (
              <Stepper.Step key={`${index + 1}-${label}`} label={label}>
                <Text c="text-light" mb="lg" ta="center">
                  Isi form ini untuk menjadi partner distributor Hotto.
                </Text>
                {step}
              </Stepper.Step>
            ))}
          </Stepper>
          <ButtonGroup mt="xl" justify="center" w="100%">
            {active > 0 && (
              <Button c="black" variant="subtle" onClick={prevStep}>
                Kembali
              </Button>
            )}
            {active === maxStep ? (
              <Button
                loading={isLoading}
                disabled={!continuable}
                onClick={handleSubmit}
              >
                Daftar
              </Button>
            ) : (
              <Tooltip
                withArrow
                disabled={continuable}
                label="Isi form di atas dengan benar terlebih dahulu"
              >
                <Button onClick={handleNext} data-disabled={!continuable}>
                  Selanjutnya
                </Button>
              </Tooltip>
            )}
          </ButtonGroup>
        </Stack>
      </Stack>
    </Center>
  );
};

export default RegisterForms;
