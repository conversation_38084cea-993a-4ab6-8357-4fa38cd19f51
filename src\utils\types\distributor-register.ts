import { ApiResponse } from ".";

export interface ShopsInfo {
  tokopedia?: string;
  shopee?: string;
  tiktok?: string;
}

export interface PersonalInfoFormValues {
  name: string;
  nik: string;
  npwp: string;
  email: string;
  phoneNumber: string;
  island: string;
  province: string;
  regency: string;
  district: string;
  village: string;
  postalCode: string;
  address: string;
}

export interface DetailInfoFormValues extends ShopsInfo {
  warehouseIsland: string;
  warehouseProvince: string;
  warehouseRegency: string;
  warehouseDistrict: string;
  warehouseVillage: string;
  warehousePostalCode: string;
  warehouseAddress: string;
  adminPhoneNumber?: string;
}

export interface VerificationInfoFormValues {
  idCardImage: string;
  selfieImage: string;
}

export interface RegisterPayload {
  name: string;
  email: string;
  phoneNumber: string;
  nik: string;
  npwp: string;
  ktp: string;
  selfieKtp: string;
  island: string;
  province: string;
  city: string;
  district: string;
  kelurahan: string;
  postalCode: string;
  address: string;
  warehouseAddress: string;
  warehouseIsland: string;
  warehouseProvince: string;
  warehouseCity: string;
  warehouseDistrict: string;
  warehouseKelurahan: string;
  warehousePostalCode: string;
  adminPhoneNumber?: string;
  shops?: ShopsInfo;
}

export type RegisterResponse = ApiResponse<RegisterData>;

export interface RegisterData {
  id: number;
  name: string;
  slug: string;
  email: string;
  phoneNumber: string;
  adminPhoneNumber: string;
  nik: string;
  npwp: string;
  ktp: string;
  selfieKtp: string;
  island: string;
  province: string;
  city: string;
  district: string;
  kelurahan: string;
  uniqueUserCode: string;
  roleId: number;
  passwordHash: string;
  address: string;
  warehouseAddress: string;
  createdAt: string;
  updatedAt: string;
  role: {
    id: number;
    name: string;
    createdAt: string;
    updatedAt: string;
  };
  sk: string | null;
  shops: Required<ShopsInfo>;
  status: 0 | 1;
  bankType: string;
  bankAccountNumber: string;
  isReceivingAgent: boolean;
  latitude: number;
  longitude: number;
  isWaitlisted: boolean;
  postalCode: string;
  warehouseIsland: string;
  warehouseProvince: string;
  warehouseCity: string;
  warehouseDistrict: string;
  warehouseKelurahan: string;
  warehousePostalCode: string;
}

export type ProductListResponse = ApiResponse<ProductListItem[]>;

export interface ProductListItem {
  id: number;
  name: string;
  price: number;
}

export type ShippingOptionsResponse = ApiResponse<{
  shippingOptions: ShippingOptionsItem[];
}>;

export interface ShippingOptionsItem {
  name: string;
  code: string;
  service: string;
  description: string;
  cost: number;
  etd: string;
}

export type DistributorOrderResponse = ApiResponse<DistributorOrderItem[]>;

export interface DistributorOrderItem {
  orderId: number;
  products: {
    id: number;
    name: string;
    price: number;
  }[];
  courier: string;
  shippingCost: number;
  notes: string;
  paymentProof: string;
  rejectReason: string;
  subTotalPayment: number;
  totalPayment: number;
}

export interface CreateOrderPayload {
  userId: string;
  courier: string;
  shippingCost: number;
  products: number[];
}

export type ValidDeliveryOptions =
  | "Truck Hotto"
  | "J&T Cargo"
  | "Central Cargo";
