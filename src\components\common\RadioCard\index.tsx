import {
  Box,
  Flex,
  Radio,
  RadioCardProps as MantineRadioCardProps,
  RadioIndicatorProps,
} from "@mantine/core";
import styles from "./style.module.css";
import clsx from "clsx";
import React, { forwardRef } from "react";

interface RadioCardProps extends MantineRadioCardProps {
  withIndicator?: boolean;
  indicatorProps?: RadioIndicatorProps;
}

const RadioCard = forwardRef<HTMLButtonElement, RadioCardProps>(
  (
    { children, withIndicator, className, p = "sm", indicatorProps, ...props },
    ref
  ) => {
    return (
      <Radio.Card
        {...{ p, ...props }}
        className={clsx(styles.root, className)}
        ref={ref}
      >
        <Flex align="center" gap="md">
          {withIndicator && <Radio.Indicator {...indicatorProps} />}
          <Box>{children}</Box>
        </Flex>
      </Radio.Card>
    );
  }
);

RadioCard.displayName = "RadioCard";

export default RadioCard;
