"use client";

import { Paper, Text, Group, Stack, Switch, Button } from "@mantine/core";
import { useState } from "react";
import classes from "./style.module.css";

export interface AgentApplicationAlertProps {
  isReceivingAgent?: boolean;
  quota?: number;
  quotaUsed?: number;
  onApplicationChange?: (open?: boolean) => void;
  onCompletionClick?: () => void;
}

const AgentApplicationAlert: React.FC<AgentApplicationAlertProps> = ({
  isReceivingAgent,
  quota,
  quotaUsed,
  onApplicationChange,
  onCompletionClick,
}) => {
  const [checked, setChecked] = useState(false);

  const handleApplicationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const check = e.target.checked;
    setChecked(check);
    if (onApplicationChange) {
      onApplicationChange(check);
    }
  };

  const TextLabel = isReceivingAgent ? (
    <Stack gap={0}>
      <Text fw={500}>
        Saat ini, Anda{" "}
        <Text
          component="span"
          c={`${checked ? "success" : "error"}.0`}
          td="underline"
        >
          {checked ? "sedang" : "tidak"} menerima agent.
        </Text>
        {checked && " Silakan lihat application yang ada di menu Request."}
      </Text>
      {quota && quotaUsed && (
        <Text c="text">
          {" "}
          Kuota agent: {quotaUsed}/{quota} terpenuhi
        </Text>
      )}
    </Stack>
  ) : (
    <Text fw={500}>
      Saat ini, Anda{" "}
      <Text component="span" c="error">
        belum menyelesaikan form pembukaan application agent.
      </Text>
    </Text>
  );

  const Action = isReceivingAgent ? (
    <Switch
      classNames={classes}
      checked={checked}
      onChange={handleApplicationChange}
      label="Open Application"
      size="lg"
    />
  ) : (
    <Button onClick={onCompletionClick}>Lengkapi</Button>
  );

  return (
    <Paper
      withBorder
      bg="primary.1"
      px={16}
      py={8}
      role="agent application alert"
      w="100%"
      style={{ borderColor: "var(--mantine-primary-color-filled)" }}
      variant="outline"
      h={"fit-content"}
    >
      <Group justify="space-between" align="center">
        {TextLabel}
        {Action}
      </Group>
    </Paper>
  );
};

export default AgentApplicationAlert;
