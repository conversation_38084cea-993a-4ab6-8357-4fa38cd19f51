import { useEffect, useState } from "react";
import UpdateLogsDetailsHeader, { UpdateLogsFilterValue } from "./Header";
import { Center, Flex, Skeleton, Stack, Text } from "@mantine/core";
import UpdateLogDetail from "../../../UpdateLogDetail";
import Loop from "@/components/common/Loop";
import { useRouter, useSearchParams } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { fetchUpdateLogs } from "@/store/slices/distributorAgentsSlice";
import { ITEMS_PER_PAGE, SortFilterValue } from "@/utils/constants";
import Pagination from "@/components/common/Pagination";

export interface UpdateLogsDetailsProps {
  uoc: string;
}

const UpdateLogsDetails: React.FC<UpdateLogsDetailsProps> = ({ uoc }) => {
  const params = useSearchParams();
  const router = useRouter();
  const dispatch: AppDispatch = useDispatch();

  const { updateLogsRequest, loading } = useSelector(
    (state: RootState) => state.distributorAgents
  );

  const logs = updateLogsRequest.updateLogs;
  const totalItems = updateLogsRequest.totalItems || 0;
  const totalPages = Math.max(1, Math.ceil(totalItems / ITEMS_PER_PAGE));

  const parsePageParam = (): number => {
    const pageParam = params?.get("page");
    if (!pageParam) return 1;

    const parsedPage = parseInt(pageParam);
    if (isNaN(parsedPage)) return 1;

    return Math.min(Math.max(1, parsedPage), totalPages);
  };

  const parseRequestFilter = (): UpdateLogsFilterValue => {
    const status = params?.get("status") as UpdateLogsFilterValue;
    return status && ["", "accept", "reject"].includes(status) ? status : "";
  };

  const parseSortFilter = (): SortFilterValue => {
    const sort = params?.get("sort") as SortFilterValue;
    return sort && ["desc", "asc"].includes(sort) ? sort : "desc";
  };

  const [page, setPage] = useState<number>(parsePageParam());
  const [requestFilter, setRequestFilter] = useState<UpdateLogsFilterValue>(
    parseRequestFilter()
  );
  const [sortFilter, setSortFilter] = useState<SortFilterValue>(
    parseSortFilter()
  );

  const updateURL = (updates: {
    page?: number;
    status?: UpdateLogsFilterValue;
    sort?: SortFilterValue;
  }) => {
    const currentPage = updates.page ?? page;
    const currentStatus = updates.status ?? requestFilter;
    const currentSort = updates.sort ?? sortFilter;

    const queryParams = new URLSearchParams();
    queryParams.set("page", currentPage.toString());
    if (currentStatus) queryParams.set("status", currentStatus);
    queryParams.set("sort", currentSort);

    router.replace(`?${queryParams.toString()}`, { scroll: false });
  };

  const handlePageChange = (newPage: number) => {
    const validPage = Math.min(Math.max(1, newPage), totalPages);
    setPage(validPage);
    updateURL({ page: validPage });
  };

  const handleRequestFilterChange = (newFilter: UpdateLogsFilterValue) => {
    setRequestFilter(newFilter);
    setPage(1); // Reset to first page when filter changes
    updateURL({ status: newFilter, page: 1 });
  };

  const handleSortFilterChange = (newSort: SortFilterValue) => {
    setSortFilter(newSort);
    setPage(1); // Reset to first page when sort changes
    updateURL({ sort: newSort, page: 1 });
  };

  useEffect(() => {
    const urlPage = parsePageParam();
    const urlStatus = parseRequestFilter();
    const urlSort = parseSortFilter();

    if (urlPage !== page) setPage(urlPage);
    if (urlStatus !== requestFilter) setRequestFilter(urlStatus);
    if (urlSort !== sortFilter) setSortFilter(urlSort);
  }, [params]);

  useEffect(() => {
    if (page >= 1 && page <= totalPages) {
      dispatch(fetchUpdateLogs(uoc, page, sortFilter, requestFilter));
    }
  }, [uoc, page, requestFilter, sortFilter]);

  return (
    <Stack gap="3em">
      <UpdateLogsDetailsHeader
        onRequestFilterChange={handleRequestFilterChange}
        onSortFilterChange={handleSortFilterChange}
        updateCount={totalItems}
        requestFilter={requestFilter}
        sortFilter={sortFilter}
      />
      {totalItems > 0 ? (
        <>
          <Stack gap="xl">
            {loading || !logs ? (
              <Loop count={ITEMS_PER_PAGE}>
                <Skeleton height={50} />
              </Loop>
            ) : (
              logs.map((log, index) => (
                <UpdateLogDetail key={index} log={log} />
              ))
            )}
          </Stack>
          <Flex mb="lg" justify="flex-end">
            <Pagination
              disabled={loading}
              page={page}
              total={totalPages}
              onChange={handlePageChange}
            />
          </Flex>
        </>
      ) : (
        <Center>
          <Text>Belum ada perubahan</Text>
        </Center>
      )}
    </Stack>
  );
};

export default UpdateLogsDetails;
