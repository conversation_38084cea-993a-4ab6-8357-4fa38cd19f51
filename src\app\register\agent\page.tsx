"use client";

import Footer from "@/components/common/Footer";
import AgentRegisterForms from "@/components/section/Register/Agent/Forms";
import Consideration from "@/components/section/Register/Distributor/Consideration";
import useBreakpoints from "@/hooks/useBreakpoints";
import { RootState } from "@/store";
import { Center, Stack, Image } from "@mantine/core";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useSelector } from "react-redux";

const RegisterAgentPage = () => {
  const router = useRouter();
  const { mobile } = useBreakpoints();
  const { success } = useSelector((state: RootState) => state.agentRegister);
  const { role, accessToken } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (accessToken) {
      window.location.href = "/";
    }
  }, [accessToken, role, router]);

  return (
    <Center p={{ base: 12, sm: 16, md: 24 }}>
      <Stack w="100%" gap="xl">
        <Image
          radius="md"
          src={`/images/register/hero${mobile ? "-mobile" : ""}.png`}
          alt="hero"
        />
        {success ? <Consideration role="agent" /> : <AgentRegisterForms />}
        <Footer pt={50} />
      </Stack>
    </Center>
  );
};

export default RegisterAgentPage;
