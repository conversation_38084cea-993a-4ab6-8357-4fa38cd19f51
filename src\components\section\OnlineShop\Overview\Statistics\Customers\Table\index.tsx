import {
  Flex,
  Stack,
  Table,
  Skeleton,
  Text,
  NumberFormatter,
  MantineStyleProp,
  Group,
} from "@mantine/core";
import Pagination from "@/components/common/Pagination";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { ITEMS_PER_PAGE } from "@/utils/constants";
import { isEmpty } from "@/utils/common-functions";
import { fetchTopPurchasingCustomers } from "@/store/slices/onlineShopSlices/overviewSlice";
import SectionHeader from "@/components/common/SectionHeader";
import { Star } from "@/utils/icons";
import useOverviewFilters from "@/hooks/useOverviewFilters";

const TABLE_HEADERS = [
  "Nomor Telepon",
  "Rata-Rata Rating Diberikan",
  "Frekuensi Pembelian",
  "Jumlah Produk Dibeli",
  "Region",
  "Jumlah Pembelian",
];

const CustomersStatisticsTable = ({ slug }: { slug?: string }) => {
  const dispatch: AppDispatch = useDispatch();
  const {
    topPurchasingCustomers: { customerStats, totalItems },
    loading: { topPurchasingCustomers: loading },
  } = useSelector((state: RootState) => state.overview);
  const { page, setPage } = useOverviewFilters();

  useEffect(() => {
    if (!loading) {
      dispatch(fetchTopPurchasingCustomers(page, ITEMS_PER_PAGE, slug));
    }
  }, [page, slug]);

  const tableStyle: MantineStyleProp = {
    borderTop: "1px solid var(--mantine-color-bg-gray-filled)",
    borderBottom: "1px solid var(--mantine-color-bg-gray-filled)",
  };

  const headerStyle: MantineStyleProp = {
    borderInlineEnd: "1px solid var(--mantine-color-bg-filled)",
  };

  const loadingRows = Array(5)
    .fill(null)
    .map((_, index) => (
      <Table.Tr key={index}>
        {Array(6)
          .fill(null)
          .map((_, cellIndex) => (
            <Table.Td key={cellIndex}>
              <Skeleton height={20} />
            </Table.Td>
          ))}
      </Table.Tr>
    ));

  const tableRows = !isEmpty(customerStats) ? (
    customerStats.map((data, index) => (
      <Table.Tr key={`customers-${index}`}>
        <Table.Td>{data.custPhoneNumber}</Table.Td>
        <Table.Td>
          <Group gap={5}>
            <Star />
            <span>{data.averageRating}/5</span>
          </Group>
        </Table.Td>
        <Table.Td>{data.purchaseFrequency}x</Table.Td>
        <Table.Td>{data.totalBoughtProducts}</Table.Td>
        <Table.Td>{data.region}</Table.Td>
        <Table.Td>
          <NumberFormatter value={data.totalSpend} />
        </Table.Td>
      </Table.Tr>
    ))
  ) : (
    <Table.Tr>
      <Table.Td colSpan={6} ta="center">
        <Text c="text" p="xl" mih="16em">
          Data yang anda cari tidak ditemukan!
        </Text>
      </Table.Td>
    </Table.Tr>
  );

  return (
    <Stack>
      <SectionHeader
        title="Top Purchasing Individuals"
        description="Berikut adalah pembeli di online shop Hotto yang diurutkan dari pembelian terbanyak."
      />

      <Table.ScrollContainer minWidth={768}>
        <Table
          withColumnBorders
          verticalSpacing="md"
          horizontalSpacing="md"
          style={tableStyle}
        >
          <Table.Thead>
            <Table.Tr c="text">
              {TABLE_HEADERS.map((header, index) => (
                <Table.Th key={index} style={headerStyle}>
                  {header}
                </Table.Th>
              ))}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{loading ? loadingRows : tableRows}</Table.Tbody>
        </Table>
      </Table.ScrollContainer>

      {!loading && totalItems !== undefined && (
        <Flex justify="flex-end" w="100%">
          <Pagination
            total={Math.ceil(totalItems / ITEMS_PER_PAGE)}
            page={page}
            onChange={setPage}
            disabled={loading}
          />
        </Flex>
      )}
    </Stack>
  );
};

export default CustomersStatisticsTable;
