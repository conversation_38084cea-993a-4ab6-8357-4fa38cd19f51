import { AppDispatch, RootState } from "@/store";
import { fetchProductList } from "@/store/slices/distributorRegisterSlice";
import { isEmpty, parseToRupiah } from "@/utils/common-functions";
import { Delete } from "@/utils/icons";
import {
  Stack,
  NativeSelect,
  Flex,
  Button,
  ActionIcon,
  Grid,
} from "@mantine/core";
import { useEffect, useMemo } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";

interface FormValues {
  products: Array<{ id: string }>;
}

const ProductSelection = ({
  onChange,
}: {
  onChange?: (products: FormValues["products"]) => void;
}) => {
  const dispatch: AppDispatch = useDispatch();
  const { productList, isLoading } = useSelector(
    (state: RootState) => state.distributorRegister
  );

  const form = useForm<FormValues>({
    mode: "onChange",
    defaultValues: {
      products: [{ id: "" }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    name: "products",
    control: form.control,
  });

  const disabled = useMemo(
    () => isLoading || isEmpty(productList),
    [isLoading, productList]
  );

  useEffect(() => {
    const fetch = async () => {
      if (isEmpty(productList)) {
        const products = await dispatch(fetchProductList(2));
        if (products) {
          form.setValue("products.0.id", products[0].id.toString());
        }
      }
    };
    const subscription = form.watch((value) => {
      onChange?.(value.products as FormValues["products"]);
    });
    fetch();
    return () => subscription.unsubscribe();
  }, []);

  return (
    <Stack gap="lg">
      {fields.map((field, index) => (
        <Grid columns={2} align="end" gutter="xs" key={field.id}>
          <Grid.Col span="auto">
            <NativeSelect
              {...form.register(`products.${index}.id`)}
              w="100%"
              required
              disabled={disabled}
              label={`Jumlah + Varian Produk ${index + 1}`}
              data={productList
                .filter((product) =>
                  index === 0 ? product.name.includes("120 Pouch") : true
                )
                .map((product) => ({
                  value: product.id.toString(),
                  label: `${product.name} (${parseToRupiah(product.price)})`,
                }))}
            />
          </Grid.Col>
          {index > 0 && (
            <Grid.Col span="content">
              <ActionIcon
                variant="subtle"
                size="xl"
                style={{ transform: "translate(0, 3px)" }}
                onClick={() => remove(index)}
              >
                <Delete size={30} />
              </ActionIcon>
            </Grid.Col>
          )}
        </Grid>
      ))}
      {fields.length < 2 && (
        <Flex justify="end" mt="lg">
          <Button
            disabled={disabled}
            variant="outline"
            onClick={() => append({ id: productList[0]?.id?.toString() || "" })}
          >
            Tambah
          </Button>
        </Flex>
      )}
    </Stack>
  );
};

export default ProductSelection;
