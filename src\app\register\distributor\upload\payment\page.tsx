"use client";

import Footer from "@/components/common/Footer";
import RegistrationFullModal from "@/components/section/Register/Distributor/RegistrationFullModal";
import DistributorUploadPayment from "@/components/section/Register/Distributor/Upload/Payment";
import useBreakpoints from "@/hooks/useBreakpoints";
import { RootState } from "@/store";
import { Center, Stack, Image } from "@mantine/core";
import { useQueryState } from "nuqs";
import { useEffect } from "react";
import { useSelector } from "react-redux";

const DistributorUploadPaymentPage = () => {
  const [token] = useQueryState("token");
  const [uoc] = useQueryState("uoc");
  const { mobile } = useBreakpoints();
  const { accessToken } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (!token || !uoc) {
      window.location.href = "/login";
      return;
    }
    if (accessToken) {
      window.location.href = "/";
      return;
    }
  }, [token, accessToken]);

  return (
    <>
      <Center p={{ base: 12, sm: 16, md: 24 }}>
        <Stack w="100%" gap="xl">
          <Image
            radius="md"
            src={`/images/register/hero${mobile ? "-mobile" : ""}.png`}
            alt="hero"
          />
          <DistributorUploadPayment token={token!} uoc={uoc!} />
          <Footer pt={50} />
        </Stack>
      </Center>
      <RegistrationFullModal />
    </>
  );
};

export default DistributorUploadPaymentPage;
