import { toast } from "sonner";
import * as yup from "yup";
import { MONTH_NAMES } from "./constants";
import { HISTORY_LIMIT } from "./constants";
import {
  AgentDistributorDetailsData,
  UserAgentDetail,
} from "./types/distributor-agent";
import { RegionData } from "./types/region";
import { ComboboxData, NumberFormatter } from "@mantine/core";

export const matchUrlPattern = (url: string, pattern: string): boolean => {
  const regexPattern = pattern.replace(/\*/g, ".*"); // Replace * with .*

  const regex = new RegExp("^" + regexPattern + "$");
  return regex.test(url);
};

export const passwordValidationLogin = yup
  .string()
  .required("Password harus diisi")
  .min(8, "Password harus minimal 8 karakter")
  .matches(/^(?=.*\d).*$/, "Password harus mengandung angka");

export const passwordValidationReset = yup
  .string()
  .required("Password harus diisi")
  .min(8, "Password harus minimal 8 karakter")
  .matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]).*$/,
    "Password harus terdiri atas kombinasi huruf besar, huruf kecil, angka, dan karakter spesial"
  );

export const phoneValidation = yup
  .string()
  .required("Nomor telepon harus diisi")
  .min(10, "Nomor telepon minimal 10 karakter")
  .max(16, "Nomor telepon maksimal 16 karakter")
  .matches(/^08\d+$/, "Nomor telepon tidak valid");

export const invalidLinksReasonsSchema = yup.object().shape({
  userID: yup.string().required("User ID harus diisi"),
  type: yup.string().required("Tipe harus diisi"),
  url: yup.string().required("URL harus diisi"),
  reason: yup.string().required("Alasan harus diisi"),
});

export const submitInvalidLinksReasonsSchema = yup.object().shape({
  invalidLinks: yup
    .array()
    .of(invalidLinksReasonsSchema)
    .min(1, "Minimal harus ada satu link yang di-submit"),
});

export const distributorRegisterSchema = yup.object().shape({
  name: yup.string().required("Nama harus diisi"),
  nik: yup
    .string()
    .matches(/^\d{16}$/, "NIK harus 16 digit angka")
    .required("NIK harus diisi"),
  npwp: yup
    .string()
    .required("NPWP harus diisi")
    .min(15, "NPWP minimal 15 angka")
    .max(16, "NPWP maksimal 16 angka"),
  email: yup.string().email().required("Email harus diisi"),
  phoneNumber: phoneValidation,
  island: yup.string().required("Pulau Domisili harus diisi"),
  province: yup
    .string()
    .required("Provinsi harus diisi")
    .matches(/^.*#.*$/, "Provinsi tidak valid"),
  regency: yup
    .string()
    .required("Kota harus diisi")
    .matches(/^.*#.*$/, "Kota tidak valid"),
  district: yup
    .string()
    .required("Kecamatan harus diisi")
    .matches(/^.*#.*$/, "Kecamatan tidak valid"),
  village: yup
    .string()
    .required("Kelurahan harus diisi")
    .matches(/^.*#.*$/, "Kelurahan tidak valid"),
  postalCode: yup
    .string()
    .required("Kodepos harus diisi")
    .matches(/^\d{5}$/, "Kode pos tidak valid"),
  address: yup.string().required("Alamat harus diisi"),
  warehouseIsland: yup.string().required("Pulau Warehouse harus diisi"),
  warehouseProvince: yup
    .string()
    .required("Provinsi Warehouse harus diisi")
    .matches(/^.*#.*$/, "Provinsi Warehouse tidak valid"),
  warehouseRegency: yup
    .string()
    .required("Kota Warehouse harus diisi")
    .matches(/^.*#.*$/, "Kota Warehouse tidak valid"),
  warehouseDistrict: yup
    .string()
    .required("Kecamatan Warehouse harus diisi")
    .matches(/^.*#.*$/, "Kecamatan Warehouse tidak valid"),
  warehouseVillage: yup
    .string()
    .required("Kelurahan Warehouse harus diisi")
    .matches(/^.*#.*$/, "Kelurahan Warehouse tidak valid"),
  warehousePostalCode: yup
    .string()
    .required("Kodepos Warehouse harus diisi")
    .matches(/^\d{5}$/, "Kode pos Warehouse tidak valid"),
  warehouseAddress: yup.string().required("Alamat Warehouse harus diisi"),
  adminPhoneNumber: yup
    .string()
    .matches(
      /^(08\d{8,14})?$/,
      "Nomor telepon tidak valid. Minimal 10 karakter, maksimal 16 karakter"
    ),
  idCardImage: yup.string().required("Foto KTP harus diisi"),
  selfieImage: yup.string().required("Foto selfie dengan KTP harus diisi"),
  tokopedia: yup.string(),
  shopee: yup.string(),
  tiktok: yup.string(),
});

export const agentRegisterSchema = yup.object().shape({
  name: yup.string().required("Nama harus diisi"),
  nik: yup
    .string()
    .matches(/^\d{16}$/, "NIK harus 16 digit angka")
    .required("NIK harus diisi"),
  email: yup.string().email().required("Email harus diisi"),
  phoneNumber: phoneValidation,
  island: yup.string().required("Pulau Domisili harus diisi"),
  province: yup
    .string()
    .required("Provinsi harus diisi")
    .matches(/^.*#.*$/, "Provinsi tidak valid"),
  regency: yup
    .string()
    .required("Kota harus diisi")
    .matches(/^.*#.*$/, "Kota tidak valid"),
  district: yup
    .string()
    .required("Kecamatan harus diisi")
    .matches(/^.*#.*$/, "Kecamatan tidak valid"),
  village: yup
    .string()
    .required("Kelurahan harus diisi")
    .matches(/^.*#.*$/, "Kelurahan tidak valid"),
  postalCode: yup
    .string()
    .required("Kodepos harus diisi")
    .matches(/^\d{5}$/, "Kode pos tidak valid"),
  address: yup.string().required("Alamat harus diisi"),
  warehouseIsland: yup.string().required("Pulau Warehouse harus diisi"),
  warehouseProvince: yup
    .string()
    .required("Provinsi Warehouse harus diisi")
    .matches(/^.*#.*$/, "Provinsi Warehouse tidak valid"),
  warehouseRegency: yup
    .string()
    .required("Kota Warehouse harus diisi")
    .matches(/^.*#.*$/, "Kota Warehouse tidak valid"),
  warehouseDistrict: yup
    .string()
    .required("Kecamatan Warehouse harus diisi")
    .matches(/^.*#.*$/, "Kecamatan Warehouse tidak valid"),
  warehouseVillage: yup
    .string()
    .required("Kelurahan Warehouse harus diisi")
    .matches(/^.*#.*$/, "Kelurahan Warehouse tidak valid"),
  warehousePostalCode: yup
    .string()
    .required("Kodepos Warehouse harus diisi")
    .matches(/^\d{5}$/, "Kode pos Warehouse tidak valid"),
  warehouseAddress: yup.string().required("Alamat Warehouse harus diisi"),
  adminPhoneNumber: yup
    .string()
    .matches(
      /^(08\d{8,14})?$/,
      "Nomor telepon tidak valid. Minimal 10 karakter, maksimal 16 karakter"
    ),
  idCardImage: yup.string().required("Foto KTP harus diisi"),
  selfieImage: yup.string().required("Foto selfie dengan KTP harus diisi"),
  tokopedia: yup.string(),
  shopee: yup.string(),
  tiktok: yup.string(),
  distributorId: yup
    .string()
    .matches(/^.+$/, "Distributor ID tidak boleh kosong")
    .required("Distributor ID harus diisi"),
});

export const extractMonthYearFromTimestamp = (timestamp: string): string => {
  const date = new Date(timestamp);
  return `${MONTH_NAMES[date.getMonth()]} ${date.getFullYear()}`;
};

export const validateFileSize = (file: File) => {
  const maxFileSize = 2 * 1024 * 1024; // 2MB
  if (file && file.size > maxFileSize) {
    toast.error("File size exceeded the limit of 2MB");
    return false;
  }
  return true;
};
/**
 * Check if a given string is a valid URL
 * @param {string} url URL string to check
 * @returns {boolean} true if the string is a valid URL, false otherwise
 */
export const isValidUrl = (url?: string): boolean => {
  if (!url) return false;
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Check if a given URL is alive (i.e. can be fetched successfully)
 * @param {string} url URL string to check
 * @returns {Promise<boolean>} true if the URL is alive, false otherwise
 */
export const isUrlAlive = async (url: string): Promise<boolean> => {
  if (!isValidUrl(url)) return false;
  try {
    await fetch(url, {
      method: "HEAD",
      mode: "no-cors",
      cache: "no-cache",
    });
    return true;
  } catch {
    return false;
  }
};

export const formatDateToString = (
  date: Date | string,
  format: "name" | "number" = "name"
) => {
  if (!date) return "";
  if (typeof date === "string") {
    date = new Date(date);
  }
  const year = date.getFullYear();
  const day = String(date.getDate()).padStart(2, "0");
  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const monthName = monthNames[date.getMonth()];
  if (format === "number") {
    return `${day}/${String(date.getMonth() + 1).padStart(2, "0")}/${year
      .toString()
      .slice(-2)}`;
  }
  return `${day} ${monthName} ${year}`;
};

export const formatTimeStamp = (isoString: string): string => {
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  const date = new Date(isoString);

  // Convert to WIB (UTC+7)
  const wibDate = new Date(date.getTime() + 7 * 60 * 60 * 1000);
  const day = String(wibDate.getUTCDate()).padStart(2, "0");
  const month = months[wibDate.getUTCMonth()];
  const year = wibDate.getUTCFullYear();
  const hours = String(wibDate.getUTCHours()).padStart(2, "0");
  const minutes = String(wibDate.getUTCMinutes()).padStart(2, "0");

  return `${day} ${month} ${year}; ${hours}:${minutes} WIB`;
};

export const pushHistory = (
  histories: string[],
  newHistory: string,
  historyLocalStorageKey?: string
): string[] => {
  const updatedHistories = [...histories];
  if (updatedHistories.length >= HISTORY_LIMIT) {
    updatedHistories.pop();
  }
  updatedHistories.unshift(newHistory);

  if (historyLocalStorageKey) {
    // store history to local storage
    if (typeof window !== "undefined") {
      localStorage.setItem(
        historyLocalStorageKey,
        JSON.stringify(updatedHistories)
      );
    }
  }

  return [...new Set(updatedHistories)];
};

export const formatField = (field: string): string => {
  const formattedFields: Record<string, string> = {
    name: "Nama",
    email: "Email",
    phone: "Nomor Telepon",
    address: "Alamat",
    npwp: "NPWP",
    nik: "NIK",
  };

  return formattedFields[field] || field;
};

/**
 * Capitalizes the first letter of each word in a string.
 *
 * @param {string} str - The input string to capitalize.
 * @returns {string} The string with the first letter of each word capitalized.
 */
export const capitalize = (str: string = "") => {
  return str.replace(/\b\w/g, (l) => l.toUpperCase());
};

export const validateAgentAttribute = (
  field: string,
  value: string,
  setError: (field: string, message: string) => void
) => {
  const requiredFields = [
    "name",
    "phoneNumber",
    "adminPhoneNumber",
    "email",
    "nik",
    "address",
    "warehouseAddress",
    "SK",
  ];

  // Check if field is required
  if (requiredFields.includes(field) && value.length === 0) {
    setError(field, `This field is required.`);
    return false;
  }

  // Additional validations for specific fields
  if (field === "phoneNumber" || field === "adminPhoneNumber") {
    if (!/^\d{10,15}$/.test(value)) {
      setError(field, "Invalid phone number format.");
      return false;
    }
  }

  if (field === "email") {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      setError(field, "Invalid Email format.");
      return false;
    }
  }

  if (field === "nik") {
    const nikRegex = /^\d{16}$/; // NIK must be 16 digits
    if (!nikRegex.test(value)) {
      setError(field, "NIK must be 16 digits.");
      return false;
    }
  }

  if (field === "npwp") {
    if (value) {
      const npwpRegex = /^\d{15,16}$/; // NPWP must be 15-16 digits
      if (!npwpRegex.test(value)) {
        setError(field, "Invalid NPWP format.");
        return false;
      }
    }
  }

  if (field === "tokopedia" || field === "shopee") {
    if (value && !isValidUrl(value)) {
      setError(field, "Invalid Marketplace URL.");
      return false;
    }
  }

  // If validation passes, clear the error for this field (if any)
  setError(field, ""); // Clear any existing error message
  return true;
};

export const getValueFromAgentDetails = (
  key: string,
  agentDetails: AgentDistributorDetailsData | UserAgentDetail
): unknown => {
  const keys = key.split(".").map((k) => (k === "shops" ? "shop" : k)); // because in agentDetails, the key for "shops" is replaced with "shop"
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let result: any = agentDetails; // Start with the root object
  for (const k of keys) {
    if (result && typeof result === "object" && k in result) {
      result = result[k];
    } else {
      return undefined;
    }
  }
  return result;
};

/**
 * Checks if an object has any value in it, including nested objects and empty strings.
 * @param {object} object The object to check.
 * @returns {boolean} True if the object has at least one value, false otherwise.
 */
export const checkObjectHasValue = (object: object): boolean => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const checkValue = (value: any): boolean => {
    if (value === undefined) {
      return false; // Ignore undefined values
    }
    if (typeof value === "object" && value !== null) {
      // If the value is an object, recursively check its properties
      return Object.values(value).some((v) => checkValue(v));
    }
    return true; // For primitive values (strings, numbers, etc.), we consider non-undefined as a change
  };

  return Object.values(object).some((value) => checkValue(value));
};

/**
 * Generates a random number between the specified minimum and maximum values, inclusive.
 *
 * @param {number} min - The minimum value of the range.
 * @param {number} max - The maximum value of the range.
 * @param {boolean} [asFloat=false] - If true, returns a float; otherwise, returns an integer.
 * @returns {number} A random number between min and max, inclusive.
 */
export const randomNumber = (
  min: number,
  max: number,
  options: {
    asFloat?: boolean;
  } = {}
): number => {
  const { asFloat = false } = options;

  const random = Math.random() * (max - min) + min;
  return asFloat ? random : Math.floor(random);
};

/**
 * Downloads a file from the specified URL.
 *
 * @param {string} fileUrl - The URL of the file to download.
 */
export const downloadFile = async (fileUrl: string) => {
  if (!fileUrl) return;
  try {
    const response = await fetch(fileUrl);
    const blob = await response.blob();
    const blobUrl = URL.createObjectURL(blob);

    const a = document.createElement("a");
    a.href = blobUrl;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(blobUrl);
  } catch (error) {
    toast.error("Failed to download file.");
  }
};

/**
 * Parses a time duration string and converts it to an Indonesian language format.
 *
 * @param {string} input - The time duration string (e.g., "7d", "12h", "7h30m").
 * @returns {string} - The parsed time in Indonesian (e.g., "7 hari", "12 jam", "7 jam 30 menit").
 *
 * @example
 * parseTime("7d");      // "7 hari"
 * parseTime("12h");     // "12 jam"
 * parseTime("7h30m");   // "7 jam 30 menit"
 * parseTime("2d5h15m"); // "2 hari 5 jam 15 menit"
 */
export const parseTimeToBahasa = (input: string) => {
  const regex = /(\d+d)?\s*(\d+h)?\s*(\d+m)?/;
  const match = input.match(regex);

  if (!match) return input; // Return original input if no match

  const days = match[1] ? parseInt(match[1]) + " hari" : "";
  const hours = match[2] ? parseInt(match[2]) + " jam" : "";
  const minutes = match[3] ? parseInt(match[3]) + " menit" : "";

  return [days, hours, minutes].filter(Boolean).join(" ");
};

/**
 * Generates a UUID v4 string.
 *
 * @returns {string} - A UUID v4 string with hyphens (xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx).
 */
export const generateUUID = (): string => {
  const hexDigits = new Array(36);
  for (let i = 0; i < 36; i++) {
    hexDigits[i] = Math.floor(Math.random() * 16).toString(16);
  }

  // Replace digits at specific positions as per UUID v4 format
  // Position 14: set the 4-bit version to 0100 (version 4)
  hexDigits[14] = "4";

  // Position 19: set the high-order bits of the clock_seq_hi_and_reserved to 10xx (variant 1)
  hexDigits[19] = ((parseInt(hexDigits[19], 16) & 0x3) | 0x8).toString(16);

  // Add hyphens at positions 8, 13, 18, and 23
  hexDigits[8] = hexDigits[13] = hexDigits[18] = hexDigits[23] = "-";

  // Join the array into a single string
  return hexDigits.join("");
};

/**
 * Converts an array of RegionData objects into a ComboboxData array.
 *
 * @param {RegionData[]} region - The array of RegionData objects to convert.
 * @param {boolean} [withPlaceholder=false] - If true, adds a placeholder option ("Select an option...") to the beginning of the array.
 * @returns {ComboboxData[]} A ComboboxData array, where each element is an object with a `value` and a `label` property.
 */
export const serializeRegion = (
  region: RegionData[],
  withPlaceholder?: boolean
): ComboboxData => {
  const regions = region.map(({ code, name }) => ({
    value: code + "#" + name,
    label: name,
  }));

  if (withPlaceholder) {
    return [{ value: "", label: "Select an option..." }, ...regions];
  }

  return regions;
};

/**
 * Parses a string into a Region object with a code and a name.
 *
 * The string is expected to be in the format "code#name", where
 * the "#" character is the default separator. If the separator is
 * specified, it is used to split the string into the code and name
 * components.
 *
 * @throws {Error} If the separator is not found in the input string.
 *
 * @param {string} input - The string to parse.
 * @param {string} [separator="#"] - The separator to use when splitting the string.
 * @returns {{ code: string; name: string }} - An object with a code and a name.
 */
export const parseRegion = (
  input: string,
  separator: string = "#"
): { code: string; name: string } => {
  const separatorIndex = input.indexOf(separator);

  if (separatorIndex === -1) {
    throw new Error(`Invalid format: missing separator "${separator}"`);
  }

  const code = input.substring(0, separatorIndex);
  const name = input.substring(separatorIndex + separator.length);

  return { code, name };
};

/**
 * Formats a number as Indonesian Rupiah (IDR).
 *
 * @param {number} amount - The number to format.
 * @param {Object} [options] - Optional parameters.
 * @param {boolean} [options.includePrefix=true] - Include the "Rp" prefix.
 * @param {number} [options.decimalPlaces=0] - The number of decimal places to round to.
 * @returns {string} The formatted number as a string.
 */
export const parseToRupiah = (
  amount: number,
  options: {
    includePrefix?: boolean;
    decimalPlaces?: number;
  } = {}
): string => {
  // Destructure options with default values
  const { includePrefix = true, decimalPlaces = 0 } = options;

  // Handle invalid input
  if (isNaN(amount)) {
    return "";
  }

  // Round to specified decimal places
  const roundedAmount = Number(amount.toFixed(decimalPlaces));

  // Format the number with thousand separators
  const formattedNumber = roundedAmount.toLocaleString("id-ID", {
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces,
  });

  // Add Rupiah prefix if specified
  return includePrefix ? `Rp${formattedNumber}` : formattedNumber;
};

/**
 * Deeply compares two values to determine if they are equivalent.
 * Works with primitive types, arrays, objects, dates, and more.
 *
 * @param a - First value to compare
 * @param b - Second value to compare
 * @returns boolean - True if the values are equivalent, false otherwise
 */
export const isEqual = (a: unknown, b: unknown): boolean => {
  if (a === b) return true;
  if (
    a === null ||
    b === null ||
    typeof a !== "object" ||
    typeof b !== "object"
  )
    return false;
  if (a instanceof Date && b instanceof Date) {
    return a.getTime() === b.getTime();
  }
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (!isEqual(a[i], b[i])) return false;
    }
    return true;
  }
  const keysA = Object.keys(a as object);
  const keysB = Object.keys(b as object);
  if (keysA.length !== keysB.length) return false;
  for (const key of keysA) {
    const objA = a as Record<string, unknown>;
    const objB = b as Record<string, unknown>;
    if (
      !Object.prototype.hasOwnProperty.call(objB, key) ||
      !isEqual(objA[key], objB[key])
    ) {
      return false;
    }
  }
  return true;
};

/**
 * Checks if a variable is empty
 * - `null` and `undefined` are considered empty
 * - Empty strings, arrays, maps, sets, and objects are considered empty
 * - The number 0 is not considered empty
 * - Boolean false is not considered empty
 *
 * @param value - The value to check
 * @returns True if the value is considered empty, false otherwise
 */
export const isEmpty = <T>(value: T): boolean => {
  if (value === null || value === undefined) {
    return true;
  }
  if (typeof value === "string") {
    return value.length === 0;
  }
  if (Array.isArray(value)) {
    return value.length === 0;
  }
  if (value instanceof Map || value instanceof Set) {
    return value.size === 0;
  }
  if (value !== null && typeof value === "object") {
    return Object.keys(value).length === 0;
  }
  return false;
};

/**
 * Converts a number to a string with a suffix (k for thousands, juta for millions).
 *
 * @param {number} number - The number to convert.
 * @returns {string} The converted number as a string.
 *
 * @example
 * numberToString(1000);    // returns "1.0 k"
 * numberToString(1000000); // returns "1.0 juta"
 */
export const numberToString = (number: number): string => {
  if (number >= 1000000) {
    // Convert to millions (juta)
    const millions = number / 1000000;
    return `${millions.toFixed(1)} juta`;
  } else if (number >= 1000) {
    // Convert to thousands (k)
    const thousands = number / 1000;
    return `${thousands.toFixed(1)} k`;
  }
  return number.toString();
};

/**
 * Returns the current date and time in Jakarta timezone.
 *
 * @returns {string} The current date and time in Jakarta timezone.
 *
 * @example
 * getTodayDateTime(); // returns "12 Agustus 2023 10:30 WIB"
 */
export const getTodayDateTime = (withTime: boolean = true): string => {
  const jakartaDate = new Date(
    new Date().toLocaleString("en-US", { timeZone: "Asia/Jakarta" })
  );

  const dateOptions: Intl.DateTimeFormatOptions = {
    day: "2-digit",
    month: "long",
    year: "numeric",
    ...(withTime && {
      hour: "2-digit",
      minute: "2-digit",
    }),
  };

  const formatted = jakartaDate
    .toLocaleDateString("id-ID", dateOptions)
    .replace(/(\d+)/, (match) => match.padStart(2, "0"))
    .replace(" pukul", "");

  return withTime ? formatted + " WIB" : formatted;
};

/**
 * Formats a date string as a localized date string.
 *
 * @param {string} date - The date string to format, in ISO 8601 format.
 * @param {string} locales - The locale string to format the date string for.
 * @param {Intl.DateTimeFormatOptions} options - The options to use when formatting the date string.
 * @returns {string} The formatted date string.
 *
 * @example
 * formatDate("2023-08-12T10:30:00.000Z"); // returns "12 Ags 2023"
 */
export const formatDate = (
  date: number | string | Date,
  locales: Intl.LocalesArgument = "id-ID",
  options: Intl.DateTimeFormatOptions = {
    day: "2-digit",
    month: "short",
    year: "numeric",
    timeZone: "Asia/Jakarta",
  }
): string => {
  try {
    return new Intl.DateTimeFormat(locales, options).format(new Date(date));
  } catch {
    return "";
  }
};

/**
 * Formats a number as a percentage string.
 *
 * @param {number} percentage - The percentage value to format.
 * @returns {string} A formatted percentage string.
 *
 * @example
 * formatPercentage(0);    // returns "0.00%"
 * formatPercentage(5.5);  // returns "+5.5%"
 * formatPercentage(-3.2); // returns "-3.2%"
 */
export const formatPercentage = (percentage: number) => {
  const fixed = Number(percentage).toFixed(2);
  if (percentage < 0) return `${fixed}%`;
  if (percentage > 0) return `+${fixed}%`;
  return "0.00%";
};

/**
 * Returns the color for a percentage value.
 *
 * @param {number} percentage - The percentage value.
 * @returns {string} The color for the percentage value.
 *
 * @example
 * getPercentageColor(0);    // returns "text"
 * getPercentageColor(5.5);  // returns "success"
 * getPercentageColor(-3.2); // returns "error"
 */
export const getPercentageColor = (percentage: number) => {
  if (percentage < 0) return "error";
  if (percentage > 0) return "success";
  return "text";
};

/**
 * Formats a number based on the specified format type.
 *
 * @param {number} value - The number to format.
 * @param {"currency" | "percentage" | "rating"} formatAs - The format type to apply.
 * @returns {React.ReactNode} The formatted number as a React node.
 *
 * @example
 * formatNumber(1000, "currency");    // returns "Rp1,000"
 * formatNumber(75, "percentage");    // returns "75%"
 * formatNumber(4.5, "rating");       // returns "4.5/5.0"
 */
export const formatNumber = (
  value: number,
  formatAs: "currency" | "percentage" | "rating"
): string => {
  if (isEmpty(value)) return "";
  if (typeof value !== "number") return value;

  switch (formatAs) {
    case "currency":
      return parseToRupiah(value);
    case "percentage":
      return `${Number(value).toFixed(2)}%`;
    case "rating":
      return `${Number(value).toFixed(1)}/5.0`;
    default:
      return value.toString();
  }
};

/**
 * Formats a number of bytes into a human-readable string with appropriate units (KB, MB, GB)
 * @param bytes - The number of bytes to format
 * @param decimals - Number of decimal places to show (default: 2)
 * @returns Formatted string with appropriate unit
 */
export function formatBytes(bytes: number = 0, decimals: number = 2): string {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const formattedValue = parseFloat((bytes / Math.pow(k, i)).toFixed(decimals));
  return `${formattedValue} ${sizes[i]}`;
}

/**
 * Checks if a city is in Jabodetabek area.
 *
 * @param {string} city - The name of the city to check.
 * @returns {boolean} True if the city is in Jabodetabek, false otherwise.
 *
 * @example
 * isJabodetabek("Jakarta"); // returns true
 * isJabodetabek("Bandung"); // returns false
 */
export const isJabodetabek = (city: string): boolean => {
  if (isEmpty(city)) return false;
  const jabodetabekKeywords = [
    "jakarta",
    "bogor",
    "depok",
    "tangerang",
    "tangsel",
    "bekasi",
  ];
  const cityLower = city.toLowerCase().trim();
  return jabodetabekKeywords.some((keyword) => cityLower.includes(keyword));
};
