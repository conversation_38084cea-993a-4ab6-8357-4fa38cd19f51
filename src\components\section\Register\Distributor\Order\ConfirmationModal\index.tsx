import ButtonGroup from "@/components/common/ButtonGroup";
import ResponsiveModal from "@/components/common/ResponsiveModal";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import {
  Box,
  Stack,
  Text,
  Title,
  Paper,
  Button,
  Divider,
  NumberFormatter,
  Group,
} from "@mantine/core";
import { createOrder } from "@/store/slices/distributorRegisterSlice";
import { Fragment, useMemo } from "react";
import { DISTRIBUTOR_REGISTRATION_DATA_VAR } from "@/utils/constants";
import { useRouter } from "next/navigation";

const ConfirmationModal = ({
  userId,
  opened,
  onClose,
}: {
  userId?: string;
  opened: boolean;
  onClose: () => void;
}) => {
  const router = useRouter();
  const dispacth: AppDispatch = useDispatch();
  const {
    isLoading,
    productList,
    order: { products, shippingOption, shippingCost },
  } = useSelector((state: RootState) => state.distributorRegister);

  const produtcsToOrder = useMemo(() => {
    return products.map((product) => {
      const productData = productList.find(
        (item) => item.id === Number(product.id)
      );
      return {
        id: productData?.id,
        name: productData?.name,
        price: productData?.price,
      };
    });
  }, [products, productList]);

  const handleSubmit = async () => {
    if (!userId) return;
    const success = await dispacth(createOrder(userId));
    if (success) {
      localStorage.removeItem(DISTRIBUTOR_REGISTRATION_DATA_VAR);
      router.push("/register/finished?status=considered&role=distributor");
      onClose();
    }
  };

  return (
    <ResponsiveModal
      opened={opened}
      onClose={onClose}
      modalProps={{ padding: "lg", size: 566 }}
      drawerProps={{ padding: "lg" }}
      loading={isLoading}
    >
      <Stack gap="xl3">
        <Box ta="center">
          <Title order={2}>
            Anda akan memesan produk ini sebagai pesanan Anda!
          </Title>
          <Text c="text-light" mt="sm">
            Silakan konfirmasi pesanan Anda
          </Text>
        </Box>

        <Box>
          {produtcsToOrder.map((product, index) => (
            <Fragment key={product.name + "-" + index}>
              <Group justify="space-between">
                <Text>{product.name}</Text>
                <Box ta="end">
                  <Text fz="lg" fw={500}>
                    <NumberFormatter value={product.price} />
                  </Text>
                  <Text c="text-light" td="line-through">
                    <NumberFormatter value={product.price} />
                  </Text>
                </Box>
              </Group>
              <Divider my="xs" />
            </Fragment>
          ))}
          <Group justify="space-between">
            <Text>Estimasi Pengiriman {shippingOption}</Text>
            <Text fz="lg" fw={500}>
              {shippingCost < 0 ? (
                "TBA"
              ) : shippingCost === 0 ? (
                "FREE"
              ) : (
                <NumberFormatter value={shippingCost} />
              )}
            </Text>
          </Group>
        </Box>

        <Paper
          py="sm"
          px="lg"
          bg="error.1"
          c="error"
          mx="-lg"
          radius="8px 8px 0 0"
        >
          <Text ta="center">
            Pesanan yang telah dibuat tidak dapat diganti dengan alasan apapun.
          </Text>
        </Paper>
        <ButtonGroup justify="center">
          <Button c="black" variant="subtle" onClick={onClose}>
            Periksa Kembali
          </Button>
          <Button loading={isLoading} onClick={handleSubmit}>
            Pesan
          </Button>
        </ButtonGroup>
      </Stack>
    </ResponsiveModal>
  );
};

export default ConfirmationModal;
