import { Divider, Flex, Text, Title } from "@mantine/core";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import PastBroadcastCard from "./PastBroadcastCard";
import { fetchBroadcasts } from "@/store/slices/adminBroadcastSlice";
import useBreakpoints from "@/hooks/useBreakpoints";
import { BroadcastRecipient } from "@/utils/types/broadcast";

const PastBroadcastList = () => {
  const dispatch: AppDispatch = useDispatch();
  const { pastBroadcasts } = useSelector(
    (state: RootState) => state.adminBroadcast
  );
  useEffect(() => {
    dispatch(fetchBroadcasts());
  }, [dispatch]);

  const { tablet, mobile } = useBreakpoints();

  return (
    <Flex
      direction={"column"}
      w={tablet || mobile ? "100%" : "55%"}
      gap={"md"}
      pb={tablet || mobile ? "3em" : "md"}
    >
      <Title order={5}>Broadcast Sebelumnya</Title>
      <Divider />
      {pastBroadcasts.length > 0 ? (
        <Flex direction={"column"} gap={"xs"}>
          {pastBroadcasts.map((value, index) => (
            <PastBroadcastCard
              key={index}
              {...value}
              recipients={value.recipientsDetail as BroadcastRecipient[]}
            />
          ))}
        </Flex>
      ) : (
        <Text c={"text-light"}>Belum ada broadcast sebelumnya</Text>
      )}
    </Flex>
  );
};

export default PastBroadcastList;
