import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { useState } from "react";
import OrderSectionForm from "./Form";
import { Stack, Text } from "@mantine/core";
import { setFirstOrderToUpdate } from "@/store/slices/adminRequestSlice";
import { useAdminRequestContext } from "../../../../../../context/AdminRequestContext";

const OrderSection = () => {
  const dispatch: AppDispatch = useDispatch();
  const { applicationRequest: data } = useSelector(
    (state: RootState) => state.adminRequest
  );
  const [activeOrder, setActiveOrder] = useState(0);
  const { submitRequest, closeModal } = useAdminRequestContext();
  const orders = data?.newAgent?.firstOrder;

  if (!orders || !data) return "No data";

  const handleSubmit = async (id: number, cost: number) => {
    dispatch(
      setFirstOrderToUpdate({
        orderId: id,
        shippingCost: cost,
      })
    );
    if (activeOrder + 1 < orders.length) {
      setActiveOrder(activeOrder + 1);
      return;
    }
    submitRequest?.("accept");
  };

  const handlePrev = () => {
    if (activeOrder > 0) {
      setActiveOrder(activeOrder - 1);
      return;
    }
    closeModal?.();
  };

  return orders.map((order, index) => {
    return (
      activeOrder === index && (
        <Stack gap="xl" key={index}>
          <Text ta="center" c="text-light">
            Silakan review biaya ongkir orderan{" "}
            {index === 0 ? "pertama" : "kedua"}.
          </Text>
          <OrderSectionForm
            order={order}
            address={data.newAgent.warehouseAddress || ""}
            onChange={handleSubmit}
            onPrev={handlePrev}
          />
        </Stack>
      )
    );
  });
};

export default OrderSection;
