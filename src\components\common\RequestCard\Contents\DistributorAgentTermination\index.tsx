import { Divider, Flex, Text } from "@mantine/core";
import RequestCardHeader from "../../RequestCardHeader";
import { RequestCardData } from "@/utils/types/request";
import { useMediaQuery } from "@mantine/hooks";
import ReviewStatus from "@/components/common/ReviewStatus";

const DistributorAgentTerminationRequestCard = ({
  ...request
}: RequestCardData) => {
  const isMobile = useMediaQuery("(max-width: 768px)");

  const renderAgentName = () => {
    if (request.requesterRole === "Agent") {
      return request.requesterName;
    }
    return request.metadatas[0].oldValue;
  };

  return (
    <Flex direction={"column"}>
      <RequestCardHeader
        leftText={`Pemutusan Hubungan Kerja Agent`}
        rightText={`Dikirim`}
        updatedAt={request.updatedAt}
        rightItalic={false}
      />
      <Divider />
      {request.metadatas[0] ? (
        <Flex
          direction={isMobile ? "column" : "row"}
          align={isMobile ? "flex-start" : "center"}
          justify={"space-between"}
          p={"lg"}
          gap={"lg"}
        >
          <Flex direction={"column"} gap={"xs"}>
            <Text fw={500}>{`Agent: ${renderAgentName()}`}</Text>
            <Text fw={500} c={"text-light"}>{`Alasan: ${request.reason}`}</Text>
          </Flex>

          <Flex w={isMobile ? "100%" : "fit-content"} justify={"flex-end"}>
            {request.metadatas[0].status === "pending" ? (
              <Text fw={400} fs={"italic"} ta={"right"}>
                Sedang direview
              </Text>
            ) : (
              <ReviewStatus status={request.metadatas[0].status} />
            )}
          </Flex>
        </Flex>
      ) : (
        <Text c={"text-light"} p={"lg"} fw={500}>
          Tidak ada detail data
        </Text>
      )}
    </Flex>
  );
};

export default DistributorAgentTerminationRequestCard;
