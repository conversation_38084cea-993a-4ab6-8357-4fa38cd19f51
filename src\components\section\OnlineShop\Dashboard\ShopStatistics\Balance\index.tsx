import SectionHeader from "@/components/common/SectionHeader";
import { RootState } from "@/store";
import {
  Anchor,
  Flex,
  NumberFormatter,
  Paper,
  Skeleton,
  Stack,
  Text,
} from "@mantine/core";
import { useSelector } from "react-redux";

const BalanceContent = ({
  balance,
  balanceUrl,
}: {
  balance: number;
  balanceUrl: string;
}) => (
  <Paper p="lg" bg="bg">
    <Stack gap={0}>
      <Text fw={500} c="text-light" size="sm">
        Saldo
      </Text>
      <Flex gap="sm" justify="space-between" w="100%" align="center">
        <Text fw={600} size="lg">
          <NumberFormatter value={balance} />
        </Text>
        <Anchor href={balanceUrl} size="sm">
          Tarik
        </Anchor>
      </Flex>
    </Stack>
  </Paper>
);

const Balance = () => {
  const {
    shopBalance,
    loading: { shopBalance: loading },
  } = useSelector((state: RootState) => state.dashboard);

  const isAdmin = window.location.pathname.includes("/admin/");
  const balanceUrl = isAdmin
    ? "/admin/shop/dashboard/balance"
    : "/shop/dashboard/balance";

  const BalanceSkeleton = () => (
    <Paper p="lg" bg="bg">
      <Stack gap={0}>
        <Skeleton w="40px" height="12px" />
        <Flex gap="sm" justify="space-between" w="100%" align="center">
          <Skeleton w="80px" height="24px" mt="xs" />
        </Flex>
      </Stack>
    </Paper>
  );

  return (
    <Stack>
      <SectionHeader
        title="Saldo Anda"
        description="Jumlah total yang dicairkan sekarang"
        loading={loading}
      />
      {loading ? (
        <BalanceSkeleton />
      ) : (
        <BalanceContent balance={shopBalance || 0} balanceUrl={balanceUrl} />
      )}
    </Stack>
  );
};

export default Balance;
