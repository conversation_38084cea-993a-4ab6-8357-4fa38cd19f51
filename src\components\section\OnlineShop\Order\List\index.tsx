import {
  Center,
  Loader,
  Stack,
  Text,
  ScrollArea,
  Flex,
  Box,
  Paper,
  Group,
  Button,
} from "@mantine/core";
import OrderCard from "../Card";
import OrderListHeader from "./Header";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";
import {
  fetchOrders,
  setFilters,
  setSelectedOrders,
} from "@/store/slices/onlineShopSlices/orderListSlice";
import {
  parseAsInteger,
  parseAsString,
  parseAsStringLiteral,
  useQueryState,
} from "nuqs";
import { OrderStatuses } from "@/utils/types/online-shop";
import { ORDER_STATUS } from "../Filter/Status";
import {
  useDebouncedValue,
  useDisclosure,
  useElementSize,
} from "@mantine/hooks";
import OrderPaymentConfirmationModal from "../Modal/PaymentConfirmation";
import OrderResiInputModal from "../Modal/ResiInput";
import { isEmpty } from "@/utils/common-functions";
import Pagination from "@/components/common/Pagination";
import { ITEMS_PER_PAGE } from "@/utils/constants";
import useBreakpoints from "@/hooks/useBreakpoints";
import Placeholder from "@/components/common/Placeholder";
import ComplaintModal from "../Modal/Complaint";

const OrderList: React.FC = () => {
  const dispatch: AppDispatch = useDispatch();
  const {
    data: { orders, totalItems },
    loading,
    selectedOrders,
  } = useSelector((state: RootState) => state.orderList);

  const { ref, height } = useElementSize();
  const { mobile } = useBreakpoints();

  const [evidenceModalOpened, evidenceModalHandlers] = useDisclosure(false);
  const [evidence, setEvidence] = useState({
    id: 0,
    image: "",
    viewOnly: true,
  });

  const [resiModalOpened, resiModalHandlers] = useDisclosure(false);
  const [resiInput, setResiInput] = useState({
    id: -1,
    resiNumber: "",
  });

  const [complaintModalOpened, complaintModalHandlers] = useDisclosure(false);
  const [orderId, setOrderId] = useState("");

  const [page, setPage] = useQueryState("page", parseAsInteger.withDefault(1));
  const [status] = useQueryState(
    "status",
    parseAsStringLiteral(OrderStatuses).withDefault(ORDER_STATUS[0].value)
  );
  const [order] = useQueryState(
    "order",
    parseAsStringLiteral(["desc", "asc"] as const).withDefault("desc")
  );
  const [search] = useQueryState("search", parseAsString.withDefault(""));
  const [debouncedSearch] = useDebouncedValue(search, 500);

  useEffect(() => {
    dispatch(
      setFilters({
        status,
        order,
        page,
        search: debouncedSearch,
      })
    );
    dispatch(fetchOrders(status, page, order, ITEMS_PER_PAGE, debouncedSearch));
  }, [status, order, debouncedSearch, page]);

  useEffect(() => {
    setPage(1);
  }, [status, order, debouncedSearch]);

  const handleSelect = (id: number, selected: boolean) => {
    dispatch(
      setSelectedOrders(
        selected
          ? [...selectedOrders, id]
          : selectedOrders.filter((i) => i !== id)
      )
    );
  };

  const handlePageChange = (newPage: number) => {
    const validPage = Math.min(Math.max(1, newPage), 100);
    setPage(validPage);
  };

  const empty = (!orders || orders.length === 0) && isEmpty(debouncedSearch);

  const LabelDownloadAction = (
    <Box>
      <Box h={height + 20} />
      <Paper
        ref={ref}
        pos="fixed"
        bottom={0}
        left={0}
        miw="100%"
        p={{ base: "sm", md: "md" }}
        radius={0}
        style={{ borderTop: "1px solid var(--mantine-color-bg-gray-filled)" }}
      >
        <Group justify="space-between" ml={{ md: 295 }}>
          <Text fw={500}>{selectedOrders.length} pesanan dipilih</Text>
          <Button
            variant="outline"
            fullWidth={mobile}
            mb={{ base: "sm", md: 0 }}
          >
            Download Label ({selectedOrders.length})
          </Button>
        </Group>
      </Paper>
    </Box>
  );

  return (
    <>
      <OrderPaymentConfirmationModal
        opened={evidenceModalOpened}
        onClose={evidenceModalHandlers.close}
        viewOnly={evidence.viewOnly}
        data={{
          id: evidence.id,
          evidence: evidence.image,
        }}
      />

      <OrderResiInputModal
        opened={resiModalOpened}
        onClose={resiModalHandlers.close}
        data={resiInput}
      />

      <ComplaintModal
        opened={complaintModalOpened}
        onClose={complaintModalHandlers.close}
        orderId={orderId}
      />

      <Stack>
        {loading.orders ? (
          <Center mt="30dvh">
            <Loader size="xl" type="dots" />
          </Center>
        ) : orders.length === 0 ? (
          <Placeholder
            mt="20dvh"
            label="order"
            description="Silahkan tunggu data dari customer terlebih dahulu"
            empty={empty}
          />
        ) : (
          <>
            <ScrollArea>
              <Stack miw={1024}>
                <OrderListHeader />
                <Stack>
                  {orders.map((order) => (
                    <OrderCard
                      key={order.id}
                      data={order}
                      checked={selectedOrders.includes(order.id)}
                      onCheckboxChange={(checked) =>
                        handleSelect(order.id, checked)
                      }
                      onEvidenceClick={(viewOnly) => {
                        setEvidence({
                          id: order.id,
                          image: order.evidence,
                          viewOnly: viewOnly,
                        });
                        evidenceModalHandlers.open();
                      }}
                      onResiInputClick={() => {
                        setResiInput({
                          id: order.id,
                          resiNumber: order.resiNumber || "",
                        });
                        resiModalHandlers.open();
                      }}
                      onComplaintClick={(orderId) => {
                        setOrderId(orderId);
                        complaintModalHandlers.open();
                      }}
                    />
                  ))}
                </Stack>
              </Stack>
            </ScrollArea>
            <Flex my="lg" justify="flex-end">
              <Pagination
                page={page}
                total={Math.ceil(totalItems / ITEMS_PER_PAGE)}
                onChange={handlePageChange}
              />
            </Flex>
          </>
        )}
      </Stack>

      {selectedOrders.length > 0 && LabelDownloadAction}
    </>
  );
};

export default OrderList;
