import SectionHeader from "@/components/common/SectionHeader";
import { Flex, NumberFormatter, Skeleton, Stack, Text } from "@mantine/core";
import StatisticCard from "./StatisticsCard";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { getTodayDateTime, isEmpty } from "@/utils/common-functions";

const ShopStatistic = () => {
  const {
    shopStats,
    loading: { shopStats: loading },
  } = useSelector((state: RootState) => state.dashboard);

  const statistics = {
    views: shopStats?.totalProductSeen ?? 0,
    sold: shopStats?.totalProductSold ?? 0,
    revenue: <NumberFormatter value={shopStats?.totalRevenue ?? 0} />,
  };

  const LastUpdate = ({ loading }: { loading?: boolean }) => (
    <Flex gap="4px">
      <Text c="text-light" size="sm">
        {!loading && "Update terakhir: "}
      </Text>
      {loading ? (
        <Skeleton height={16} width="150px" />
      ) : (
        <Text c="text-light" size="sm" fw={700}>
          {isEmpty(shopStats) ? "Gagal memuat data" : getTodayDateTime()}
        </Text>
      )}
    </Flex>
  );

  return (
    <Stack>
      <SectionHeader
        title="Statistik Toko"
        description={<LastUpdate loading={loading} />}
        loading={loading}
      />
      <Flex justify="space-between" gap="xs">
        <StatisticCard
          label="Produk Dilihat"
          value={statistics.views}
          loading={loading}
        />
        <StatisticCard
          label="Produk Terjual"
          value={statistics.sold}
          loading={loading}
        />
        <StatisticCard
          label="Revenue"
          value={statistics.revenue}
          loading={loading}
        />
      </Flex>
    </Stack>
  );
};

export default ShopStatistic;
