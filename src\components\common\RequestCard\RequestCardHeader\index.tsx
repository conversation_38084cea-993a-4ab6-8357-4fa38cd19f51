import { formatTimeStamp } from "@/utils/common-functions";
import { Flex, Text } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";

interface RequestCardHeaderProps {
  updatedAt?: string;
  createdAt?: string;
  leftText: string;
  rightText?: string;
  rightItalic?: boolean;
  size?: "sm" | "md";
  leftCaption?: string;
}

const RequestCardHeader = ({
  updatedAt,
  createdAt,
  leftText,
  rightText,
  rightItalic = true,
  size = "md",
  leftCaption,
}: RequestCardHeaderProps) => {
  const isMobile = useMediaQuery("(max-width: 768px)");

  return (
    <Flex direction={"row"} justify={"space-between"} p={"lg"}>
      <Flex direction={"column"} gap={size === "sm" ? "0" : "sm"}>
        <Text fw={600}>{leftText}</Text>
        {createdAt && (
          <Text c="text-light" fw={500} size={size}>
            {formatTimeStamp(createdAt)}
          </Text>
        )}
        {leftCaption && (
          <Text c="text-light" fw={500} size={size}>
            {leftCaption}
          </Text>
        )}
      </Flex>
      {updatedAt && !isMobile && (
        <Flex
          direction={"column"}
          gap={size === "sm" ? "0" : "sm"}
          c={"text-light"}
          align={"flex-end"}
          ta={"right"}
        >
          <Text fs={!rightItalic ? "normal" : "italic"} fw={300} size={size}>
            {rightText}
          </Text>
          <Text fw={500} size={size}>
            {formatTimeStamp(updatedAt)}
          </Text>
        </Flex>
      )}
    </Flex>
  );
};

export default RequestCardHeader;
