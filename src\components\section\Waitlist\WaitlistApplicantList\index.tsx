import { RootState } from "@/store";
import { Divider, Flex, Stack, Text, Button } from "@mantine/core";
import { useSelector } from "react-redux";
import ApplicantCard from "./ApplicantCard";
import ApplicantListSkeleton from "./ListSkeleton";
import { useState } from "react";

interface WaitlistApplicantListProps {
  role: "agent" | "distributor";
}

const WaitlistApplicantList = ({ role }: WaitlistApplicantListProps) => {
  const { agentWaitlist, distributorWaitlist, loading } = useSelector(
    (state: RootState) => state.waitlist
  );

  const [showAll, setShowAll] = useState(false); // if !showAll only show 3 applicants
  const applicants =
    role === "agent"
      ? agentWaitlist?.users || []
      : distributorWaitlist?.users || [];
  const applicantCount = applicants.length;

  const renderContent = () => {
    if (loading) {
      return <ApplicantListSkeleton />;
    }

    if (!applicants?.length) {
      return (
        <Text ta="center" c="text-light">
          No applicants
        </Text>
      );
    }

    return (
      <Stack>
        {applicants
          .slice(0, showAll ? applicants.length : 3)
          .map((applicant, idx) => (
            <ApplicantCard key={idx} applicant={applicant} />
          ))}
      </Stack>
    );
  };

  return (
    <Stack>
      <Flex justify="space-between">
        <Text fw={500}>{`# of ${
          role.charAt(0).toUpperCase() + role.slice(1)
        } on waitlist`}</Text>
        <Text fw={500}>{applicantCount}</Text>
      </Flex>
      <Divider />
      {renderContent()}
      {!showAll && applicantCount > 3 && (
        <Flex justify="center" w={"100%"} mt={"md"}>
          <Button variant="transparent" onClick={() => setShowAll(true)}>
            Show All
          </Button>
        </Flex>
      )}
    </Stack>
  );
};

export default WaitlistApplicantList;
