export type RegionType = "provinces" | "regencies" | "districts" | "villages";

export interface IslandResponse {
  status_code: number;
  message: string;
  data: {
    islands: IslandData[];
  };
}

export interface RegionResponse {
  data: RegionData[];
  meta: RegionMetadata;
}

export interface IslandData {
  name: string;
  provinceAreaCodes: number[];
}

export interface RegionData {
  code: string;
  name: string;
  postal_code?: string;
}

export interface RegionMetadata {
  administrative_area_level: number;
  updated_at: string;
}
