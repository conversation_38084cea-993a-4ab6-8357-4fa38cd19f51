import { AppDispatch, RootState } from "@/store";
import {
  Center,
  Stack,
  Stepper,
  Title,
  Button,
  Text,
  Loader,
} from "@mantine/core";
import React, { useEffect, useState, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import UploadPaymentForm from "./Form";
import SuccessUploadPayment from "./Success";
import {
  fetchOrders,
  submitPaymentProof,
} from "@/store/slices/distributorRegisterSlice";
import { MAX_WIDTH } from "@/utils/constants";
import ButtonGroup from "@/components/common/ButtonGroup";
import { isEmpty } from "@/utils/common-functions";
import ShippingPolicy from "./ShippingPolicy";

interface DistributorUploadPaymentProps {
  uoc: string;
  token: string;
}

const DistributorUploadPayment = ({
  uoc,
  token,
}: DistributorUploadPaymentProps) => {
  const dispatch: AppDispatch = useDispatch();
  const { isLoading, orderList } = useSelector(
    (state: RootState) => state.distributorRegister
  );
  const [active, setActive] = useState(0);
  const [success, setSuccess] = useState(false);
  const [continuable, setContinuable] = useState(false);
  const rejected = useMemo(
    () => orderList.some((order) => !isEmpty(order.rejectReason)),
    [orderList]
  );
  const STEPS = useMemo(() => {
    const filteredOrders = rejected
      ? orderList.filter((order) => !isEmpty(order.rejectReason))
      : orderList;
    return filteredOrders.map((order, index) => ({
      label: `Order ${index + 1}`,
      step: <UploadPaymentForm order={order} onFormValid={setContinuable} />,
    }));
  }, [orderList, rejected]);

  const shouldContinue = rejected
    ? active + 1 < STEPS.length
    : active + 1 <= STEPS.length;

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [active]);

  useEffect(() => {
    const fetch = async () => {
      if (isEmpty(orderList) && !isEmpty(uoc)) {
        const valid = await dispatch(fetchOrders(uoc));
        if (!valid) {
          window.location.replace("/register/distributor");
          return;
        }
      }
    };
    fetch();
  }, [token]);

  const handleNext = async () => {
    if (shouldContinue) {
      setActive(active + 1);
      setContinuable(false);
      return;
    }

    const success = await dispatch(submitPaymentProof(token));
    setSuccess(success);
  };

  const renderFormsSection = () => {
    if (success) {
      return <SuccessUploadPayment />;
    }

    if (active + 1 > STEPS.length) {
      return <ShippingPolicy onFormValid={setContinuable} />;
    }

    return (
      <Stepper
        active={active}
        allowNextStepsSelect={false}
        styles={{ separator: { transform: "translateY(-10px)" } }}
      >
        {STEPS.map(({ label, step }, index) => (
          <Stepper.Step key={`step-${index}`} label={label}>
            {step}
          </Stepper.Step>
        ))}
      </Stepper>
    );
  };

  if (isEmpty(orderList)) {
    return (
      <Center h="50vh">
        <Loader size="xl" />
      </Center>
    );
  }

  return (
    <Center w="100%" maw={MAX_WIDTH} mx="auto">
      <Stack gap="xs" align="center">
        {!success && (
          <>
            <Title ta="center" order={2}>
              {rejected
                ? "Pembayaran Anda ditolak"
                : "Yuk, lakukan pembayaran untuk order pertamamu!"}
            </Title>
            <Text c="text-light" mb="lg" ta="center">
              {rejected
                ? "Pembayaran Anda ditolak karena alasan berikut:"
                : "Lakukan pembayaran berikut untuk menjadi Distributor Resmi Hotto."}
            </Text>
          </>
        )}
        <Stack gap={32} w="100%">
          {renderFormsSection()}
          {!success && (
            <ButtonGroup justify="center">
              {active > 0 && (
                <Button variant="subtle" onClick={() => setActive(active - 1)}>
                  Kembali
                </Button>
              )}
              <Button
                loading={isLoading}
                disabled={!continuable}
                onClick={handleNext}
              >
                {shouldContinue ? "Selanjutnya" : "Submit"}
              </Button>
            </ButtonGroup>
          )}
        </Stack>
      </Stack>
    </Center>
  );
};

export default DistributorUploadPayment;
