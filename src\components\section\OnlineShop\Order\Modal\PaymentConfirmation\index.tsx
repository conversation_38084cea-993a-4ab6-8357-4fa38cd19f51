import ButtonGroup from "@/components/common/ButtonGroup";
import ResponsiveModal from "@/components/common/ResponsiveModal";
import Warning from "@/components/common/Warning";
import useBreakpoints from "@/hooks/useBreakpoints";
import { isEmpty, isValidUrl } from "@/utils/common-functions";
import {
  Stack,
  Title,
  Paper,
  Image,
  Text,
  TextInput,
  Button,
  Select,
  Checkbox,
  Box,
} from "@mantine/core";
import { useState } from "react";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { approveOrder } from "@/store/slices/onlineShopSlices/orderListSlice";

export interface OrderPaymentConfirmationModalProps {
  opened: boolean;
  onClose: () => void;
  viewOnly: boolean;
  data: {
    id: number;
    evidence: string;
  };
}

const OrderPaymentConfirmationModal: React.FC<
  OrderPaymentConfirmationModalProps
> = ({ opened, onClose, viewOnly, data: { id, evidence } }) => {
  const dispatch: AppDispatch = useDispatch();
  const { loading } = useSelector((state: RootState) => state.orderList);

  const { mobile } = useBreakpoints();

  const [reason, setReason] = useState("");
  const [custom, setCustom] = useState(false);

  const handleClose = () => {
    setReason("");
    setCustom(false);
    onClose();
  };

  const handleSubmit = (approve: boolean) => {
    dispatch(
      approveOrder(id, {
        isApproved: approve,
        rejectReason: approve ? "" : reason,
        resiNumber: "",
      })
    ).then(handleClose);
  };

  return (
    <ResponsiveModal opened={opened} onClose={handleClose}>
      <Stack>
        <Title order={3} ta="center">
          Bukti Pembayaran
        </Title>
        <Paper bg="bg" p="md">
          <Text size="sm" c="text" mb="xs">
            Uploaded Bukti Pembayaran
          </Text>
          {isValidUrl(evidence) ? (
            <Image
              title="Click untuk melihat bukti pembayaran"
              radius="md"
              src={evidence}
              alt="evidence"
              h="100%"
              w="100%"
              onClick={() => window.open(evidence, "_blank")}
              style={{ cursor: "pointer" }}
            />
          ) : (
            <Warning
              title="Data tidak valid"
              caption="Bukti pembayaran ini tidak dapat dibaca atau tidak valid"
            />
          )}
        </Paper>
        {!viewOnly && (
          <>
            <Box>
              {custom ? (
                <TextInput
                  name="reason"
                  label="Alasan Penolakan"
                  placeholder="Tulis alasan"
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                />
              ) : (
                <Select
                  name="reason"
                  label="Alasan Penolakan"
                  placeholder="Pilih alasan"
                  value={reason}
                  data={["Transfer tidak masuk", "Bukti foto tidak sesuai"]}
                  onChange={(value) => setReason(value || "")}
                />
              )}
              <Checkbox
                mt="xs"
                label="Custom input"
                checked={custom}
                onChange={() => {
                  setReason("");
                  setCustom(!custom);
                }}
              />
            </Box>
            <ButtonGroup justify="center" w="100%">
              <Button
                fullWidth={mobile}
                color="error"
                disabled={isEmpty(reason)}
                loading={loading.approve}
                onClick={() => handleSubmit(false)}
              >
                Reject
              </Button>
              <Button
                fullWidth={mobile}
                disabled={!isValidUrl(evidence)}
                loading={loading.approve}
                onClick={() => handleSubmit(true)}
              >
                Terima
              </Button>
            </ButtonGroup>
          </>
        )}
      </Stack>
    </ResponsiveModal>
  );
};

export default OrderPaymentConfirmationModal;
