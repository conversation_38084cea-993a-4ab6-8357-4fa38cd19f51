import { AppDispatch, RootState } from "@/store";
import { postFile } from "@/store/slices/fileSlice";
import { formatBytes } from "@/utils/common-functions";
import { FileUpload, Close, Upload } from "@/utils/icons";
import { InputWrapperProps, Stack, Input, Text, Anchor } from "@mantine/core";
import {
  Dropzone,
  DropzoneProps,
  IMAGE_MIME_TYPE,
  MS_WORD_MIME_TYPE,
  PDF_MIME_TYPE,
} from "@mantine/dropzone";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "sonner";
import classes from "./style.module.css";

export interface FileUploadInputProps
  extends Omit<InputWrapperProps, "onChange" | "children">,
    Pick<DropzoneProps, "maxSize" | "children"> {
  prefix: string;
  disabled?: boolean;
  helperText?: string;
  fileType?: FileType;
  iconSize?: number;
  onChange?: (url: string) => void;
}

export type FileType =
  | "all"
  | "images"
  | "word"
  | "pdf"
  | "documents"
  | "images_and_documents";

const ACCEPT_TYPES: Record<FileType, string[] | undefined> = {
  all: undefined,
  images: IMAGE_MIME_TYPE,
  word: MS_WORD_MIME_TYPE,
  pdf: PDF_MIME_TYPE,
  documents: [...PDF_MIME_TYPE, ...MS_WORD_MIME_TYPE],
  images_and_documents: [
    ...IMAGE_MIME_TYPE,
    ...PDF_MIME_TYPE,
    ...MS_WORD_MIME_TYPE,
  ],
};

const HELPER_TEXT: Record<FileType, string> = {
  all: "Upload any file",
  images: "Upload image files (JPG, PNG, etc.)",
  word: "Upload Word documents (.DOC or .DOCX)",
  pdf: "Upload PDF files",
  documents: "Upload document files (.PDF, .DOC, or .DOCX)",
  images_and_documents:
    "Upload image files (JPG, PNG, etc.) or documents (.PDF, .DOC, or .DOCX)",
};

const FileUploadInput: React.FC<FileUploadInputProps> = ({
  prefix,
  disabled,
  helperText,
  fileType = "all",
  maxSize = 2 * 1024 ** 2,
  iconSize = 82,
  onChange,
  ...props
}) => {
  const dispatch: AppDispatch = useDispatch();
  const { loading } = useSelector((state: RootState) => state.file);

  const handleDrop = async (files: File[]) => {
    const file = files[0];
    const formData = new FormData();
    formData.append("file", file, file.name);
    const url = await dispatch(postFile(formData, prefix));
    onChange?.(url);
  };

  const handleReject = () => {
    toast.warning(
      `File is not supported.${
        maxSize && " Max file size is " + formatBytes(maxSize)
      }`
    );
  };

  return (
    <Input.Wrapper {...props}>
      <Dropzone
        p="lg"
        classNames={classes}
        accept={ACCEPT_TYPES[fileType]}
        maxSize={maxSize}
        onDrop={handleDrop}
        onReject={handleReject}
        disabled={disabled || loading}
        loading={loading}
        data-reject={props.error ? true : undefined}
      >
        <Stack
          gap="sm"
          justify="center"
          align="center"
          ta="center"
          style={{ pointerEvents: "none" }}
        >
          <Dropzone.Accept>
            <Upload size={iconSize} />
          </Dropzone.Accept>
          <Dropzone.Reject>
            <Close size={iconSize} />
          </Dropzone.Reject>
          <Dropzone.Idle>
            <FileUpload
              size={iconSize}
              color="var(--mantine-color-text-light-filled)"
            />
          </Dropzone.Idle>
          <Text size="md" fw={500}>
            Drag & drop files or <Anchor underline="always">Browse</Anchor>
          </Text>
          <Text size="sm" c="text-light" inline>
            {helperText || HELPER_TEXT[fileType]}
            {maxSize && ". Max " + formatBytes(maxSize)}
          </Text>
        </Stack>
      </Dropzone>
    </Input.Wrapper>
  );
};

export default FileUploadInput;
