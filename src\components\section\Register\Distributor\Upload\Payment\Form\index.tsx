import { Input, SimpleGrid, Stack, Text } from "@mantine/core";
import { FormStepperProps } from "../../../Forms";
import UploadedFileRow from "@/components/common/UploadedFileRow";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { setPaymentProof } from "@/store/slices/distributorRegisterSlice";
import { useEffect } from "react";
import { isEmpty } from "@/utils/common-functions";
import FileUploadInput from "@/components/common/FileUploadInput";
import OrderDetailCard from "./OrderDetailCard";
import { DistributorOrderItem } from "@/utils/types/distributor-register";
import RejectAlert from "./RejectAlert";

const UploadPaymentForm: React.FC<
  FormStepperProps & { order: DistributorOrderItem }
> = ({ onFormValid, order }) => {
  const dispatch: AppDispatch = useDispatch();
  const { paymentProofs } = useSelector(
    (state: RootState) => state.distributorRegister
  );

  const paymentProof = paymentProofs?.find(
    (proof) => proof.orderId === order.orderId
  )?.paymentProof;

  useEffect(() => {
    const hasPaymentProof = Boolean(
      paymentProofs?.some((proof) => proof.orderId === order.orderId)
    );
    if (order.paymentProof && !hasPaymentProof) {
      dispatch(
        setPaymentProof({
          orderId: order.orderId,
          paymentProof: order.paymentProof,
        })
      );
    }
    onFormValid(hasPaymentProof);
  }, [paymentProofs, order.orderId, order.paymentProof]);

  return (
    <Stack gap={50}>
      {!isEmpty(order.rejectReason) && <RejectAlert order={order} />}
      <OrderDetailCard order={order} />
      <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="xl">
        <FileUploadInput
          required
          label="Upload Bukti Pembayaran"
          fileType="images"
          prefix={`distributor-payment/order-${order.orderId}`}
          onChange={(url) => {
            dispatch(
              setPaymentProof({
                orderId: order.orderId,
                paymentProof: url,
              })
            );
          }}
        />
        <Input.Wrapper label="Uploaded Bukti Pembayaran">
          {isEmpty(paymentProof) ? (
            <Text c="text">Belum ada file diupload!</Text>
          ) : (
            <UploadedFileRow
              fileUrl={paymentProof!}
              onDelete={() => {
                if (confirm("Apakah anda yakin ingin menghapus file ini?")) {
                  dispatch(
                    setPaymentProof({
                      orderId: order.orderId,
                      paymentProof: "",
                    })
                  );
                }
              }}
            />
          )}
        </Input.Wrapper>
      </SimpleGrid>
    </Stack>
  );
};

export default UploadPaymentForm;
