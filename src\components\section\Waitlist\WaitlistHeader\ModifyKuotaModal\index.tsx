import ButtonGroup from "@/components/common/ButtonGroup";
import InputGroup from "@/components/common/InputGroup";
import { Button, Input, Paper, Stack, Text, Title } from "@mantine/core";
import { useState } from "react";
import { AppDispatch } from "@/store";
import { useDispatch } from "react-redux";
import {
  fetchWaitlistData,
  updateRadiusKuota,
} from "@/store/slices/waitlistSlice";
import { toast } from "sonner";
import ResponsiveModal from "@/components/common/ResponsiveModal";

interface ModifyKuotaModalProps {
  opened: boolean;
  onClose: () => void;
  role: "agent" | "distributor";
  currentKuota: number;
  currentRadius: number;
}

const ModifyKuotaModal = ({
  opened,
  onClose,
  role,
  currentKuota,
  currentRadius,
}: ModifyKuotaModalProps) => {
  const dispatch: AppDispatch = useDispatch();
  const [kuota, setKuota] = useState("");

  const handleUpdateKuota = () => {
    if (Number(kuota) < currentKuota) {
      toast.error(
        "Kuota yang dimasukkan harus lebih besar dari kuota saat ini"
      );
      return;
    }
    dispatch(updateRadiusKuota(Number(kuota), role, onSuccess));
  };
  const onSuccess = () => {
    onClose();
    dispatch(fetchWaitlistData(role));
  };

  return (
    <ResponsiveModal opened={opened} onClose={onClose}>
      <Stack align="center" pos="relative">
        <Title order={3}>Tambah Kuota {role}</Title>
        <Text c="text-light" ta="center">
          Radius: {currentRadius}km
          <br />
          Jumlah kuota {role} saat ini: {currentKuota}
        </Text>
        <InputGroup label={`Jumlah ${role} per radius`} mandatory>
          <Input
            placeholder="Masukkan jumlah kuota"
            type="number"
            value={kuota}
            onChange={(e) => setKuota(e.target.value)}
          />
        </InputGroup>
        <Stack pos="relative" w="100%" h="4em" justify="flex-end">
          <Paper
            pos="absolute"
            c={"error"}
            bg={"error.1"}
            w="calc(100% + 32px)"
            ml="-16px"
            p="sm"
            ta="center"
            fw={600}
            fz={{ base: "xs", md: "sm" }}
            style={{
              borderRadius: "8px 8px 0 0",
            }}
          >
            Pastikan angka yang dituliskan jumlah terbaru, bukan total
            penambahan.
          </Paper>
        </Stack>
        <ButtonGroup direction="row" w="100%" justify="center">
          <Button variant="transparent" onClick={onClose}>
            Kembali
          </Button>
          <Button onClick={handleUpdateKuota}>Tambah</Button>
        </ButtonGroup>
      </Stack>
    </ResponsiveModal>
  );
};

export default ModifyKuotaModal;
