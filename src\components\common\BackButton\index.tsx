"use client";

import { ActionIcon, ActionIconProps } from "@mantine/core";
import { useRouter } from "next/navigation";
import { CaretLeft } from "@/utils/icons";
import { isEmpty } from "@/utils/common-functions";

export interface BackButtonProps extends ActionIconProps {
  href?: string;
  iconSize?: number;
}

/**
 * A button to go back to the previous page.
 * Based on the Mantine ActionIcon component.
 * See more https://mantine.dev/core/action-icon/
 */
const BackButton: React.FC<BackButtonProps> = ({
  size = "lg",
  iconSize = 24,
  href = "",
  ...props
}: BackButtonProps) => {
  const router = useRouter();

  const handleClick = () => {
    if (!isEmpty(href)) {
      router.push(href);
    } else {
      router.back();
    }
  };

  return (
    <ActionIcon
      {...props}
      size={size}
      radius={9999}
      aria-label="Back"
      variant="default"
      bg="bg"
      onClick={handleClick}
      {...props}
    >
      <CaretLeft size={iconSize} color="var(--mantine-color-primary-3)" />
    </ActionIcon>
  );
};

export default BackButton;
