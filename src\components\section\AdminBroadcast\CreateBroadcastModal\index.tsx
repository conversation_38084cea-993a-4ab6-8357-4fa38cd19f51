import InputGroup from "@/components/common/InputGroup";
import MultipleFileInput from "@/components/common/MultipleFileInput";
import MultiselectAgentsDistributor from "@/components/common/MultiselectAgentsDistributor";
import { AppDispatch, RootState } from "@/store";
import {
  createBroadcast,
  fetchBroadcastDetails,
  resetBroadcastDetails,
  updateBroadcast,
} from "@/store/slices/adminBroadcastSlice";
import { BROADCAST_TYPE_SELECTIONS } from "@/utils/constants";
import { BroadcastRecipient, CreateBroadcast } from "@/utils/types/broadcast";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  Checkbox,
  Drawer,
  Flex,
  Input,
  Modal,
  Select,
  Text,
  Textarea,
  Title,
  Button,
} from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import * as yup from "yup";

const schema = yup.object({
  title: yup.string().required("Judul harus diisi").default(""),
  body: yup.string().required("Pesan harus diisi").default(""),
  type: yup.string().required("Jenis pengumuman harus diisi"),
  documents: yup.array(yup.string().required()).default([]),
  recipients: yup.array(yup.string().required()).default([]),
  isAllRecipient: yup.boolean().default(false),
});

interface CreateBroadcastModalProps {
  id?: number | null; // if ID exist this'll be an update modal
  opened: boolean;
  onClose: () => void;
}

const CreateBroadcastModal = ({
  id = null,
  opened,
  onClose,
}: CreateBroadcastModalProps) => {
  const { broadcastDetail } = useSelector(
    (state: RootState) => state.adminBroadcast
  );
  const dispatch: AppDispatch = useDispatch();

  const {
    watch,
    register,
    setValue,
    resetField,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    mode: "onChange",
  });

  const isValid = (): boolean => {
    const type = watch("type");
    const isAllRecipient = watch("isAllRecipient");
    const recipients = watch("recipients");

    return !!type && (isAllRecipient || (recipients && recipients.length > 0));
  };

  const handleSubmit = (data: CreateBroadcast, asDraft: boolean) => {
    if (!asDraft && !isValid) return;
    if (id !== null) {
      dispatch(updateBroadcast(id, data, asDraft));
    } else {
      dispatch(createBroadcast(data, asDraft));
    }
    onClose();
  };

  const resetAllFields = () => {
    resetField("title");
    resetField("body");
    resetField("type");
    resetField("isAllRecipient");
    setValue("documents", []);
    setValue("recipients", []);
  };

  useEffect(() => {
    if (id) {
      dispatch(fetchBroadcastDetails(id));
    }
  }, [id, dispatch]); // fetch the details if ID is passed

  useEffect(() => {
    if (id && broadcastDetail) {
      setValue("title", broadcastDetail.title || "");
      setValue("body", broadcastDetail.body || "");
      setValue("type", broadcastDetail.type || "");
      setValue("documents", broadcastDetail.documents || []);
      setValue("isAllRecipient", broadcastDetail.isAllRecipient || false);
      setValue(
        "recipients",
        broadcastDetail.recipients && broadcastDetail.recipients.length > 0
          ? broadcastDetail.recipients
              .filter(
                (recipient): recipient is BroadcastRecipient =>
                  typeof recipient !== "string"
              )
              .map((recipient) => recipient.uoc)
          : []
      );
    }
  }, [broadcastDetail]); // set form values based on response

  useEffect(() => {
    if (!opened) {
      resetAllFields();
      dispatch(resetBroadcastDetails());
    }
  }, [opened]); // reset all fields when closing the modal

  const renderModalContent = () => {
    return (
      <Flex direction={"column"} gap={"md"} align={"center"}>
        <Title order={2}>Broadcast Baru</Title>
        <Flex
          p={"2px"}
          w={"100%"}
          direction={"column"}
          gap={"md"}
          align={"center"}
          mah={isMobile ? "64vh" : "fit-content"}
          className="scrollable-form"
          style={{
            overflowY: "scroll",
          }}
        >
          <InputGroup label={"Tipe Surat"} mandatory>
            <Select
              data={BROADCAST_TYPE_SELECTIONS}
              placeholder="Pilih Tipe Surat"
              value={watch("type")}
              onChange={(value) => setValue("type", value || "")}
            />
            {(watch("type") === "" || errors.type) && (
              <Text c={"error"}>
                {errors.type?.message || "Jenis Pengumuman harus diisi"}
              </Text>
            )}
          </InputGroup>
          <InputGroup label="Judul Surat" mandatory>
            <Input
              type="text"
              placeholder="Contoh: Peringatan pelanggaran surat perjanjian kerjasama"
              {...register("title")}
            />
            {errors.title && <Text c={"error"}>{errors.title.message}</Text>}
          </InputGroup>
          <InputGroup label="Isi" mandatory>
            <Textarea
              placeholder="Contoh: Anda terindikasi melanggar perjanjian yang telah ditentukan"
              {...register("body")}
              description={`${watch("body")?.length || 0}/255 karakter`}
              maxLength={255}
            />
            {errors.body && <Text c={"error"}>{errors.body.message}</Text>}
          </InputGroup>
          {watch("documents") && (
            <MultipleFileInput
              prefixUrl={`broadcast/${id ? id : Date.now()}`}
              uploadedFiles={watch("documents")}
              setUploadedFiles={(uploadedFiles: string[]) =>
                setValue("documents", uploadedFiles)
              }
              label="Dokumen"
            />
          )}
          <InputGroup label="Dikirimkan untuk" mandatory>
            <MultiselectAgentsDistributor
              value={watch("recipients")}
              onChange={(value) => {
                setValue("recipients", value);
              }}
              placeholder={
                watch("isAllRecipient")
                  ? "Semua agent & distributor"
                  : "Surat ini ditujukan untuk"
              }
              disabled={watch("isAllRecipient")}
              previousValueDetails={
                broadcastDetail?.recipients as BroadcastRecipient[]
              }
            />
          </InputGroup>
          <Flex gap={"md"} justify={"space-between"} w={"100%"}>
            <Checkbox
              label="Kirim ke semua distributor & agent"
              {...register("isAllRecipient")}
              maw={"45%"}
            />
            {!watch("isAllRecipient") && (
              <Text ta={"right"} size="sm" maw={"45%"}>
                {`${watch("recipients")?.length || 0} Tujuan`} terpilih
              </Text>
            )}
          </Flex>
        </Flex>
        <Flex gap={"md"} justify={"space-between"} mt={"xl"} w={"100%"}>
          {!isMobile && (
            <Button variant="subtle" onClick={onClose} type="button">
              Kembali
            </Button>
          )}
          <Flex
            direction={isMobile ? "column-reverse" : "row"}
            gap={"sm"}
            w={isMobile ? "100%" : "fit-content"}
          >
            <Button
              variant="subtle"
              onClick={() => handleSubmit(watch() as CreateBroadcast, true)}
            >
              Simpan sebagai draft
            </Button>
            <Button
              disabled={!isValid()}
              onClick={() => handleSubmit(watch() as CreateBroadcast, false)}
            >
              Kirim
            </Button>
          </Flex>
        </Flex>
      </Flex>
    );
  };

  const isMobile = useMediaQuery("(max-width: 768px)");
  return (
    <>
      {isMobile ? (
        <Drawer
          opened={opened}
          onClose={onClose}
          radius={"24px 24px 0 0 "}
          position="bottom"
          size={"95vh"}
        >
          {renderModalContent()}
        </Drawer>
      ) : (
        <Modal opened={opened} onClose={onClose} size={"xl"} radius={"8px"}>
          {renderModalContent()}
        </Modal>
      )}
    </>
  );
};

export default CreateBroadcastModal;
