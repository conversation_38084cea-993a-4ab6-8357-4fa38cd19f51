import SectionHeader from "@/components/common/SectionHeader";
import {
  AspectRatio,
  Image,
  NumberFormatter,
  Skeleton,
  Stack,
  Table,
  Text,
} from "@mantine/core";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

const BestSellerProducts = () => {
  const {
    bestSellerProducts,
    loading: { bestSellerProducts: loading },
  } = useSelector((state: RootState) => state.dashboard);

  const LoadingRows = () => (
    <>
      {[1, 2, 3].map((index) => (
        <Table.Tr key={index}>
          <Table.Td>
            <AspectRatio ratio={1} maw={100}>
              <Skeleton height="100%" />
            </AspectRatio>
          </Table.Td>
          <Table.Td>
            <Skeleton height={20} width="80%" />
          </Table.Td>
          <Table.Td>
            <Skeleton height={20} width="60%" />
          </Table.Td>
          <Table.Td>
            <Skeleton height={20} width="40%" />
          </Table.Td>
          <Table.Td>
            <Skeleton height={20} width="40%" />
          </Table.Td>
          <Table.Td>
            <Skeleton height={20} width="60%" />
          </Table.Td>
        </Table.Tr>
      ))}
    </>
  );

  const ProductImage = ({ src, alt }: { src: string; alt: string }) => (
    <AspectRatio ratio={1} maw={100} style={{ overflow: "hidden" }}>
      <Image
        src={src}
        alt={alt}
        width="100%"
        height="auto"
        style={{ objectFit: "cover", aspectRatio: "1/1" }}
      />
    </AspectRatio>
  );

  const TableHeader = ({
    children,
    width,
  }: {
    children: React.ReactNode;
    width?: number | string;
  }) => (
    <Table.Th
      style={{ borderInlineEnd: "1px solid var(--mantine-color-bg-filled)" }}
      w={width}
    >
      {children}
    </Table.Th>
  );

  const rows = bestSellerProducts?.map((product) => (
    <Table.Tr key={product.id}>
      <Table.Td>
        <ProductImage src={product.images[0]} alt={product.name} />
      </Table.Td>
      <Table.Td>{product.name}</Table.Td>
      <Table.Td>{product.variant || "-"}</Table.Td>
      <Table.Td>{product.seenCount}</Table.Td>
      <Table.Td>{product.productSold}</Table.Td>
      <Table.Td>
        <Text>
          <NumberFormatter value={product.productRevenue} />
        </Text>
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <Stack>
      <SectionHeader
        title="Produk Best Seller"
        description={`Menampilkan ${
          bestSellerProducts?.length || 0
        } produk best seller dengan penjualan tertinggi`}
        loading={loading}
      />
      <Table.ScrollContainer minWidth="600">
        <Table
          withColumnBorders
          verticalSpacing="md"
          style={{
            borderTop: "1px solid var(--mantine-color-bg-gray-filled)",
            borderBottom: "1px solid var(--mantine-color-bg-gray-filled)",
          }}
        >
          <Table.Thead>
            <Table.Tr c="text-light">
              <TableHeader width={120}>Gambar</TableHeader>
              <TableHeader width={200}>Nama Produk</TableHeader>
              <TableHeader width={1}>Varian</TableHeader>
              <TableHeader width={1}>Dilihat</TableHeader>
              <TableHeader width={1}>Terjual</TableHeader>
              <TableHeader width={1}>Pendapatan</TableHeader>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {loading ? (
              <LoadingRows />
            ) : bestSellerProducts?.length ? (
              rows
            ) : (
              <Table.Tr>
                <Table.Td colSpan={7} c="text-light" ta="center" py="xl">
                  Gagal memuat data produk best seller.
                  <br />
                  Coba lagi nanti.
                </Table.Td>
              </Table.Tr>
            )}
          </Table.Tbody>
        </Table>
      </Table.ScrollContainer>
    </Stack>
  );
};

export default BestSellerProducts;
