import Icon from "@/components/common/Icon";
import { Divider, Flex } from "@mantine/core";

export interface LeftSectionProps {
  icon: string | React.ReactNode;
  iconSize?: number;
}

const LeftSection: React.FC<LeftSectionProps> = ({ icon, iconSize }) => {
  return (
    <Flex gap={5} align="center">
      {typeof icon === "string" ? (
        <Icon icon={icon} size={iconSize ?? 16} />
      ) : (
        icon
      )}
      <Divider orientation="vertical" />
    </Flex>
  );
};

export default LeftSection;
