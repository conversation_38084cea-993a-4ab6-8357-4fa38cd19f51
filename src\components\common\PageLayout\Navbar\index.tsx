import {
  AppShell,
  AppShellNavbarProps,
  Burger,
  Group,
  ScrollArea,
  Stack,
} from "@mantine/core";
import BrandLogo from "@/components/common/BrandLogo";
import MenuItem, { MenuItemProps } from "./MenuItem";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { ADMIN_MENUS, AGENT_MENUS, DISTRIBUTOR_MENUS } from "@/utils/constants";
import { logout } from "@/store/slices/authSlice";
import { padding } from "..";
import { Logout } from "@/utils/icons";
import { memo } from "react";

export interface NavbarProps extends Partial<AppShellNavbarProps> {
  opened: boolean;
  toggle: () => void;
}

const Navbar: React.FC<NavbarProps> = ({ opened, toggle, ...props }) => {
  const dispatch: AppDispatch = useDispatch();
  const { role } = useSelector((state: RootState) => state.auth);
  const MENUS: MenuItemProps[] =
    role === "admin"
      ? ADMIN_MENUS
      : role === "agent"
      ? AGENT_MENUS
      : DISTRIBUTOR_MENUS;

  return (
    <AppShell.Navbar bg="primary.8" p={padding} {...props}>
      <AppShell.Section>
        <Group p="lg" justify="space-between">
          <BrandLogo alt />
          <Burger
            color="white"
            opened={opened}
            onClick={toggle}
            hiddenFrom="sm"
            size="sm"
          />
        </Group>
      </AppShell.Section>
      <AppShell.Section grow my="md" component={ScrollArea}>
        <Stack gap="xs">
          {MENUS.map((menu, index) => (
            <MenuItem key={index} {...menu} />
          ))}
        </Stack>
      </AppShell.Section>
      <AppShell.Section>
        <MenuItem
          label="Logout"
          icon={<Logout />}
          onClick={() => dispatch(logout())}
        />
      </AppShell.Section>
    </AppShell.Navbar>
  );
};

export default memo(Navbar);
