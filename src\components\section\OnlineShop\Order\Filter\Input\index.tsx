import Icon from "@/components/common/Icon";
import { RootState } from "@/store";
import { SORT_FILTERS } from "@/utils/constants";
import { CloseButton, Grid, Input, Select } from "@mantine/core";
import { parseAsString, parseAsStringLiteral, useQueryState } from "nuqs";
import { useSelector } from "react-redux";

const OrderInputFilter: React.FC = () => {
  const { loading } = useSelector((state: RootState) => state.orderList);

  const [order, setOrder] = useQueryState(
    "order",
    parseAsStringLiteral(["desc", "asc"] as const).withDefault("desc")
  );
  const [search, setSearch] = useQueryState(
    "search",
    parseAsString.withDefault("")
  );

  const handleSearchChange = (value: string) => {
    setSearch(value);
  };

  const handleOrderChange = (value: string) => {
    setOrder(value as "desc" | "asc");
  };

  return (
    <Grid justify="center" align="center" gutter="xs">
      <Grid.Col span={{ base: 12, md: "auto" }}>
        <Input
          value={search}
          onChange={(e) => handleSearchChange(e.currentTarget.value)}
          placeholder="Cari Produk"
          type="search"
          leftSection={<Icon icon="search" size={16} />}
          rightSectionPointerEvents="auto"
          rightSection={
            <CloseButton
              onClick={() => setSearch("")}
              style={{ display: search ? undefined : "none" }}
            />
          }
        />
      </Grid.Col>
      <Grid.Col span={{ base: "auto", md: "content" }}>
        <Select
          w={{ base: "100%", md: "7em" }}
          value={order}
          disabled={loading.orders}
          onChange={(value) => handleOrderChange(value!)}
          data={SORT_FILTERS}
        />
      </Grid.Col>
      {/* TODO: filter kurir */}
    </Grid>
  );
};

export default OrderInputFilter;
