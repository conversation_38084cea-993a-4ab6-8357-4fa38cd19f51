import ActivationStatus, {
  ActivationStatusProps,
} from "@/components/common/ActivationStatus";
import Icon from "@/components/common/Icon";
import { Paper, Text, Group, Flex, Stack, Anchor } from "@mantine/core";
import Link from "next/link";

export interface AgentCardProps {
  uoc: string;
  name: string;
  status: ActivationStatusProps["status"];
  ownerPhone: string;
  adminPhone: string;
}

const AgentCard: React.FC<AgentCardProps> = ({
  uoc,
  name,
  status,
  ownerPhone,
  adminPhone,
}) => {
  return (
    <Paper bg="bg" p="lg" h="100%">
      <Stack>
        <Group align="center">
          <Anchor
            component={Link}
            href={`/agent/${uoc}`}
            c="text-light"
            fw={600}
          >
            {name}
          </Anchor>
          <ActivationStatus status={status} />
        </Group>
        <Stack gap="xs">
          {ownerPhone && (
            <Flex direction="row" gap="xs" align="center">
              <Icon size={20} icon="info/phone" />
              <Text size="sm" c="text">
                Pemilik: {ownerPhone}
              </Text>
            </Flex>
          )}
          {adminPhone && (
            <Flex direction="row" gap="xs" align="center">
              <Icon size={20} icon="info/phone" />
              <Text size="sm" c="text">
                Admin: {adminPhone}
              </Text>
            </Flex>
          )}
        </Stack>
      </Stack>
    </Paper>
  );
};

export default AgentCard;
