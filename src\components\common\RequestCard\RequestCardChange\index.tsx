import UpdateLog from "@/components/section/AdminAgentDistributor/UpdateLog";
import { Flex, Text, Button } from "@mantine/core";
import { useState } from "react";
import { useMediaQuery } from "@mantine/hooks";
import ReviewStatus from "@/components/common/ReviewStatus";
import RequestModal from "../../RequestModal";
import { RequestType } from "@/utils/types/request";

interface RequestCardChangeProps {
  requestId: number;
  field: string;
  oldValue: string;
  newValue: string;
  reason?: string;
  status?: "pending" | "accept" | "reject";
  isAdmin?: boolean;
}

const RequestCardChange = ({
  requestId,
  field,
  oldValue,
  newValue,
  reason = "-",
  status = "pending",
  isAdmin = true,
}: RequestCardChangeProps) => {
  const [openedModal, setOpenedModal] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<"accept" | "reject">(
    "accept"
  );

  const handleAcceptChange = () => {
    setSelectedStatus("accept");
    setOpenedModal(true);
  };

  const handleRejectChange = () => {
    setSelectedStatus("reject");
    setOpenedModal(true);
  };

  const renderRequestStatus = () => {
    if (status !== "pending") {
      return <ReviewStatus status={status} />;
    }

    if (!isAdmin) {
      return (
        <Text fw={400} fs={"italic"} ta={"right"}>
          Sedang direview
        </Text>
      );
    }

    return (
      <Flex
        direction={"row"}
        gap={"md"}
        w={isMobile ? "100%" : "fit-content"}
        justify={"center"}
      >
        <Button variant="subtle" onClick={handleRejectChange}>
          Reject
        </Button>
        <Flex w={isMobile ? "33%" : "fit-content"}>
          <Button onClick={handleAcceptChange}>Accept</Button>
        </Flex>
      </Flex>
    );
  };

  const isMobile = useMediaQuery("(max-width: 768px)");

  return (
    <>
      <RequestModal
        opened={openedModal}
        onClose={() => {
          setOpenedModal(false);
        }}
        status={selectedStatus}
        requestId={requestId}
        field={field}
        type={RequestType.DataChange}
      />
      <Flex
        direction={isMobile ? "column" : "row"}
        justify={"space-between"}
        p={"lg"}
        gap={"xl"}
      >
        <Flex maw={isMobile ? "100%" : "66%"}>
          <UpdateLog
            log={{
              fieldChanged: field,
              old: oldValue,
              new: newValue,
              reason: reason,
            }}
            fw={500}
          />
        </Flex>
        <Flex w={isMobile ? "100%" : "fit-content"} justify={"flex-end"}>
          {renderRequestStatus()}
        </Flex>
      </Flex>
    </>
  );
};

export default RequestCardChange;
