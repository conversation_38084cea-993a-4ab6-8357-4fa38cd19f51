import {
  Textarea,
  Text,
  Checkbox,
  ComboboxData,
  Stack,
  NativeSelect,
  Title,
  Button,
} from "@mantine/core";

import useBreakpoints from "@/hooks/useBreakpoints";
import ButtonGroup from "@/components/common/ButtonGroup";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { ComposeAgentTerminationData } from "@/utils/types/request";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { fetchAgentList } from "@/store/slices/distributorAgentsSlice";
import {
  clearAgentTerminationDraft,
  createAgentTerminationRequest,
  fetchAgentTerminationRequestDraft,
  updateAgentTerminationRequest,
} from "@/store/slices/distributorRequestSlice";
import { toast } from "sonner";
import ResponsiveModal from "@/components/common/ResponsiveModal";

const schema = yup.object({
  agentId: yup
    .string()
    .required("Kamu belum memilih agent yang akan dihentikan"),
  reason: yup.string().required("Alasan harus diisi"),
  isAgree: yup
    .boolean()
    .oneOf([true], "Persetujuan harus dikonfirmasi")
    .required("Persetujuan harus dikonfirmasi"),
});

interface RequestModalProps {
  id?: number;
  opened: boolean;
  onClose: () => void;
}

const ComposeAgentTerminationModal = ({
  id,
  opened,
  onClose,
}: RequestModalProps) => {
  const {
    register,
    setValue,
    getValues,
    reset,
    formState: { errors, isValid },
  } = useForm({
    resolver: yupResolver<ComposeAgentTerminationData>(schema),
    mode: "onChange",
  });

  const dispatch: AppDispatch = useDispatch();
  const { agentList } = useSelector(
    (state: RootState) => state.distributorAgents
  );
  const { agentTerminationDraft } = useSelector(
    (state: RootState) => state.request
  );

  const [agents, setAgents] = useState<ComboboxData>([]);

  useEffect(() => {
    if (opened && agentList.length === 0) {
      dispatch(fetchAgentList());
    }
    if (opened) {
      if (id) {
        dispatch(fetchAgentTerminationRequestDraft(id));
      }
    } else {
      dispatch(clearAgentTerminationDraft());
    }
  }, [opened, id]);

  useEffect(() => {
    if (agentList.length > 0) {
      setAgents([
        { value: "", label: "Pilih agent" },
        ...agentList.map((agent) => ({
          value: agent.uoc,
          label: `${agent.name} - ${agent.phone}`,
        })),
      ]);
    }
  }, [agentList]);

  useEffect(() => {
    if (agentTerminationDraft) {
      setValue("agentId", agentTerminationDraft?.terminateAgent?.agentID || "");
      setValue("reason", agentTerminationDraft.reason || "");
    }
  }, [agentTerminationDraft]);

  const handleSaveAsDraftButtonClick = () => {
    if (id) {
      dispatch(
        updateAgentTerminationRequest(
          id,
          getValues().agentId,
          getValues().reason
        )
      );
    } else {
      dispatch(
        createAgentTerminationRequest(
          getValues().agentId,
          getValues().reason,
          true
        )
      );
    }
    handleCloseModal();
  };

  const handleSubmitButtonClick = () => {
    if (isValid) {
      dispatch(
        createAgentTerminationRequest(
          getValues().agentId,
          getValues().reason,
          false,
          id
        )
      );
    } else {
      toast.error("Mohon lengkapi semua data yang dibutuhkan");
    }
    handleCloseModal();
  };

  const handleCloseModal = () => {
    reset();
    onClose();
  };

  const { mobile } = useBreakpoints();

  return (
    <ResponsiveModal
      opened={opened}
      onClose={handleCloseModal}
      modalProps={{ size: "lg" }}
    >
      <Stack p={mobile ? 0 : "lg"}>
        <Stack ta={"center"} gap={"xs"}>
          <Title order={2}>Pemutusan Hubungan kerja Agent</Title>
          <Text c={"text-light"} fw={500} size="sm">
            Pemutusan hubungan kerja akan menghentikan hubungan kerja antara
            Anda dengan agent terkait. Mohon untuk secara bijak membuat
            keputusan ini.
          </Text>
        </Stack>
        <NativeSelect
          required
          label="Agent"
          data={agents}
          error={errors.agentId?.message}
          {...register("agentId")}
        />
        <Textarea
          required
          label="Alasan pemutusan hubungan"
          placeholder="Contoh: Anda terindikasi melanggar perjanjian yang telah ditentukan"
          error={errors.reason?.message}
          {...register("reason")}
        />
        <Checkbox
          fw={500}
          error={errors.isAgree?.message}
          label="Saya memahami bahwa pemutusan hubungan kerja ini jika disetujui akan secara final memutuskan hubungan dan dampak berdampak ke agent tersebut."
          {...register("isAgree")}
        />

        <ButtonGroup justify={"space-between"} w={"100%"} pt={"xl"}>
          {mobile ? (
            <Button variant="subtle" onClick={handleSaveAsDraftButtonClick}>
              Simpan sebagai draft
            </Button>
          ) : (
            <Button variant="subtle" onClick={handleCloseModal}>
              Kembali
            </Button>
          )}
          <ButtonGroup>
            {!mobile && (
              <Button variant="subtle" onClick={handleSaveAsDraftButtonClick}>
                Simpan sebagai draft
              </Button>
            )}
            <Button onClick={handleSubmitButtonClick} disabled={!isValid}>
              Kirim
            </Button>
          </ButtonGroup>
        </ButtonGroup>
      </Stack>
    </ResponsiveModal>
  );
};

export default ComposeAgentTerminationModal;
