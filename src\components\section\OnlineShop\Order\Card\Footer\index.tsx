import Icon from "@/components/common/Icon";
import { AppDispatch, RootState } from "@/store";
import { approveOrder } from "@/store/slices/onlineShopSlices/orderListSlice";
import { isEmpty } from "@/utils/common-functions";
import { OrderDetail, OrderStatusType } from "@/utils/types/online-shop";
import { Center, Grid, Group, Text, Button } from "@mantine/core";
import { useDispatch, useSelector } from "react-redux";

export interface OrderCardFooterProps {
  data: OrderDetail;
  onConfirmPaymentClick?: () => void;
  onResiInputClick?: () => void;
  onComplaintClick?: () => void;
}

const OrderCardFooter: React.FC<OrderCardFooterProps> = ({
  data,
  onConfirmPaymentClick,
  onResiInputClick,
  onComplaintClick,
}) => {
  const dispatch: AppDispatch = useDispatch();
  const { loading } = useSelector((state: RootState) => state.orderList);

  const renderActionButton = (): React.ReactNode => {
    switch (data.status) {
      case OrderStatusType.WaitingConfirmation:
        return (
          <Button
            disabled={isEmpty(data.evidence)}
            onClick={onConfirmPaymentClick}
          >
            Konfirmasi Pembayaran
          </Button>
        );
      case OrderStatusType.OrderConfirmation:
        return <Button>Konfirmasi Pesanan</Button>;
      case OrderStatusType.WaitingForDelivery:
        return <Button onClick={onResiInputClick}>Input Resi</Button>;
      case OrderStatusType.OnDelivery:
        return (
          <>
            {data.resiNumber && (
              <Button onClick={onResiInputClick}>Edit Resi</Button>
            )}
          </>
        );
      case OrderStatusType.Complaint:
        return <Button onClick={onComplaintClick}>Investigasi</Button>;
      case OrderStatusType.Finished:
        // TODO: implement this button
        return <Button variant="outline">Lihat rating pembeli</Button>;
      default:
        return null;
    }
  };

  return (
    <Grid align="center">
      <Grid.Col span="content">
        <Center>
          <Icon icon="note" size={18} />
        </Center>
      </Grid.Col>
      <Grid.Col span="auto">
        <Text>{data.notes ? `"${data.notes}"` : "-"}</Text>
      </Grid.Col>
      <Grid.Col span="content">
        <Group gap="xs">
          {/* TODO: implement this button */}
          <Button variant="outline">Download Label</Button>
          {renderActionButton()}
        </Group>
      </Grid.Col>
    </Grid>
  );
};

export default OrderCardFooter;
