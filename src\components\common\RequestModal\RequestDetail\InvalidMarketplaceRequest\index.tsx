import { AppDispatch, RootState } from "@/store";
import { fetchRequestDetailForDistributor } from "@/store/slices/distributorRequestSlice";
import { RequestType } from "@/utils/types/request";
import { Flex, Text } from "@mantine/core";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

interface RequestDetailContentProps {
  requestId: number;
  field?: string;
}
const InvalidMarketplaceRequestDetail = ({
  requestId,
  field = "",
}: RequestDetailContentProps) => {
  const dispatch: AppDispatch = useDispatch();
  const { invalidMarketplaceRequest } = useSelector(
    (state: RootState) => state.request
  );

  useEffect(() => {
    dispatch(
      fetchRequestDetailForDistributor(
        requestId,
        RequestType.DistributorCheckMarketplaceLinks,
        field
      )
    );
  }, [requestId]);

  return (
    <>
      {invalidMarketplaceRequest && (
        <Flex
          direction={"column"}
          justify={"center"}
          align={"center"}
          gap={"xs"}
        >
          <Text
            fw={700}
            c={"text-light"}
          >{`Silahkan segera update link marketplace Anda!`}</Text>

          <Text fw={500}>{`Link lama: ${
            invalidMarketplaceRequest.invalidLink?.old || "-"
          }`}</Text>
          <Text fw={500} c={"text-light"}>{`Alasan invalid: ${
            invalidMarketplaceRequest.reason || "-"
          }`}</Text>
        </Flex>
      )}
    </>
  );
};

export default InvalidMarketplaceRequestDetail;
