import { COLORS } from "@/styles/color";
import { SPACES } from "@/styles/spacing";
import styled from "styled-components";

export const StepperWrapper = styled.div`
  position: relative;
  width: 100%;

  .mantine-Stepper-root {
    position: relative;
    z-index: 2;
    width: 100%;
  }

  .mantine-Stepper-steps {
    align-items: flex-start;
    justify-content: space-between;
    gap: ${SPACES.md};
  }

  .mantine-Stepper-separator {
    display: none;
    opacity: 0;
  }

  .mantine-Stepper-step {
    flex-direction: column;
    gap: ${SPACES.xs};
    justify-content: center;
    align-items: center;

    .mantine-Stepper-stepBody {
      margin: 0;
      text-align: center;
      max-width: 80px;

      @media screen and (max-width: 768px) {
        span {
          font-size: 14px;
        }
      }
    }

    .mantine-Stepper-stepIcon {
      background-color: ${COLORS.disabled2};
      color: ${COLORS.background};
      border-color: transparent;
      font-size: 1em;

      &:where([data-progress]) {
        background-color: ${COLORS.accent};
        border-color: transparent;
      }
      &:where([data-completed]) {
        background-color: ${COLORS.accentDisabled};
        border-color: transparent;
      }
    }
  }
`;

export const StepperLine = styled.div`
  position: absolute;
  left: ${SPACES.xl2};
  right: ${SPACES.xl2};
  top: 1.5em;
  width: calc(100% - ${SPACES.xl3});
  border-top: 1px dashed ${COLORS.disabled};
  z-index: 1;
`;
