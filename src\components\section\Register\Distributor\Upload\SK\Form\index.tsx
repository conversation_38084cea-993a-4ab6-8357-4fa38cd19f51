import { Checkbox, Input, SimpleGrid, Stack, Text } from "@mantine/core";
import { FormStepperProps } from "../../../Forms";
import classes from "../../../Forms/UserAgreement/style.module.css";
import UploadedFileRow from "@/components/common/UploadedFileRow";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { setNewSk } from "@/store/slices/distributorRegisterSlice";
import { useId } from "@mantine/hooks";
import { useEffect, useState } from "react";
import FileUploadInput from "@/components/common/FileUploadInput";

const UploadSKForm: React.FC<FormStepperProps> = ({ onFormValid }) => {
  const dispatch: AppDispatch = useDispatch();
  const { sk } = useSelector((state: RootState) => state.distributorRegister);

  const [checked, setChecked] = useState(false);
  const id = useId().replace("mantine-", "");

  useEffect(() => {
    onFormValid(checked);
  }, [checked]);

  return (
    <Stack gap={50}>
      <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="xl">
        <FileUploadInput
          required
          label="Upload Surat Kerja yang telah Ditandatangan"
          prefix={`distributor-sk/${id}`}
          fileType="pdf"
          onChange={(url) => {
            dispatch(setNewSk(url));
          }}
        />
        <Input.Wrapper label="Uploaded Surat Kerja">
          {!sk ? (
            <Text c="text">Belum ada file diupload!</Text>
          ) : (
            <UploadedFileRow
              fileUrl={sk}
              onDelete={() => {
                if (confirm("Apakah anda yakin ingin menghapus file ini?")) {
                  dispatch(setNewSk(""));
                  setChecked(false);
                }
              }}
            />
          )}
        </Input.Wrapper>
      </SimpleGrid>
      <Checkbox
        disabled={!sk}
        checked={checked}
        classNames={classes}
        onChange={(target) => setChecked(target.currentTarget.checked)}
        label="Saya dapat mempertanggungjawabkan Surat Kerja ini adalah asli dan benar"
        description="Saya memahami bahwa pelanggaran terhadap syarat & ketentuan ini dapat membuat NIK & NPWP saya diblacklist dari aplikasi distributor Hotto ke depannya."
      />
    </Stack>
  );
};

export default UploadSKForm;
