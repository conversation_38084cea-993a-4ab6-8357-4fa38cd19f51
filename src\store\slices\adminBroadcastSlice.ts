import api from "@/lib/axios";
import { createSlice } from "@reduxjs/toolkit";
import { AppDispatch } from "..";
import {
  GET_ADMIN_BROADCAST_BY_ID,
  GET_ADMIN_BROADCASTS,
  POST_ADMIN_BROADCAST,
} from "@/utils/api-routes";
import { toast } from "sonner";
import {
  AdminBroadcast,
  AdminPastBroadcast,
  CreateBroadcast,
} from "@/utils/types/broadcast";

interface AdminBroadcastState {
  pastBroadcasts: AdminPastBroadcast[];
  draftBroadcasts: AdminBroadcast[];
  broadcastDetail: AdminBroadcast | undefined;
  error: undefined;
  loading: false;
}

const initialState: AdminBroadcastState = {
  pastBroadcasts: [],
  draftBroadcasts: [],
  broadcastDetail: undefined,
  error: undefined,
  loading: false,
};

const adminBroadcastSlice = createSlice({
  name: "adminBroadcast",
  initialState,
  reducers: {
    setError(state, action) {
      state.error = action.payload;
      if (state.error) {
        toast.error(action.payload);
      }
    },
    setLoading(state, action) {
      state.loading = action.payload;
    },
    setPastBroadcasts(state, action) {
      state.pastBroadcasts = action.payload;
    },
    setDraftBroadcasts(state, action) {
      state.draftBroadcasts = action.payload;
    },
    setBroadcastDetail(state, action) {
      state.broadcastDetail = action.payload;
    },
  },
});

export const fetchDraftBroadcasts = () => async (dispatch: AppDispatch) => {
  try {
    dispatch(adminBroadcastSlice.actions.setLoading(true));
    dispatch(adminBroadcastSlice.actions.setError(undefined));
    const res = await api.get(GET_ADMIN_BROADCASTS(true));
    if (res) {
      dispatch(
        adminBroadcastSlice.actions.setDraftBroadcasts(res.data.data.broadcasts)
      );
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      dispatch(adminBroadcastSlice.actions.setError(error.message));
    } else {
      dispatch(
        adminBroadcastSlice.actions.setError(`Error fetching broadcast drafts`)
      );
    }
  } finally {
    dispatch(adminBroadcastSlice.actions.setLoading(false));
  }
};

export const fetchBroadcasts = () => async (dispatch: AppDispatch) => {
  try {
    dispatch(adminBroadcastSlice.actions.setLoading(true));
    dispatch(adminBroadcastSlice.actions.setError(undefined));
    const res = await api.get(GET_ADMIN_BROADCASTS(false));
    if (res) {
      dispatch(
        adminBroadcastSlice.actions.setPastBroadcasts(res.data.data.broadcasts)
      );
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      dispatch(adminBroadcastSlice.actions.setError(error.message));
    } else {
      dispatch(
        adminBroadcastSlice.actions.setError(`Error fetching past broadcasts`)
      );
    }
  } finally {
    dispatch(adminBroadcastSlice.actions.setLoading(false));
  }
};

export const deleteBroadcast =
  (id: number) => async (dispatch: AppDispatch) => {
    try {
      dispatch(adminBroadcastSlice.actions.setLoading(true));
      dispatch(adminBroadcastSlice.actions.setError(undefined));
      const res = await api.delete(GET_ADMIN_BROADCAST_BY_ID(id));
      if (res) {
        toast.success("Broadcast deleted successfully");
        dispatch(fetchDraftBroadcasts());
        dispatch(fetchBroadcasts());
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        dispatch(adminBroadcastSlice.actions.setError(error.message));
      } else {
        dispatch(
          adminBroadcastSlice.actions.setError("Error deleting broadcast")
        );
      }
    } finally {
      dispatch(adminBroadcastSlice.actions.setLoading(false));
    }
  };

export const createBroadcast =
  (data: CreateBroadcast, isDraft: boolean) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(adminBroadcastSlice.actions.setLoading(true));
      dispatch(adminBroadcastSlice.actions.setError(undefined));
      const res = await api.post(POST_ADMIN_BROADCAST, {
        ...data,
        isDraft: isDraft,
      });
      if (res) {
        if (isDraft) {
          toast.success("Broadcast draft saved!");
        } else {
          toast.success("Broadcast published!");
        }
        dispatch(fetchDraftBroadcasts());
        dispatch(fetchBroadcasts());
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        dispatch(adminBroadcastSlice.actions.setError(error.message));
      } else {
        dispatch(
          adminBroadcastSlice.actions.setError("Error creating broadcast")
        );
      }
    }
  };

export const updateBroadcast =
  (broadcastId: number, data: CreateBroadcast, isDraft: boolean) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(adminBroadcastSlice.actions.setLoading(true));
      dispatch(adminBroadcastSlice.actions.setError(undefined));
      const res = await api.put(GET_ADMIN_BROADCAST_BY_ID(broadcastId), {
        ...data,
        isDraft: isDraft,
      });
      if (res) {
        if (isDraft) {
          toast.success("Broadcast draft saved!");
        } else {
          toast.success("Broadcast published!");
        }
        dispatch(fetchBroadcasts());
        dispatch(fetchDraftBroadcasts());
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        dispatch(adminBroadcastSlice.actions.setError(error.message));
      } else {
        dispatch(
          adminBroadcastSlice.actions.setError("Error updating broadcast")
        );
      }
    }
  };

export const fetchBroadcastDetails =
  (id: number) => async (dispatch: AppDispatch) => {
    try {
      dispatch(adminBroadcastSlice.actions.setLoading(true));
      dispatch(adminBroadcastSlice.actions.setError(undefined));
      const res = await api.get(GET_ADMIN_BROADCAST_BY_ID(id));
      if (res) {
        dispatch(adminBroadcastSlice.actions.setBroadcastDetail(res.data.data));
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        dispatch(adminBroadcastSlice.actions.setError(error.message));
      } else {
        dispatch(
          adminBroadcastSlice.actions.setError("Error fetching broadcast")
        );
      }
    } finally {
      dispatch(adminBroadcastSlice.actions.setLoading(false));
    }
  };

export const resetBroadcastDetails = () => async (dispatch: AppDispatch) => {
  dispatch(adminBroadcastSlice.actions.setBroadcastDetail(undefined));
};

export default adminBroadcastSlice.reducer;
