import Announcement from "@/components/section/Login/DistributorAgentLogin/Announcement";
import DistributorAgentLogin from "@/components/section/Login/DistributorAgentLogin/Form";
import { Center, Flex } from "@mantine/core";

const LoginPage = () => {
  return (
    <Center h="100dvh" w="100%" p={{ base: 12, sm: 24 }}>
      <Flex
        w="100%"
        h="100%"
        gap="md"
        direction={{ base: "column", sm: "row" }}
        justify="flex-start">
        {/* <Announcement data={announcements} /> */}
        <DistributorAgentLogin />
      </Flex>
    </Center>
  );
};

export default LoginPage;
