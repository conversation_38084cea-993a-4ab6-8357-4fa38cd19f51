.steps {
  margin: 0 auto;
  width: fit-content;
  min-width: 20em;
}

.step {
  gap: 0.5rem;
  flex: 1 1 0%;
  flex-shrink: 1;
  text-align: center;
  flex-direction: column;
}

.stepBody {
  margin: 0;
}

.separator {
  margin-left: -5px;
  margin-right: -5px;
  height: 0;
  border: none;
  color: transparent;
  background-color: transparent;
  border-top: 1.5px dashed var(--mantine-color-text-light-1);
  transform: translateY(-1em);
  scale: 1.5;
  z-index: -1;

  &[data-active] {
    border-color: var(--mantine-primary-color-filled);
  }
}

.stepIcon {
  font-size: 1em;
  font-weight: 500;
  border-color: transparent;
  color: var(--mantine-color-white);
  background-color: var(--mantine-color-gray-3);

  &[data-progress] {
    background-color: var(--mantine-primary-color-filled);
  }

  &[data-completed] {
    background-color: var(--mantine-primary-color-3);
  }
}

.stepLabel {
  line-height: 1.25;
}

@media (max-width: $mantine-breakpoint-sm) {
  .stepLabel {
    font-size: var(--mantine-font-size-sm);
  }
}
