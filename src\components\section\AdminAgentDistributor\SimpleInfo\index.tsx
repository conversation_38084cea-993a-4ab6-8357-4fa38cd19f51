import Icon, { IconProps } from "@/components/common/Icon";
import { isValidUrl } from "@/utils/common-functions";
import { Anchor, Flex, Text, TextProps } from "@mantine/core";
import Link from "next/link";

export interface SimpleInfoProps {
  title?: string;
  description?: string;
  icon?: IconProps["icon"];
  iconSize?: IconProps["size"];
  truncate?: TextProps["truncate"];
}

const SimpleInfo = ({
  title,
  description,
  icon,
  iconSize,
  truncate = true,
}: SimpleInfoProps) => {
  const noTitle = !title && !icon;
  const desc = description || "-";

  return (
    <Flex
      align="flex-start"
      gap={6}
      aria-label={title}
      miw={truncate ? 0 : "auto"}
    >
      {!noTitle && (
        <Flex gap={3} c="text">
          {icon && <Icon icon={icon} size={iconSize} />}
          {title && <Text fw={400}>{title}:</Text>}
        </Flex>
      )}
      {isValidUrl(desc) ? (
        <Anchor
          component={Link}
          fw={400}
          href={desc}
          target="_blank"
          truncate={truncate}
        >
          {desc}
        </Anchor>
      ) : (
        <Text fw={400} truncate={truncate}>
          {desc}
        </Text>
      )}
    </Flex>
  );
};

export default SimpleInfo;
