import { Flex, Text } from "@mantine/core";
import { useState, useEffect } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import useBreakpoints from "@/hooks/useBreakpoints";
import FilterButton from "@/components/common/FilterButton";

const REQUEST_FILTER_DATA = [
  {
    label: "All Requests",
    value: "all",
  },
  {
    label: "Request Diterima",
    value: "received",
  },
  {
    label: "Request Dikirim",
    value: "sent",
  },
];

const RequestFilter = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [activeFilter, setActiveFilter] = useState<string>(
    searchParams.get("filter") || "all"
  );

  const handleFilter = (value: string) => {
    setActiveFilter(value);
    const params = new URLSearchParams(searchParams.toString());
    params.set("filter", value);
    router.push(`?${params.toString()}`);
  };

  useEffect(() => {
    const currentFilter = searchParams.get("filter");
    if (!currentFilter) {
      const params = new URLSearchParams(searchParams.toString());
      params.set("filter", "all");
      router.push(`?${params.toString()}`);
    }
  }, []);

  const { mobile } = useBreakpoints();
  return (
    <Flex align={"center"} gap={"md"} maw={"100%"} wrap={"wrap"} pb={"xl"}>
      <Text fw={500}>View Requests: </Text>
      <Flex gap={mobile ? "xs" : "sm"} wrap={"wrap"}>
        {REQUEST_FILTER_DATA.map((item) => (
          <FilterButton
            key={item.value}
            item={item}
            activeFilter={activeFilter}
            handleFilter={handleFilter}
          />
        ))}
      </Flex>
    </Flex>
  );
};

export default RequestFilter;
