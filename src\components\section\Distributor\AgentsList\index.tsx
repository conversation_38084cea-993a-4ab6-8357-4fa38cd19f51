'use client'

import {
 TableData,
 Group,
 Text,
 ActionIcon,
 Stack,
 Flex,
 Divider,
 Table,
 Anchor,
} from '@mantine/core'
import Icon from '@/components/common/Icon'
import ActivationStatus from '@/components/common/ActivationStatus'
import Link from 'next/link'
import { AppDispatch, RootState } from '@/store'
import { useDispatch, useSelector } from 'react-redux'
import { useEffect } from 'react'
import AgentsListHeader from './Header'
import { fetchAgentList } from '@/store/slices/distributorAgentsSlice'
import { formatDateToString } from '@/utils/common-functions'

export interface AgentsListProps {
 onApplicationFormClick?: () => void
}

const AgentsList: React.FC<AgentsListProps> = ({ onApplicationFormClick }) => {
 const dispatch: AppDispatch = useDispatch()
 const { agentList, loading } = useSelector(
  (state: RootState) => state.distributorAgents
 )

 useEffect(() => {
  dispatch(fetchAgentList())
 }, [dispatch])

 const tableData: TableData = {
  head: [
   'Nama Agent',
   'Alama<PERSON>',
   'Nomor Telepon',
   'Agent Since',
   'Last Order Date',
   'Last Ordered Item',
   'Action',
  ],
  body:
   agentList?.length > 0
    ? agentList.map((agent) => [
       <Group key={agent.uoc}>
        <Anchor component={Link} href={`/admin/agent/${agent.uoc}`}>
         {agent.name}
        </Anchor>
        <ActivationStatus status={agent.status} withBorder />
       </Group>,
       agent.address,
       agent.phone,
       agent.joinedAt,
       agent.lastOrderDate,
       agent.lastOrderItem,
       <Flex gap="sm" key={agent.uoc}>
        <ActionIcon variant="transparent" aria-label="Delete">
         <Icon icon="action/delete" />
        </ActionIcon>
        <ActionIcon variant="transparent" aria-label="Edit">
         <Icon icon="action/edit" />
        </ActionIcon>
       </Flex>,
      ])
    : undefined,
 }

 return (
  !loading && (
   <Stack gap="xs">
    <AgentsListHeader />
    <Divider size="sm" />
    {tableData.body ? (
     <Table.ScrollContainer minWidth={1024}>
      <Table
       withColumnBorders
       highlightOnHover
       verticalSpacing="md"
       data={tableData}
      />
     </Table.ScrollContainer>
    ) : (
     <Stack ta={'center'} my={50}>
      <Text fw={600}>Anda belum memiliki Agent!</Text>
      <Text>
       Segera{' '}
       <Anchor underline="always" onClick={onApplicationFormClick}>
        lengkapi form pembukaan application agent
       </Anchor>{' '}
       dan mulai hire Agent Anda!
      </Text>
     </Stack>
    )}
   </Stack>
  )
 )
}

export default AgentsList
