"use client";

import PageLayout from "@/components/common/PageLayout";
import dynamic from "next/dynamic";

const AdminRequests = dynamic(
  () => import("@/components/section/AdminRequests"),
  { ssr: false }
);
import { withAuth } from "@/utils/withAuth";

const AdminRequestPage = () => {
  return (
    <PageLayout title="Request">
      <AdminRequests />
    </PageLayout>
  );
};

export default withAuth(AdminRequestPage);
