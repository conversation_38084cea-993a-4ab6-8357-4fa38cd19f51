import {
  IslandData,
  IslandResponse,
  RegionData,
  RegionResponse,
} from "@/utils/types/region";
import { createSlice } from "@reduxjs/toolkit";
import { AppDispatch, RootState } from "..";
import axios, { AxiosResponse } from "axios";
import { GET_ISLANDS, GET_REGIONS } from "@/utils/api-routes";
import { toast } from "sonner";
import api from "@/lib/axios";

export interface RegionState {
  islands: IslandData[];
  provinces: RegionData[];
  filteredProvinces: RegionData[];
  regencies: RegionData[];
  districts: RegionData[];
  villages: RegionData[];
  postalCodes: string[];
  error?: object | string;
  isLoading: boolean;
}

const initialState: RegionState = {
  islands: [],
  provinces: [],
  filteredProvinces: [],
  regencies: [],
  districts: [],
  villages: [],
  postalCodes: [],
  error: undefined,
  isLoading: false,
};

const regionSlice = createSlice({
  name: "region",
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
      toast.error(
        "Failed to get region data. Please refresh the page and try again."
      );
    },
    setIslands: (state, action) => {
      state.islands = action.payload;
    },
    setProvinces: (state, action) => {
      state.provinces = action.payload;
    },
    setFilteredProvinces: (state, action) => {
      state.filteredProvinces = action.payload;
    },
    setRegencies: (state, action) => {
      state.regencies = action.payload;
    },
    setDistricts: (state, action) => {
      state.districts = action.payload;
    },
    setVillages: (state, action) => {
      state.villages = action.payload;
    },
    setPostalCodes: (state, action) => {
      state.postalCodes = action.payload;
    },
  },
});

export const fetchIslands = () => async (dispatch: AppDispatch) => {
  try {
    dispatch(regionSlice.actions.setLoading(true));
    const res: AxiosResponse<IslandResponse> = await api.get(GET_ISLANDS);
    if (res.status === 200 && res.data.data) {
      dispatch(regionSlice.actions.setIslands(res.data.data.islands));
    } else {
      dispatch(regionSlice.actions.setError(res.statusText));
    }
  } catch (error) {
    dispatch(regionSlice.actions.setError(error));
  } finally {
    dispatch(regionSlice.actions.setLoading(false));
  }
};

export const fetchProvinces =
  (island?: string) =>
  async (dispatch: AppDispatch, getState: () => RootState) => {
    if (!island) return;
    const state = getState();
    const provinces = state.region.provinces;

    if (provinces.length === 0) {
      try {
        dispatch(regionSlice.actions.setLoading(true));
        const res: AxiosResponse<RegionResponse> = await axios.get(
          GET_REGIONS("provinces")
        );
        if (res.status === 200 && res.data.data) {
          dispatch(regionSlice.actions.setProvinces(res.data.data));
          dispatch(regionSlice.actions.setRegencies([]));
          dispatch(regionSlice.actions.setDistricts([]));
          dispatch(regionSlice.actions.setVillages([]));
          dispatch(regionSlice.actions.setPostalCodes([]));
        } else {
          dispatch(regionSlice.actions.setError(res.statusText));
        }
      } catch (error) {
        dispatch(regionSlice.actions.setError(error));
      } finally {
        dispatch(regionSlice.actions.setLoading(false));
      }
    }

    if (island) {
      dispatch(filterProvinces(island));
    }
  };

export const filterProvinces =
  (island: string) =>
  async (dispatch: AppDispatch, getState: () => RootState) => {
    if (!island) return;
    const state = getState();
    const islands = state.region.islands;
    const provinces = state.region.provinces;
    const targetIsland = islands.find(({ name }) => name === island);

    if (targetIsland && provinces.length > 0) {
      const provinceAreaCodes = targetIsland.provinceAreaCodes;
      const filteredProvinces = provinces.filter(({ code }) =>
        provinceAreaCodes.includes(parseInt(code))
      );
      dispatch(regionSlice.actions.setFilteredProvinces(filteredProvinces));
    }
  };

export const fetchRegencies =
  (provinceCode: string) => async (dispatch: AppDispatch) => {
    if (!provinceCode) return;
    try {
      dispatch(regionSlice.actions.setLoading(true));
      const res: AxiosResponse<RegionResponse> = await axios.get(
        GET_REGIONS("regencies", provinceCode)
      );
      if (res.status === 200 && res.data.data) {
        dispatch(regionSlice.actions.setRegencies(res.data.data));
        dispatch(regionSlice.actions.setDistricts([]));
        dispatch(regionSlice.actions.setVillages([]));
        dispatch(regionSlice.actions.setPostalCodes([]));
      } else {
        dispatch(regionSlice.actions.setError(res.statusText));
      }
    } catch (error) {
      dispatch(regionSlice.actions.setError(error as string));
    } finally {
      dispatch(regionSlice.actions.setLoading(false));
    }
  };

export const fetchDistricts =
  (regencyCode: string) => async (dispatch: AppDispatch) => {
    if (!regencyCode) return;
    try {
      dispatch(regionSlice.actions.setLoading(true));
      const res: AxiosResponse<RegionResponse> = await axios.get(
        GET_REGIONS("districts", regencyCode)
      );
      if (res.status === 200 && res.data.data) {
        dispatch(regionSlice.actions.setDistricts(res.data.data));
        dispatch(regionSlice.actions.setVillages([]));
        dispatch(regionSlice.actions.setPostalCodes([]));
      } else {
        dispatch(regionSlice.actions.setError(res.statusText));
      }
    } catch (error) {
      dispatch(regionSlice.actions.setError(error as string));
    } finally {
      dispatch(regionSlice.actions.setLoading(false));
    }
  };

export const fetchVillages =
  (districtCode: string) => async (dispatch: AppDispatch) => {
    if (!districtCode) return;
    try {
      dispatch(regionSlice.actions.setLoading(true));
      dispatch(regionSlice.actions.setPostalCodes([]));
      const res: AxiosResponse<RegionResponse> = await axios.get(
        GET_REGIONS("villages", districtCode)
      );
      if (res.status === 200 && res.data.data) {
        dispatch(regionSlice.actions.setVillages(res.data.data));
        dispatch(
          regionSlice.actions.setPostalCodes([
            ...new Set(res.data.data.map(({ postal_code }) => postal_code!)),
          ])
        );
      } else {
        dispatch(regionSlice.actions.setError(res.statusText));
      }
    } catch (error) {
      dispatch(regionSlice.actions.setError(error as string));
    } finally {
      dispatch(regionSlice.actions.setLoading(false));
    }
  };

export default regionSlice.reducer;
