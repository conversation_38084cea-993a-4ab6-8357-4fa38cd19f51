"use client";

import PageLayout from "@/components/common/PageLayout";
import CustomerStatistics from "@/components/section/OnlineShop/Dashboard/CustomerStatistics";
import DownloadStatsButton from "@/components/section/OnlineShop/Dashboard/DownloadStatsButton";
import { getTodayDateTime } from "@/utils/common-functions";
import { withAuth } from "@/utils/withAuth";
import { Flex, Text } from "@mantine/core";

const OnlineShopCustomerPage = () => {
  const backUrl = window.location.pathname.includes("/admin")
    ? "/admin/shop/dashboard"
    : "/shop/dashboard";

  const NavigationDescription = () => {
    return (
      <Flex gap={"4px"}>
        <Text size="sm" c={"text-light"}>
          Update terakhir:
        </Text>
        <Text size="sm" c={"text-light"} fw={700}>
          {getTodayDateTime()}
        </Text>
      </Flex>
    );
  };

  return (
    <PageLayout
      title="Online Shop Dashboard"
      navigationContent={{
        title: "Statistik Pelanggan",
        description: <NavigationDescription />,
        rightContent: <DownloadStatsButton />,
        backUrl,
      }}
    >
      <CustomerStatistics />
    </PageLayout>
  );
};

export default withAuth(OnlineShopCustomerPage);
