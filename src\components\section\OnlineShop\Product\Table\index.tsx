import Icon from "@/components/common/Icon";
import useBreakpoints from "@/hooks/useBreakpoints";
import {
  ActionIcon,
  Box,
  Button,
  Checkbox,
  Group,
  Paper,
  Switch,
  Table,
  Text,
  Flex,
  Image,
  Loader,
  Center,
} from "@mantine/core";
import { useDisclosure, useElementSize } from "@mantine/hooks";
import { Fragment, useEffect, useState } from "react";
import DeleteConfirmationModal from "../DeleteConfirmationModal";
import { parseToRupiah } from "@/utils/common-functions";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchProducts,
  setSelectedProducts,
  setOrder,
  updateProductStatus,
} from "@/store/slices/onlineShopSlices/productListSlice";
import ProductInputFilter from "../Filter/Input";
import Link from "next/link";
import { parseAsString, useQueryState } from "nuqs";
import Placeholder from "@/components/common/Placeholder";

const ProductTable: React.FC = () => {
  const dispatch: AppDispatch = useDispatch();
  const [switches, setSwitches] = useState<{ id: number; status: boolean }[]>(
    []
  );
  const [opened, handlers] = useDisclosure(false);
  const [search, setSearch] = useQueryState(
    "search",
    parseAsString.withDefault("")
  );
  const { ref, height } = useElementSize();
  const { mobile } = useBreakpoints();

  const { role } = useSelector((state: RootState) => state.auth);
  const { products, selectedProducts, status, loading, order, statusLoading } =
    useSelector((state: RootState) => state.productList);

  const handleSelect = (id: number, selected: boolean) => {
    dispatch(
      setSelectedProducts(
        selected
          ? [...selectedProducts, id]
          : selectedProducts.filter((i) => i !== id)
      )
    );
  };

  const handleSelectAll = (selected: boolean) => {
    dispatch(
      setSelectedProducts(selected ? filteredData.map(({ id }) => id) : [])
    );
  };

  const handleStatusChange = (id: number, newStatus: boolean) => {
    dispatch(updateProductStatus(id, newStatus)).then(() => {
      setSwitches((prevSwitches) => {
        const switchExists = prevSwitches.some((s) => s.id === id);
        if (switchExists) {
          return prevSwitches.map((s) =>
            s.id === id ? { ...s, status: newStatus } : s
          );
        } else {
          return [...prevSwitches, { id, status: newStatus }];
        }
      });
    });
  };

  const getLatestStatus = (id: number, initialStatus: boolean): boolean => {
    const foundSwitch = switches.find((s) => s.id === id);
    return foundSwitch ? foundSwitch.status : initialStatus;
  };

  const handleDelete = (id: number) => {
    dispatch(setSelectedProducts([id]));
    handlers.open();
  };

  const handleSearch = (value: string) => {
    setSearch(value);
    if (selectedProducts.length > 0) {
      dispatch(setSelectedProducts([]));
    }
  };

  useEffect(() => {
    dispatch(fetchProducts());
  }, [status, order]);

  const filteredData = products.filter((product) =>
    search.toLowerCase() === ""
      ? product
      : product.name.toLowerCase().includes(search.toLowerCase())
  );

  const empty = !products || products.length === 0;

  const DeleteAction = (
    <Box>
      <Box h={height + 20} />
      <Paper
        ref={ref}
        pos="fixed"
        bottom={0}
        left={0}
        miw="100%"
        p={{ base: "sm", md: "md" }}
        radius={0}
        style={{ borderTop: "1px solid var(--mantine-color-bg-filled)" }}
      >
        <Group justify="space-between" ml={{ md: 295 }}>
          <Text fw={500}>{selectedProducts.length} produk terpilih</Text>
          <Button
            color="error"
            fullWidth={mobile}
            onClick={handlers.open}
            mb={{ base: "sm", md: 0 }}
          >
            Hapus
          </Button>
        </Group>
      </Paper>
    </Box>
  );

  return (
    <>
      <DeleteConfirmationModal
        opened={opened}
        onClose={() => {
          handlers.close();
          dispatch(setSelectedProducts([]));
        }}
      />

      <ProductInputFilter
        onSearchChange={handleSearch}
        onOrderChange={(value) => dispatch(setOrder(value))}
        searchValue={search}
      />

      {loading ? (
        <Center mt="30dvh">
          <Loader size="xl" type="dots" />
        </Center>
      ) : filteredData.length === 0 ? (
        <Placeholder
          mt="10dvh"
          label="produk"
          description="Silahkan tambahkan produk terlebih dahulu"
          empty={empty}
        />
      ) : (
        <Table.ScrollContainer minWidth={768} mb="xl">
          <Table
            verticalSpacing="md"
            horizontalSpacing="md"
            highlightOnHover
            withTableBorder
            withColumnBorders
          >
            <Table.Thead>
              <Table.Tr c="text">
                <Table.Th w={1}>
                  <Checkbox
                    aria-label="Select all"
                    title="Select all"
                    indeterminate={
                      selectedProducts.length > 0 &&
                      selectedProducts.length < filteredData.length
                    }
                    checked={selectedProducts.length === filteredData.length}
                    onChange={(e) => handleSelectAll(e.currentTarget.checked)}
                  />
                </Table.Th>
                <Table.Th>Info Produk</Table.Th>
                <Table.Th w={1}>Varian</Table.Th>
                <Table.Th w={1}>Harga</Table.Th>
                <Table.Th w={1}>Stok</Table.Th>
                <Table.Th w={1}>Status</Table.Th>
                <Table.Th w={100}>Aksi</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody style={{ verticalAlign: "top" }}>
              {filteredData.map((product) => (
                <Fragment key={product.id}>
                  <Table.Tr
                    bg={
                      selectedProducts.includes(product.id) ? "bg" : undefined
                    }
                  >
                    <Table.Td>
                      <Checkbox
                        aria-label={`Select ${product.name}`}
                        title={`Select ${product.name}`}
                        checked={selectedProducts.includes(product.id)}
                        onChange={(e) =>
                          handleSelect(product.id, e.currentTarget.checked)
                        }
                      />
                    </Table.Td>
                    <Table.Td>
                      <Flex gap="md" direction={mobile ? "column" : "row"}>
                        <Image
                          w={100}
                          h={100}
                          width={100}
                          height={100}
                          radius="md"
                          fit="cover"
                          src={product.images[0]}
                          alt={product.name}
                          style={{
                            border:
                              "2px solid var(--mantine-primary-color-filled)",
                            aspectRatio: 1,
                          }}
                        />
                        <Text>{product.name}</Text>
                      </Flex>
                    </Table.Td>
                    <Table.Td>{product.variant}</Table.Td>
                    <Table.Td>{parseToRupiah(product.unitPrice)}</Table.Td>
                    <Table.Td>{product.totalStocks}</Table.Td>
                    <Table.Td>
                      <Switch
                        size="lg"
                        checked={getLatestStatus(product.id, product.status)}
                        disabled={statusLoading}
                        onChange={(e) =>
                          handleStatusChange(
                            product.id,
                            e.currentTarget.checked
                          )
                        }
                      />
                    </Table.Td>
                    <Table.Td>
                      <Group gap={5}>
                        <ActionIcon
                          variant="transparent"
                          aria-label="Delete"
                          onClick={() => handleDelete(product.id)}
                        >
                          <Icon icon="action/delete" />
                        </ActionIcon>
                        <ActionIcon
                          component={Link}
                          href={`${
                            role === "admin" ? "/admin" : ""
                          }/shop/product/${product.id}/edit`}
                          variant="transparent"
                          aria-label="Edit"
                        >
                          <Icon icon="action/edit" />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                </Fragment>
              ))}
            </Table.Tbody>
          </Table>
        </Table.ScrollContainer>
      )}

      {selectedProducts.length > 0 && !opened && DeleteAction}
    </>
  );
};

export default ProductTable;
