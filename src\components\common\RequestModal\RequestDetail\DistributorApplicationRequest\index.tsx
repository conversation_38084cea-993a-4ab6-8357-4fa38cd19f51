import { AppDispatch } from "@/store";
import { fetchRequestDetail } from "@/store/slices/adminRequestSlice";
import { RequestType } from "@/utils/types/request";
import { Stack, Stepper } from "@mantine/core";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import ReviewStepModal from "./ReviewStep";
import OrderSection from "./OrderSection";
import { useAdminRequestContext } from "../../../../../context/AdminRequestContext";

interface RequestDetailContentProps {
  requestId: number;
  applicationStep?: number;
}

const STEPS = [
  {
    label: "Approval Distributor",
    step: <ReviewStepModal />,
  },
  {
    label: "Pengaturan Pengiriman",
    step: <OrderSection />,
  },
  {
    label: "Konfirmasi Pembayaran",
    step: <ReviewStepModal />,
  },
  {
    label: "Konfirmasi Surat Kerja",
    step: <ReviewStepModal />,
  },
];

const DistributorApplicationRequestDetail: React.FC<
  RequestDetailContentProps
> = ({ requestId, applicationStep = 1 }) => {
  const dispatch: AppDispatch = useDispatch();
  const { active } = useAdminRequestContext();

  useEffect(() => {
    dispatch(fetchRequestDetail(requestId, RequestType.Application));
  }, [requestId]);

  return (
    <Stack align="center" w="100%">
      <Stepper w="100%" active={active!} allowNextStepsSelect={false}>
        {STEPS.map(({ label, step }, index) => (
          <Stepper.Step key={index} label={label}>
            {step}
          </Stepper.Step>
        ))}
      </Stepper>
    </Stack>
  );
};

export default DistributorApplicationRequestDetail;
