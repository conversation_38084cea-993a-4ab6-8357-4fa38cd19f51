import api from "@/lib/axios";
import {
  RequestCardData,
  AdminRequestDetailChangeField,
  AdminRequestDetailReactivateAccount,
  AdminRequestDetailTerminateAgent,
  AdminRequestResponse,
  AdminRequestUpdateStatusData,
  RequestType,
  NewApplicationData,
} from "@/utils/types/request";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { AppDispatch, RootState } from "..";
import { toast } from "sonner";
import {
  GET_ADMIN_REQUEST_DETAIL,
  GET_ADMIN_REQUESTS,
  GET_ADMIN_REQUESTS_COUNT,
  UPDATE_ADMIN_REQUEST_STATUS,
} from "@/utils/api-routes";
import { RoleType } from "./authSlice";
import { isEmpty } from "@/utils/common-functions";

interface AdminRequestState {
  adminRequestResponse: AdminRequestResponse | undefined;
  requests: RequestCardData[];
  newRequestCount: number;
  dataChangeRequest: AdminRequestDetailChangeField | undefined;
  terminationRequest: AdminRequestDetailTerminateAgent | undefined;
  reactivationRequest: AdminRequestDetailReactivateAccount | undefined;
  applicationRequest: NewApplicationData | undefined;
  firstOrdersToUpdate: {
    orderId: number;
    shippingCost?: number;
    rejectReason?: string;
  }[];
  currFilter: string;
  currSortBy: string;
  error: string | undefined;
  loading: boolean;
}

const initialState: AdminRequestState = {
  dataChangeRequest: undefined,
  terminationRequest: undefined,
  reactivationRequest: undefined,
  applicationRequest: undefined,
  adminRequestResponse: undefined,
  firstOrdersToUpdate: [],
  currFilter: "",
  currSortBy: "desc",
  requests: [],
  newRequestCount: 0,
  error: undefined,
  loading: false,
};

const adminRequestSlice = createSlice({
  name: "adminRequestSlice",
  initialState,
  reducers: {
    setError(state, action) {
      state.error = action.payload;
      toast.error(action.payload);
    },
    setLoading(state, action) {
      state.loading = action.payload;
    },
    setRequests(state, action) {
      state.adminRequestResponse = action.payload;
      if (action.payload.page === 1) {
        state.requests = [];
      }
      state.requests.push(...action.payload.requests);
    },
    setNewRequestCount(state, action) {
      state.newRequestCount = action.payload.count;
    },
    setRequestDetail(state, action) {
      switch (action.payload.requestType) {
        case RequestType.DataChange:
          state.dataChangeRequest = action.payload.data;
          break;
        case RequestType.Termination:
          state.terminationRequest = action.payload.data;
          break;
        case RequestType.Reactivation:
          state.reactivationRequest = action.payload.data;
          break;
        case RequestType.Application:
          state.applicationRequest = action.payload.data;
          break;
      }
    },
    setCurrFilter(state, action) {
      state.currFilter = action.payload;
    },
    setCurrSortBy(state, action) {
      state.currSortBy = action.payload;
    },
    resetFirstOrdersToUpdate(state) {
      state.firstOrdersToUpdate = initialState.firstOrdersToUpdate;
    },
    setFirstOrderToUpdate: (
      state,
      action: PayloadAction<{
        orderId: number;
        shippingCost?: number;
        rejectReason?: string;
      }>
    ) => {
      const existingIndex = state.firstOrdersToUpdate.findIndex(
        (order) => order.orderId === action.payload.orderId
      );
      if (existingIndex !== -1) {
        state.firstOrdersToUpdate[existingIndex] = action.payload;
      } else {
        state.firstOrdersToUpdate.push(action.payload);
      }
    },
  },
});

const act = adminRequestSlice.actions;

export const fetchAdminRequests =
  (page: number, status: string = "all", sortBy: string = "desc") =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(act.setLoading(true));
      dispatch(act.setCurrFilter(status));
      dispatch(act.setCurrSortBy(sortBy));
      const res = await api.get(GET_ADMIN_REQUESTS(page, status, sortBy));
      dispatch(act.setRequests(res.data.data));
    } catch (error: unknown) {
      dispatch(
        act.setError(
          error instanceof Error ? error.message : "Error fetching requests"
        )
      );
    } finally {
      dispatch(act.setLoading(false));
    }
  };

export const fetchAdminRequestsCount =
  (status: string = "") =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(act.setLoading(true));
      const res = await api.get(GET_ADMIN_REQUESTS_COUNT(status));
      dispatch(act.setNewRequestCount(res.data.data));
    } catch (error: unknown) {
      dispatch(
        act.setError(
          error instanceof Error
            ? error.message
            : "Error fetching requests count"
        )
      );
    } finally {
      dispatch(act.setLoading(false));
    }
  };

export const fetchRequestDetail =
  (requestId: number, requestType: RequestType, field?: string) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(act.setLoading(true));
      const res = await api.get(GET_ADMIN_REQUEST_DETAIL(requestId, field));
      res.data.requestType = requestType;
      dispatch(act.setRequestDetail(res.data));
    } catch (error: unknown) {
      dispatch(
        act.setError(
          error instanceof Error
            ? error.message
            : "Error fetching request detail"
        )
      );
    } finally {
      dispatch(act.setLoading(false));
    }
  };

export const updateRequestStatus =
  (
    requestId: number,
    data: AdminRequestUpdateStatusData,
    userRole?: RoleType
  ) =>
  async (dispatch: AppDispatch, getState: () => RootState) => {
    try {
      const state = getState().adminRequest;
      dispatch(act.setLoading(true));
      const dataToSubmit = {
        ...data,
        firstOrdersToUpdate: !isEmpty(state.firstOrdersToUpdate)
          ? state.firstOrdersToUpdate.filter(
              (order) => order.rejectReason !== ""
            )
          : undefined,
      };
      console.log(dataToSubmit);
      const res = await api.put(
        UPDATE_ADMIN_REQUEST_STATUS(requestId),
        dataToSubmit
      );
      if (res) {
        toast.success(`Request berhasil di-${data.status}`);
        if (userRole === "admin") {
          dispatch(fetchAdminRequests(1, state.currFilter, state.currSortBy));
        }
        return true;
      }
      return false;
    } catch (error: unknown) {
      dispatch(
        act.setError(
          error instanceof Error ? error.message : "Error updating request"
        )
      );
      return false;
    } finally {
      dispatch(act.setLoading(false));
    }
  };

export const { setFirstOrderToUpdate, resetFirstOrdersToUpdate } = act;

export default adminRequestSlice.reducer;
