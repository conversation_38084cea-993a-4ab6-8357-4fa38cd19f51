import { SPACES } from "@/styles/spacing";
import styled from "styled-components";

const InfiniteScrollWrapper = styled.div`
  .infinite-scroll {
    display: flex;
    flex-direction: column;
    gap: ${SPACES.md};
    padding-bottom: ${SPACES.md};
    overflow-x: hidden;
  }

  .end-of-requests {
    cursor: pointer;
    transition: ease-in-out 0.4s;

    &:hover {
      transform: translateY(2px);
    }
  }
`;

export default InfiniteScrollWrapper;
