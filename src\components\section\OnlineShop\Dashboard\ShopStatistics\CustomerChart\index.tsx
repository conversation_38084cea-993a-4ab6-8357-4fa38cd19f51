import DonutChart from "@/components/common/DonutChart";
import SectionHeader from "@/components/common/SectionHeader";
import { RootState } from "@/store";
import { getTodayDateTime, numberToString } from "@/utils/common-functions";
import { Flex, Skeleton, Stack, Text } from "@mantine/core";
import { useSelector } from "react-redux";

const CustomerChart = () => {
  const {
    customerStatsOverview,
    loading: { customerStatsOverview: loading },
  } = useSelector((state: RootState) => state.dashboard);

  const isAdmin = window.location.pathname.includes("/admin/");
  const customerUrl = isAdmin
    ? "/admin/shop/dashboard/customer"
    : "/shop/dashboard/customer";

  const LastUpdate = () => (
    <Flex gap={"4px"}>
      <Text c={"text-light"} size="sm">
        {"Update Terakhir: "}
      </Text>
      <Text c={"text-light"} size="sm" fw={700}>
        {getTodayDateTime()}
      </Text>
    </Flex>
  );

  const CHART_DATA =
    customerStatsOverview?.topSpenders.map((spender) => ({
      name: spender.name,
      value: spender.totalSpend,
    })) || [];

  const description = numberToString(
    CHART_DATA.reduce((acc, item) => {
      return acc + item.value;
    }, 0)
  );

  return (
    <Stack w={"100%"}>
      <SectionHeader
        title="Statistik Pelanggan"
        description={
          loading ? <Skeleton w="200px" height="12px" /> : <LastUpdate />
        }
        link={customerUrl}
        linkLabel="Lihat"
        loading={loading}
      />
      {loading ? (
        <Skeleton height={300} radius="md" />
      ) : (
        <DonutChart
          chartLabel="Total Transaksi"
          chartDescription={description}
          legendLabel="Top Spender"
          isPrice
          data={CHART_DATA}
          legendPosition="bottom"
        />
      )}
    </Stack>
  );
};

export default CustomerChart;
