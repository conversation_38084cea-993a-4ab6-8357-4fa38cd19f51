"use client";

import Footer from "@/components/common/Footer";
import RegistrationFullModal from "@/components/section/Register/Distributor/RegistrationFullModal";
import RegisterUploadSK from "@/components/section/Register/Distributor/Upload/SK";
import useBreakpoints from "@/hooks/useBreakpoints";
import { RootState } from "@/store";
import { Center, Stack, Image } from "@mantine/core";
import { useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { useSelector } from "react-redux";

const RegisterUploadSKPage = () => {
  const searchParams = useSearchParams();
  const token = searchParams.get("token")!;

  const { mobile } = useBreakpoints();
  const { role, accessToken } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (!token) window.location.href = "/login";
  }, [token]);

  useEffect(() => {
    if (accessToken) window.location.href = "/";
  }, [accessToken, role]);

  return (
    <>
      <Center p={{ base: 12, sm: 16, md: 24 }}>
        <Stack w="100%" gap="xl">
          <Image
            radius="md"
            src={`/images/register/hero${mobile ? "-mobile" : ""}.png`}
            alt="hero"
          />
          <RegisterUploadSK token={token} />
          <Footer pt={50} />
        </Stack>
      </Center>
      <RegistrationFullModal />
    </>
  );
};

export default RegisterUploadSKPage;
