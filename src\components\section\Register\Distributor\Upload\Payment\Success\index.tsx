import Check from "@/components/icons/Check";
import { Text, Stack, Box, Title } from "@mantine/core";

const SuccessUploadPayment: React.FC = () => {
  return (
    <Stack justify="center" align="center" ta="center" mb="3rem">
      <Title order={2}><PERSON><PERSON>at, Aplikasi Anda Diterima!</Title>
      <Box my="xl">
        <Check fontSize="8rem" color="var(--mantine-color-success-filled)" />
      </Box>
      <Stack>
        <Text fw={600} fz="h3">
          <PERSON><PERSON><PERSON>ah Kami Terima!
        </Text>
        <Text c="text">
          Silakan tunggu 3-5 hari kerja untuk mendapatkan kabar lebih lanjut
          dari kami via email.
        </Text>
      </Stack>
    </Stack>
  );
};

export default SuccessUploadPayment;
