import { AppDispatch, RootState } from "@/store";
import { Box, Checkbox, Divider, Table } from "@mantine/core";
import { useDispatch, useSelector } from "react-redux";
import { setSelectedOrders } from "@/store/slices/onlineShopSlices/orderListSlice";

const OrderListHeader: React.FC = () => {
  const dispatch: AppDispatch = useDispatch();
  const {
    data: { orders },
    selectedOrders,
  } = useSelector((state: RootState) => state.orderList);

  const handleSelectAll = (selected: boolean) => {
    dispatch(setSelectedOrders(selected ? orders.map(({ id }) => id) : []));
  };

  return (
    <Box>
      <Divider size="sm" />
      <Box px="sm" py="xs">
        <Table withRowBorders={false} c="text">
          <Table.Thead>
            <Table.Tr>
              <Table.Th w={1}>
                <Checkbox
                  aria-label="Select all"
                  title="Select all"
                  indeterminate={
                    selectedOrders.length > 0 &&
                    selectedOrders.length < orders.length
                  }
                  checked={selectedOrders.length === orders.length}
                  onChange={(e) => handleSelectAll(e.currentTarget.checked)}
                />
              </Table.Th>
              <Table.Th w="40%">Produk</Table.Th>
              <Table.Th w="15%">Total Pesanan</Table.Th>
              <Table.Th w="15%">Status</Table.Th>
              <Table.Th w="15%">Kurir</Table.Th>
              <Table.Th w="15%">Alamat</Table.Th>
            </Table.Tr>
          </Table.Thead>
        </Table>
      </Box>
      <Divider size="sm" />
    </Box>
  );
};

export default OrderListHeader;
