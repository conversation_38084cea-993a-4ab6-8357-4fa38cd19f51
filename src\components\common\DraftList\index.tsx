import { <PERSON><PERSON><PERSON>, Flex, Paper, Text, Title, <PERSON><PERSON>, Menu } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import CreateBroadcastModal from "@/components/section/AdminBroadcast/CreateBroadcastModal";
import { memo, useState } from "react";
import { Draft, DraftType } from "@/utils/types";
import DraftCard from "./DraftCard";
import { ComposeRequestType } from "@/utils/types/request";
import ComposeDataChangeRequestModal from "@/components/section/DistributorRequest/ComposeDataChangeRequestModal";
import ComposeAgentTerminationModal from "@/components/section/DistributorRequest/ComposeAgentTerminationModal";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import ComposeAgentSelfTerminationModal from "@/components/section/DistributorRequest/ComposeAgentSelfTerminationModal";
import { CaretDown } from "@/utils/icons";
import classes from "./style.module.css";
import useBreakpoints from "@/hooks/useBreakpoints";

interface DraftListProps {
  drafts: Draft[];
  draftType?: DraftType;
}

const DraftList = ({
  draftType = DraftType.Broadcast,
  drafts,
}: DraftListProps) => {
  const { role } = useSelector((state: RootState) => state.auth);
  const [showDropdown, setShowDropdown] = useState(false);
  const [opened, { open, close }] = useDisclosure(false);
  const [selectedDraftId, setSelectedDraftId] = useState<number | undefined>(
    undefined
  );
  const [selectedRequestType, setSelectedRequestType] = useState<
    ComposeRequestType | undefined
  >(undefined);
  const { mobile } = useBreakpoints();

  const handleCloseModal = () => {
    setSelectedDraftId(undefined);
    close();
  };
  const handleOpenModal = (id: number | undefined) => {
    setSelectedDraftId(id);
    open();
  };

  const handleComposeClick = () => {
    switch (draftType) {
      case "Broadcast":
        handleOpenModal(undefined);
        break;
      case "Request":
        setShowDropdown(!showDropdown);
        break;
    }
  };

  const handleSelectRequestType = (requestType: ComposeRequestType) => {
    setSelectedRequestType(requestType);
    setShowDropdown(false);
    handleOpenModal(undefined);
  };

  const ComposeButton = memo(() => (
    <Menu
      shadow="md"
      opened={showDropdown}
      offset={0}
      width="target"
      classNames={classes}
      onClose={() => setShowDropdown(false)}
    >
      <Menu.Target>
        <Button
          fullWidth
          size="xl"
          onClick={handleComposeClick}
          styles={{ label: { width: "100%" } }}
          rightSection={draftType === "Request" && <CaretDown size={20} />}
        >
          Compose {draftType}
        </Button>
      </Menu.Target>
      <Menu.Dropdown>
        <Menu.Item
          onClick={() => handleSelectRequestType(ComposeRequestType.DataChange)}
        >
          Permohonan Edit Data Saya
        </Menu.Item>
        <Menu.Divider />
        <Menu.Item
          onClick={() =>
            handleSelectRequestType(ComposeRequestType.AgentTermination)
          }
        >
          Pemutusan Hubungan Agent
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  ));

  ComposeButton.displayName = "ComposeButton";

  return (
    <Flex
      direction={"column"}
      gap={"md"}
      w={mobile ? "100%" : "max(45%, 320px)"}
      pos={"relative"}
    >
      {draftType === DraftType.Broadcast && (
        <CreateBroadcastModal
          opened={opened}
          onClose={handleCloseModal}
          id={selectedDraftId}
        />
      )}
      {draftType === DraftType.Request &&
        selectedRequestType === ComposeRequestType.DataChange && (
          <ComposeDataChangeRequestModal
            opened={opened}
            onClose={handleCloseModal}
            id={selectedDraftId}
          />
        )}
      {draftType === DraftType.Request &&
        selectedRequestType === ComposeRequestType.AgentTermination &&
        (role !== "agent" ? (
          <ComposeAgentTerminationModal
            opened={opened}
            onClose={handleCloseModal}
            id={selectedDraftId}
          />
        ) : (
          <ComposeAgentSelfTerminationModal
            opened={opened}
            onClose={handleCloseModal}
            id={selectedDraftId}
          />
        ))}
      {!mobile ? (
        <ComposeButton />
      ) : (
        <Paper
          pos={"fixed"}
          bottom={0}
          right={0}
          w={"100%"}
          p={"lg"}
          pl={"30%"}
        >
          <ComposeButton />
        </Paper>
      )}
      {drafts.length > 0 && (
        <>
          {mobile ? (
            <Flex direction={"column"} gap={"sm"}>
              <Title order={5}>Draft Sebelumnya</Title>
              <Divider />
            </Flex>
          ) : (
            <Text
              c={"text-light"}
            >{`Ada ${drafts.length} draft sebelumnya`}</Text>
          )}
          <Flex direction={"column"} gap={"xs"}>
            {drafts.map((value, index) => (
              <DraftCard
                key={index}
                {...value}
                setSelectedId={handleOpenModal}
                draftType={draftType}
                requestType={value.type as ComposeRequestType}
                setSelectedRequestType={setSelectedRequestType}
              />
            ))}
          </Flex>
        </>
      )}
    </Flex>
  );
};

export default DraftList;
