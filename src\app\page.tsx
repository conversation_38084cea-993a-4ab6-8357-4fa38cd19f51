"use client";

import PageLayout from "@/components/common/PageLayout";
import { RootState } from "@/store";
import { capitalize } from "@/utils/common-functions";
import { withAuth } from "@/utils/withAuth";
import { Title } from "@mantine/core";
import { useSelector } from "react-redux";

const HomePage = () => {
  const { role } = useSelector((state: RootState) => state.auth);
  return (
    <PageLayout>
      <Title>{`Welcome ${capitalize(role)}! 👋`}</Title>
    </PageLayout>
  );
};

export default withAuth(HomePage);
