import LeftSection from "@/components/common/LeftSection";
import { agentRegisterSchema, serializeRegion } from "@/utils/common-functions";
import { yupResolver } from "@hookform/resolvers/yup";
import { NativeSelect, Stack, TextInput } from "@mantine/core";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { FormStepperProps } from "..";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { savePersonalInfo } from "@/store/slices/agentRegisterSlice";
import {
  fetchDistricts,
  fetchIslands,
  fetchProvinces,
  fetchRegencies,
  fetchVillages,
} from "@/store/slices/regionSlice";
import { Email, Phone } from "@/utils/icons";

const schema = agentRegisterSchema.pick([
  "name",
  "nik",
  "email",
  "phoneNumber",
  "island",
  "province",
  "regency",
  "district",
  "village",
  "postalCode",
  "address",
]);

const AgentPersonalInfoForm: React.FC<FormStepperProps> = ({
  onFormValid,
  nextClicked,
}) => {
  const dispatch: AppDispatch = useDispatch();
  const region = useSelector((state: RootState) => state.region);
  const { personalInfo, detailInfo } = useSelector(
    (state: RootState) => state.agentRegister
  );

  const form = useForm({
    mode: "onChange",
    resolver: yupResolver(schema),
    defaultValues: personalInfo,
  });

  useEffect(() => {
    if (nextClicked) form.trigger();
    onFormValid(form.formState.isValid);
  }, [form.formState.isValid, nextClicked]);

  useEffect(() => {
    const subscription = form.watch(() => {
      dispatch(savePersonalInfo(form.getValues()));
    });
    return () => subscription.unsubscribe();
  }, []);

  useEffect(() => {
    if (region.islands.length === 0) {
      dispatch(fetchIslands());
    }
  }, []);

  useEffect(() => {
    if (
      detailInfo.warehouseIsland ||
      detailInfo.warehouseProvince ||
      detailInfo.warehouseRegency ||
      detailInfo.warehouseDistrict ||
      detailInfo.warehouseVillage
    ) {
      if (
        personalInfo.island !== detailInfo.warehouseIsland ||
        personalInfo.province !== detailInfo.warehouseProvince ||
        personalInfo.regency !== detailInfo.warehouseRegency ||
        personalInfo.district !== detailInfo.warehouseDistrict ||
        personalInfo.village !== detailInfo.warehouseVillage
      ) {
        dispatch(fetchProvinces(personalInfo.island))
          .then(() => fetchRegencies(personalInfo.province))
          .then(() => form.resetField("regency"))
          .then(() => dispatch(fetchDistricts(personalInfo.regency)))
          .then(() => form.resetField("district"))
          .then(() => dispatch(fetchVillages(personalInfo.district)))
          .then(() => {
            form.resetField("village");
            form.resetField("postalCode");
          });
      }
    }
  }, []);

  const resetRegionValue = (
    names: (
      | "island"
      | "province"
      | "regency"
      | "district"
      | "village"
      | "postalCode"
      | "address"
    )[]
  ) => {
    for (const name of names) {
      form.setValue(name, "", {
        shouldValidate: form.getFieldState(name).isTouched,
      });
    }
  };

  return (
    <Stack w="100%" h="100%" gap="xl">
      <TextInput
        {...form.register("name")}
        error={form.formState.errors.name?.message}
        required
        label="Nama"
        autoFocus
      />
      <TextInput
        {...form.register("nik")}
        error={form.formState.errors.nik?.message}
        required
        label="NIK"
        type="number"
      />
      <TextInput
        {...form.register("email")}
        error={form.formState.errors.email?.message}
        required
        label="Email"
        type="email"
        leftSectionPointerEvents="none"
        leftSectionWidth={40}
        leftSection={<LeftSection icon={<Email />} />}
      />
      <TextInput
        {...form.register("phoneNumber")}
        error={form.formState.errors.phoneNumber?.message}
        placeholder="08xxxxxxxxxx"
        required
        label="Nomor Telepon Pribadi"
        leftSectionPointerEvents="none"
        leftSectionWidth={40}
        leftSection={<LeftSection icon={<Phone />} />}
      />
      <NativeSelect
        {...form.register("island")}
        required
        label="Pulau Domisili KTP"
        disabled={region.islands.length === 0}
        error={form.formState.errors.island?.message}
        data={[
          { value: "", label: "Select an option..." },
          ...region.islands.map(({ name }) => {
            return { value: name, label: name };
          }),
        ]}
        onChange={(e) => {
          const value = e.target.value;
          form.setValue("island", value);
          form.trigger("island");
          if (value) {
            dispatch(fetchProvinces(value)).then(() => {
              resetRegionValue([
                "province",
                "regency",
                "district",
                "village",
                "postalCode",
              ]);
            });
          }
        }}
      />
      <NativeSelect
        {...form.register("province")}
        required
        label="Provinsi KTP"
        disabled={
          region.filteredProvinces.length === 0 || !form.getValues("island")
        }
        error={form.formState.errors.province?.message}
        data={serializeRegion(region.filteredProvinces, true)}
        onChange={(e) => {
          const value = e.target.value;
          form.setValue("province", value);
          form.trigger("province");
          if (value) {
            dispatch(fetchRegencies(value)).then(() => {
              resetRegionValue([
                "regency",
                "district",
                "village",
                "postalCode",
              ]);
            });
          }
        }}
      />
      <NativeSelect
        {...form.register("regency")}
        required
        label="Kabupaten/Kota KTP"
        disabled={region.regencies.length === 0 || !form.getValues("province")}
        error={form.formState.errors.regency?.message}
        data={serializeRegion(region.regencies, true)}
        onChange={(e) => {
          const value = e.target.value;
          form.setValue("regency", value);
          form.trigger("regency");
          if (value) {
            dispatch(fetchDistricts(value)).then(() => {
              resetRegionValue(["district", "village", "postalCode"]);
            });
          }
        }}
      />
      <NativeSelect
        {...form.register("district")}
        required
        label="Kecamatan KTP"
        disabled={region.districts.length === 0 || !form.getValues("regency")}
        error={form.formState.errors.district?.message}
        data={serializeRegion(region.districts, true)}
        onChange={(e) => {
          const value = e.target.value;
          form.setValue("district", value);
          form.trigger("district");
          if (value) {
            dispatch(fetchVillages(value)).then(() => {
              resetRegionValue(["village", "postalCode"]);
            });
          }
        }}
      />
      <NativeSelect
        {...form.register("village")}
        required
        label="Kelurahan KTP"
        disabled={region.villages.length === 0 || !form.getValues("district")}
        error={form.formState.errors.village?.message}
        data={serializeRegion(region.villages, true)}
        onChange={(e) => {
          const value = e.target.value;
          form.setValue("village", value);
          form.trigger("village");
          if (value) {
            resetRegionValue(["postalCode"]);
          }
        }}
      />
      <NativeSelect
        {...form.register("postalCode")}
        required
        label="Kodepos KTP"
        disabled={region.postalCodes.length === 0 || !form.watch("village")}
        error={form.formState.errors.postalCode?.message}
        data={[
          { value: "", label: "Select an option..." },
          ...region.postalCodes,
        ]}
      />
      <TextInput
        {...form.register("address")}
        error={form.formState.errors.address?.message}
        required
        label="Alamat Lengkap KTP"
      />
    </Stack>
  );
};

export default AgentPersonalInfoForm;
