import type { SVGProps } from "react";

export default function Chevron({
  direction = "up",
  ...props
}: SVGProps<SVGSVGElement> & { direction?: "up" | "down" | "left" | "right" }) {
  const rotation = {
    up: "0",
    down: "180",
    left: "270",
    right: "90",
  }[direction];

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      style={{
        transform: `rotate(${rotation}deg)`,
        transition: "transform 0.2s ease-out",
      }}
      {...props}
    >
      <path
        fill="currentColor"
        d="M12.53 8.47a.75.75 0 0 0-1.06 0l-6 6a.75.75 0 0 0 0 1.06h13.06a.75.75 0 0 0 0-1.06z"
      />
    </svg>
  );
}
