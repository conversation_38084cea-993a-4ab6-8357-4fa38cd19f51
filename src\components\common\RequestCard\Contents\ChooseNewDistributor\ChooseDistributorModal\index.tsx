import {
  Drawer,
  Flex,
  Modal,
  Title,
  ModalProps,
  DrawerProps,
  Text,
  Button,
} from "@mantine/core";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import ButtonGroup from "@/components/common/ButtonGroup";
import useBreakpoints from "@/hooks/useBreakpoints";
import {
  fetchAvailableDistributors,
  updateSelectedDistributor,
} from "@/store/slices/agentRequestSlice";
import AvailableDistributors from "../AvailableDistributors";
import { fetchReceivedRequests } from "@/store/slices/distributorRequestSlice";

interface ChooseDistributorModalProps {
  requestId: number;
  opened: ModalProps["opened"];
  onClose: ModalProps["onClose"];
  modalSize?: ModalProps["size"];
  drawerSize?: DrawerProps["size"];
  onComplete?: () => void;
}

const ChooseDistributorModal = ({
  opened,
  onClose,
  requestId,
  modalSize = "lg",
  drawerSize = "lg",
  onComplete,
}: ChooseDistributorModalProps) => {
  const { availableDistributors, loading } = useSelector(
    (state: RootState) => state.agentRequest
  );
  const { data } = useSelector((state: RootState) => state.userProfile);

  const dispatch: AppDispatch = useDispatch();

  const [selectedDistributor, setSelectedDistributor] = useState<string>("");

  const handleSubmit = () => {
    dispatch(
      updateSelectedDistributor(selectedDistributor, requestId, onSuccess)
    );
  };

  const onSuccess = () => {
    dispatch(fetchReceivedRequests());
    onClose();
    onComplete?.();
  };

  useEffect(() => {
    if (opened) {
      setSelectedDistributor("");
      dispatch(
        fetchAvailableDistributors(data.province, data.city, data.district)
      );
    }
  }, [opened, data.province, data.city, data.district]);

  const { mobile } = useBreakpoints();
  const renderModalContent = (
    <Flex
      direction="column"
      align="center"
      justify="center"
      gap="sm"
      p={mobile ? 0 : "lg"}
    >
      <Title ta="center" order={2} mb="md">
        Cari Distributor Baru
      </Title>
      <Text c={"text-light"} fw={500} ta={"center"}>
        Silakan pilih salah satu distributor di bawah ini.
        <br />
        Aplikasi Anda akan secara otomatis terkirim
      </Text>

      {availableDistributors && availableDistributors.length > 0 && (
        <AvailableDistributors
          availableDistributors={availableDistributors}
          onSelect={setSelectedDistributor}
          selectedDistributor={selectedDistributor}
        />
      )}

      <ButtonGroup mt={mobile ? 0 : "xl"} justify={"space-between"} w={"100%"}>
        <Button variant="subtle" onClick={onClose}>
          Kembali
        </Button>
        <Button
          disabled={!selectedDistributor || loading}
          onClick={handleSubmit}
          loading={loading}
        >
          Submit
        </Button>
      </ButtonGroup>
    </Flex>
  );

  return mobile ? (
    <Drawer
      opened={opened}
      onClose={onClose}
      radius="8px 8px 0 0"
      size={drawerSize}
      position="bottom"
    >
      {opened && renderModalContent}
    </Drawer>
  ) : (
    <Modal
      opened={opened}
      onClose={onClose}
      radius={8}
      size={modalSize}
      centered
    >
      {opened && renderModalContent}
    </Modal>
  );
};

export default ChooseDistributorModal;
