import { Accordion, ActionIcon, Center, Flex, Text } from "@mantine/core";
import { useRouter, useSearchParams } from "next/navigation";
import Icon from "../Icon";

interface SearchHistoryProps {
  histories?: string[];
  onRemove?: (value: string) => void;
}

const SearchHistory = ({ histories = [], onRemove }: SearchHistoryProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleClickHistory = (value: string) => {
    if (searchParams) {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set("search", value);
      newSearchParams.set("page", "1");
      router.push(`?${newSearchParams.toString()}`);
    }
  };

  return (
    <Flex direction="column">
      <Text c="text">Pencarian Sebelumnya</Text>
      <Accordion
        chevron={false}
        disableChevronRotation
        onChange={(value) => handleClickHistory(value!)}
      >
        {histories.map((value, index) => (
          <Accordion.Item key={index} value={value}>
            <Center>
              <Accordion.Control>
                <Text my={"sm"}>{value}</Text>
              </Accordion.Control>
              <ActionIcon
                onClick={() => onRemove && onRemove(value)}
                variant="subtle"
                color="gray"
              >
                <Icon icon="close" size={14} />
              </ActionIcon>
            </Center>
          </Accordion.Item>
        ))}
      </Accordion>
    </Flex>
  );
};

export default SearchHistory;
