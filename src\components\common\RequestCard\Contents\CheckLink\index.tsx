import { formatTimeStamp } from "@/utils/common-functions";
import { RequestCardData } from "@/utils/types/request";
import { Button, Flex, Text } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { useRouter } from "next/navigation";

const CheckLinkRequestContent = ({ ...request }: RequestCardData) => {
  const router = useRouter();
  const isMobile = useMediaQuery("(max-width: 768px)");

  return (
    <Flex
      direction={isMobile ? "column" : "row"}
      justify={"space-between"}
      p={"lg"}
      gap={"xl"}
    >
      <Flex direction={"column"} gap={"xs"}>
        <Text fw={600}>
          Terdapat {request.metadatas[0].newValue} link marketplace untuk
          diperiksa
        </Text>
        <Text c={"text-light"} size="sm" fw={500}>
          {formatTimeStamp(request.createdAt)}
        </Text>
      </Flex>
      <Button
        onClick={() => router.push("/admin/request/links")}
        radius={"8px"}
      >
        View
      </Button>
    </Flex>
  );
};

export default CheckLinkRequestContent;
