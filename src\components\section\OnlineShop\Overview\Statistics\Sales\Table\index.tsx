import {
  Flex,
  Stack,
  Table,
  Skeleton,
  Text,
  NumberFormatter,
  MantineStyleProp,
  Group,
  Anchor,
  Avatar,
} from "@mantine/core";
import Pagination from "@/components/common/Pagination";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { ITEMS_PER_PAGE } from "@/utils/constants";
import { isEmpty } from "@/utils/common-functions";
import { fetchTopSellingStores } from "@/store/slices/onlineShopSlices/overviewSlice";
import SectionHeader from "@/components/common/SectionHeader";
import { Star } from "@/utils/icons";
import useOverviewFilters from "@/hooks/useOverviewFilters";
import Link from "next/link";

const TABLE_HEADERS = [
  "Nama",
  "Rating",
  "Kecepatan Pelayanan",
  "Produk Terjual",
  "Produk Terlaris",
  "<PERSON><PERSON><PERSON><PERSON>",
  "Pendapatan",
];

const SalesStatisticsTable = () => {
  const dispatch: AppDispatch = useDispatch();
  const {
    topSellingStores: { storeStats, totalItems },
    loading: { topSellingStores: loading },
  } = useSelector((state: RootState) => state.overview);
  const { page, setPage } = useOverviewFilters();

  useEffect(() => {
    if (!loading) dispatch(fetchTopSellingStores(page));
  }, [page]);

  const tableStyle: MantineStyleProp = {
    borderTop: "1px solid var(--mantine-color-bg-gray-filled)",
    borderBottom: "1px solid var(--mantine-color-bg-gray-filled)",
  };

  const headerStyle: MantineStyleProp = {
    borderInlineEnd: "none",
  };

  const loadingRows = Array(5)
    .fill(null)
    .map((_, index) => (
      <Table.Tr key={index}>
        {Array(6)
          .fill(null)
          .map((_, cellIndex) => (
            <Table.Td key={cellIndex}>
              <Skeleton height={20} />
            </Table.Td>
          ))}
      </Table.Tr>
    ));

  const tableRows = !isEmpty(storeStats) ? (
    storeStats.map((data) => (
      <Table.Tr key={data.userId}>
        <Table.Td>
          <Group gap="xs">
            <Avatar name={data.name} color="initials" />
            <Anchor
              c="black"
              component={Link}
              href={`/admin/shop/overview/store/${data.userId}`}
            >
              {data.name}
            </Anchor>
          </Group>
        </Table.Td>
        <Table.Td>
          <Group gap={5} wrap="nowrap">
            <Star />
            <span>{data.rating}/5</span>
          </Group>
        </Table.Td>
        <Table.Td>{data.serviceSpeed}</Table.Td>
        <Table.Td>{data.productSold}</Table.Td>
        <Table.Td>{data.bestSellingProduct}</Table.Td>
        <Table.Td>{data.totalCustomers}</Table.Td>
        <Table.Td>
          <NumberFormatter value={data.revenue} />
        </Table.Td>
      </Table.Tr>
    ))
  ) : (
    <Table.Tr>
      <Table.Td colSpan={7} ta="center">
        <Text c="text" p="xl" mih="16em">
          Data yang anda cari tidak ditemukan!
        </Text>
      </Table.Td>
    </Table.Tr>
  );

  return (
    <Stack>
      <SectionHeader
        title="Top Selling Stores"
        description="Berikut adalah distributor di online shop Hotto yang diurutkan dari penjualan terbanyak."
      />

      <Table.ScrollContainer minWidth={768}>
        <Table
          withColumnBorders
          verticalSpacing="md"
          horizontalSpacing="md"
          style={tableStyle}
        >
          <Table.Thead>
            <Table.Tr c="text">
              {TABLE_HEADERS.map((header, index) => (
                <Table.Th key={index} style={headerStyle}>
                  {header}
                </Table.Th>
              ))}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{loading ? loadingRows : tableRows}</Table.Tbody>
        </Table>
      </Table.ScrollContainer>

      {!loading && totalItems !== undefined && (
        <Flex justify="flex-end" w="100%">
          <Pagination
            total={Math.ceil(totalItems / ITEMS_PER_PAGE)}
            page={page}
            onChange={setPage}
            disabled={loading}
          />
        </Flex>
      )}
    </Stack>
  );
};

export default SalesStatisticsTable;
