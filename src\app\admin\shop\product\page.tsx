"use client";

import PageLayout from "@/components/common/PageLayout";
import ProductStatusFilter from "@/components/section/OnlineShop/Product/Filter/Status";
import ProductTable from "@/components/section/OnlineShop/Product/Table";
import { withAuth } from "@/utils/withAuth";
import { Stack } from "@mantine/core";

const ProductPage = () => {
  return (
    <PageLayout title="Produk">
      <Stack>
        <ProductStatusFilter />
        <ProductTable />
      </Stack>
    </PageLayout>
  );
};

export default withAuth(ProductPage);
