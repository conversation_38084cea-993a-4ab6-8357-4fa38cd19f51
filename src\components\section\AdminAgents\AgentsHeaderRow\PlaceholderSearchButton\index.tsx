import { But<PERSON>, <PERSON><PERSON>r, Flex } from "@mantine/core";
import styles from "./style.module.css";
import Icon from "@/components/common/Icon";
import { useRouter } from "next/navigation";

const PlaceholderSearchButton = () => {
  const router = useRouter();

  const icon = (
    <Flex align={"center"} justify={"center"} gap={"xs"}>
      <Icon icon="search" size={14} />
      <Divider orientation="vertical" />
    </Flex>
  );

  return (
    <Button
      onClick={() => {
        router.push("/admin/agent/browse");
      }}
      className={styles.searchButton}
      variant="outline"
      justify="start"
      leftSection={icon}
      miw={{ base: "13em", md: "18em" }}
    >
      Cari
    </Button>
  );
};

export default PlaceholderSearchButton;
