import { formatTimeStamp } from "@/utils/common-functions";
import { HistoriesData } from "@/utils/types/distributor-agent";
import { Flex, Text, Box, NumberFormatter } from "@mantine/core";
import React from "react";

export interface OrderHistoryProps {
  history: HistoriesData;
}

const OrderHistory = ({ history }: OrderHistoryProps) => {
  return (
    <Flex justify="space-between" align="center">
      <Box>
        <Text fw={500}>
          {history.qty} {history.type}
        </Text>
        <Text c="text" fw={500}>
          {formatTimeStamp(history.date)}
        </Text>
      </Box>
      <Text fw={500}>
        +
        <NumberFormatter
          prefix="Rp"
          value={history.nominal}
          thousandSeparator="."
          decimalSeparator=","
        />
      </Text>
    </Flex>
  );
};

export default OrderHistory;
