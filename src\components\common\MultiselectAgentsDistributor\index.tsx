import { AppDispatch, RootState } from "@/store";
import { fetchAgentsBySearchQuery } from "@/store/slices/adminAgentListSlice";
import { MultiSelect, MultiSelectProps } from "@mantine/core";
import { useDebouncedState } from "@mantine/hooks";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { MultiSelectAgentsDistributorWrapper } from "./style";
import AgentDistribtuorSelectOption from "./AgentDistributorSelectOption";
import { BroadcastRecipient } from "@/utils/types/broadcast";

interface MultiselectAgentsDistributorProps {
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  previousValueDetails?: BroadcastRecipient[]; // for names of the uocs
}

const MultiselectAgentsDistributor = ({
  value,
  onChange,
  placeholder = "Surat ini ditujukan untuk",
  disabled = false,
  previousValueDetails = [],
}: MultiselectAgentsDistributorProps) => {
  const [searchValue, setSearchValue] = useDebouncedState<string>("", 300);
  const [dropDownDatas, setDropDownDatas] = useState<
    {
      value: string; // uoc
      label: string; // label
      email?: string;
      ownerPhone?: string;
      adminPhone?: string;
    }[]
  >([]);

  const dispatch: AppDispatch = useDispatch();
  const { searchResult } = useSelector(
    (state: RootState) => state.adminAgentList
  );

  // this is to make sure the pills (from response) also have the labels (name, not only uoc)
  useEffect(() => {
    if (previousValueDetails.length > 0) {
      setDropDownDatas((prevDropDownDatas) => {
        const updatedDatas = [
          ...prevDropDownDatas,
          ...previousValueDetails.map((prevValDetail) => ({
            value: prevValDetail.uoc,
            label: prevValDetail.name,
          })),
        ];
        const uniqueDatas = updatedDatas.filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.value === item.value)
        );
        return uniqueDatas;
      });
    }
  }, [previousValueDetails]);

  useEffect(() => {
    if (searchValue) {
      dispatch(fetchAgentsBySearchQuery(searchValue));
    }
  }, [searchValue, dispatch]);

  useEffect(() => {
    setDropDownDatas((prevDropDownDatas) => {
      const newItems = searchResult.map((item) => ({
        value: item.uoc,
        label: item.name,
        email: item.email,
        ownerPhone: item.ownerPhone,
        adminPhone: item.adminPhone,
      }));
      // Combine previous data with new items
      const combinedItems = [...prevDropDownDatas, ...newItems];
      // Remove duplicates by 'value' (item.uoc)
      const uniqueItems = combinedItems.filter(
        (item, index, self) =>
          self.findIndex((t) => t.value === item.value) === index
      );
      return uniqueItems;
    });
  }, [searchResult]);

  const renderSelectOption: MultiSelectProps["renderOption"] = ({
    option,
    checked,
  }) => (
    <AgentDistribtuorSelectOption
      datas={searchResult}
      checked={checked}
      option={option}
    />
  );

  return (
    <MultiSelectAgentsDistributorWrapper>
      <MultiSelect
        searchable
        onSearchChange={setSearchValue}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
        data={dropDownDatas}
        nothingFoundMessage="Agen/distributor tidak ditemukan"
        limit={10}
        maxDropdownHeight={140}
        rightSection={<></>}
        comboboxProps={{
          position: "bottom",
          middlewares: { flip: false, shift: false },
          offset: 0,
        }}
        renderOption={renderSelectOption}
      />
    </MultiSelectAgentsDistributorWrapper>
  );
};

export default MultiselectAgentsDistributor;
