import ApplicationModalData from "@/components/common/RequestModal/RequestDetail/ApplicationRequest/ApplicationData";
import ResponsiveModal from "@/components/common/ResponsiveModal";
import { AdminRequestProvider } from "@/context/AdminRequestContext";
import { ApplicantData } from "@/utils/types/distributor-agent";
import { Stack, Title } from "@mantine/core";

interface ApplicantDetailModalProps {
  opened: boolean;
  onClose: () => void;
  applicant: ApplicantData;
}

const ApplicantDetailModal = ({
  opened,
  onClose,
  applicant,
}: ApplicantDetailModalProps) => {
  const role = applicant?.npwp ? "Distributor" : "Agent";

  return (
    <ResponsiveModal opened={opened} onClose={onClose}>
      <Stack align="center" pos="relative">
        <Title order={3}>{`Detail ${role} Waitlist`}</Title>
        {applicant && (
          <AdminRequestProvider>
            <ApplicationModalData
              type={role}
              requestStep={1}
              newAgent={{
                ...applicant,
                phone: applicant.phoneNumber,
                adminPhone: applicant.adminPhoneNumber,
                address:
                  applicant.kelurahan +
                  ", " +
                  applicant.district +
                  ", " +
                  applicant.city +
                  ", " +
                  applicant.province,
                warehouseAddress: applicant.warehouseAddress,
              }}
            />
          </AdminRequestProvider>
        )}
      </Stack>
    </ResponsiveModal>
  );
};

export default ApplicantDetailModal;
