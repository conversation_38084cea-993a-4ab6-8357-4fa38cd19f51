import SectionHeader from "@/components/common/SectionHeader";
import { Flex, Stack } from "@mantine/core";
import InfoCard from "./InfoCard";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

const TodayInfo = () => {
  const isAdmin = window.location.pathname.includes("/admin/");
  const baseUrl = isAdmin ? "/admin/shop/order" : "/shop/order";

  const {
    todayStats,
    loading: { todayStats: loading },
  } = useSelector((state: RootState) => state.dashboard);

  const infoItems = [
    {
      label: "Pesanan Baru",
      value: todayStats?.newOrdersCount ?? 0,
      href: `${baseUrl}?status=waiting_confirmation`,
    },
    {
      label: "Pesanan <PERSON>ko<PERSON>lain",
      value: todayStats?.complaintsCount ?? 0,
      href: `${baseUrl}?status=complaint`,
    },
  ];

  return (
    <Stack w="100%">
      <SectionHeader
        title="Penting Hari Ini"
        description="Segera selesaikan task ini untuk menjaga kepuasan pembeli!"
        loading={loading}
      />
      <Flex w="100%" gap="md">
        {infoItems.map((item) => (
          <InfoCard key={item.label} {...item} loading={loading} />
        ))}
      </Flex>
    </Stack>
  );
};

export default TodayInfo;
