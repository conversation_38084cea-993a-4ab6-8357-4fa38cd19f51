import InputGroup from "@/components/common/InputGroup";
import { FormInputRow } from "./style";
import Input from "@/components/common/Input";
import { Box, Flex, Text } from "@mantine/core";
import {
  AgentDistributorDetailsForceUpdateData,
  AgentDistributorErrors,
  UserAgentDetail,
} from "@/utils/types/distributor-agent";
import { capitalize, validateAgentAttribute } from "@/utils/common-functions";
import { useEffect, useState } from "react";
import UploadedFileRow from "@/components/common/UploadedFileRow";

interface ForceEditFormProps {
  changes: AgentDistributorDetailsForceUpdateData;
  agentDetails: UserAgentDetail;
  handleChanges: (data: string | undefined, key: string) => void;
  setChangesValidity: (isValid: boolean) => void;
}

const ForceEditForm = ({
  changes,
  agentDetails,
  handleChanges,
  setChangesValidity,
}: ForceEditFormProps) => {
  const [errors, setErrors] = useState<AgentDistributorErrors>({});
  const handleUpdateErrors = (field: string, message: string) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      [field]: message,
    }));
  };

  useEffect(() => {
    const isValid = Object.values(errors).every((error) => error === "");
    setChangesValidity(isValid);
  }, [errors, setChangesValidity]);

  const role = agentDetails.roleName || agentDetails.role?.name;

  return (
    <Flex p={"xs"} direction={"column"} gap={"md"} w={"100%"}>
      <InputGroup label={`Nama ${capitalize(role)}`} mandatory>
        <Input
          placeholder={agentDetails.name}
          value={changes.name ?? agentDetails.name ?? ""}
          disabled
        />
        {errors.name && (
          <Text c="error" size="sm">
            {errors.name}
          </Text>
        )}
      </InputGroup>

      {role === "distributor" && (
        <InputGroup label={"NPWP"} mandatory>
          <Input
            placeholder={agentDetails.npwp}
            value={agentDetails.npwp ?? ""}
            disabled
          />
        </InputGroup>
      )}

      <InputGroup label={"NIK"} mandatory>
        <Input
          placeholder={agentDetails.nik ?? ""}
          value={agentDetails.nik ?? ""}
          disabled
        />
        {errors.nik && (
          <Text c="error" size="sm">
            {errors.nik}
          </Text>
        )}
      </InputGroup>

      <InputGroup label={"Alamat KTP"} mandatory>
        <Input
          icon="info/location"
          placeholder={agentDetails.address ?? ""}
          value={agentDetails.address ?? ""}
          disabled
        />
        {errors.address && (
          <Text c="error" size="sm">
            {errors.address}
          </Text>
        )}
      </InputGroup>

      <InputGroup label={"Alamat Warehouse"} mandatory>
        <Input
          disabled
          icon="info/location"
          placeholder={agentDetails.warehouseAddress ?? ""}
          value={agentDetails.warehouseAddress ?? ""}
        />
        {errors.warehouseAddress && (
          <Text c="error" size="sm">
            {errors.warehouseAddress}
          </Text>
        )}
      </InputGroup>

      <InputGroup label={"Email"} mandatory>
        <Input
          icon="info/email"
          placeholder={agentDetails.email ?? "Email"}
          value={changes.email ?? agentDetails.email ?? ""}
          onChange={(e) => {
            handleChanges(e.target.value, "email");
            validateAgentAttribute("email", e.target.value, handleUpdateErrors);
          }}
        />
        {errors.email && (
          <Text c="error" size="sm">
            {errors.email}
          </Text>
        )}
      </InputGroup>

      <FormInputRow>
        <InputGroup label={"Telepon Pribadi"} mandatory>
          <Input
            icon="info/phone"
            placeholder={agentDetails.ownerPhone ?? ""}
            value={
              changes.phoneNumber ??
              agentDetails.phoneNumber ??
              agentDetails.ownerPhone ??
              ""
            }
            onChange={(e) => {
              handleChanges(e.target.value, "phoneNumber");
              validateAgentAttribute(
                "phoneNumber",
                e.target.value,
                handleUpdateErrors
              );
            }}
          />
          {errors.phoneNumber && (
            <Text c="error" size="sm">
              {errors.phoneNumber}
            </Text>
          )}
        </InputGroup>

        <InputGroup label={"Telepon Admin"} mandatory>
          <Input
            icon="info/phone"
            placeholder={agentDetails.adminPhone ?? ""}
            value={
              changes.adminPhoneNumber ??
              agentDetails.adminPhone ??
              agentDetails.adminPhoneNumber ??
              ""
            }
            onChange={(e) => {
              handleChanges(e.target.value, "adminPhoneNumber");
              validateAgentAttribute(
                "adminPhoneNumber",
                e.target.value,
                handleUpdateErrors
              );
            }}
          />
          {errors.adminPhoneNumber && (
            <Text c="error" size="sm">
              {errors.adminPhoneNumber}
            </Text>
          )}
        </InputGroup>
      </FormInputRow>

      <FormInputRow>
        <InputGroup label={"Link Tokopedia"}>
          <Input
            icon="social/tokopedia"
            placeholder={agentDetails.shop?.tokopedia ?? "Link Tokopedia"}
            value={
              changes.shops?.tokopedia ??
              agentDetails.shops?.tokopedia ??
              agentDetails.shop?.tokopedia ??
              ""
            }
            onChange={(e) => {
              handleChanges(e.target.value, "shops.tokopedia");
              validateAgentAttribute(
                "tokopedia",
                e.target.value,
                handleUpdateErrors
              );
            }}
          />
          {errors.tokopedia && (
            <Text c="error" size="sm">
              {errors.tokopedia}
            </Text>
          )}
        </InputGroup>

        <InputGroup label={"Link Shopee"}>
          <Input
            icon="social/shopee"
            placeholder={agentDetails.shop?.shopee ?? "Link Shopee"}
            value={
              changes.shops?.shopee ??
              agentDetails.shops?.shopee ??
              agentDetails.shop?.shopee ??
              ""
            }
            onChange={(e) => {
              handleChanges(e.target.value, "shops.shopee");
              validateAgentAttribute(
                "shopee",
                e.target.value,
                handleUpdateErrors
              );
            }}
          />
          {errors.shopee && (
            <Text c="error" size="sm">
              {errors.shopee}
            </Text>
          )}
        </InputGroup>
      </FormInputRow>

      {agentDetails.sk && (
        <Box w="50%" pb="md">
          <InputGroup
            label={`Uploaded ${
              role === "distributor" ? "SK Distributor" : "Bukti Pembayaran"
            }`}
          >
            <UploadedFileRow fileUrl={agentDetails.sk} />
          </InputGroup>
        </Box>
      )}
    </Flex>
  );
};

export default ForceEditForm;
