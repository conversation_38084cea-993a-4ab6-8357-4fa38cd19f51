import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { AppDispatch } from "..";
import api from "@/lib/axios";
import { toast } from "sonner";
import { AxiosError, AxiosResponse } from "axios";
import { UPLOAD_FILE } from "@/utils/api-routes";

export interface FileState {
  uploadedFileUrl: string | null;
  uploadedFileKey: string | null;
  loading: boolean;
  status: number;
  message: string;
}

const initialState: FileState = {
  uploadedFileUrl: null,
  uploadedFileKey: null,
  loading: false,
  status: 404,
  message: "",
};

const fileSlice = createSlice({
  name: "file",
  initialState,
  reducers: {
    setUploadedFileUrl(
      state,
      action: PayloadAction<FileState["uploadedFileUrl"]>
    ) {
      state.uploadedFileUrl = action.payload;
    },
    setUploadedFileKey(
      state,
      action: PayloadAction<FileState["uploadedFileKey"]>
    ) {
      state.uploadedFileKey = action.payload;
    },
    nullifyFileUrl(state) {
      state.uploadedFileUrl = null;
      state.uploadedFileKey = null;
    },
    setStatus(state, action: PayloadAction<FileState["status"]>) {
      state.status = action.payload;
    },
    setMessage(state, action: PayloadAction<FileState["message"]>) {
      state.message = action.payload;
    },
    setLoading(state, action: PayloadAction<FileState["loading"]>) {
      state.loading = action.payload;
    },
  },
});

export const postFile =
  (file: FormData, prefix: string) => async (dispatch: AppDispatch) => {
    const errMsg = "Failed to upload file";
    try {
      dispatch(fileSlice.actions.setUploadedFileKey(prefix));
      dispatch(fileSlice.actions.setLoading(true));
      file.append("prefix", prefix);
      const res: AxiosResponse<{ data: { url: string } }> = await api.post(
        UPLOAD_FILE,
        file
      );
      const url = res.data.data.url;
      if (!url) throw new Error(errMsg);
      dispatch(fileSlice.actions.setStatus(res.status));
      dispatch(fileSlice.actions.setUploadedFileUrl(url));
      toast.success("File uploaded successfully");
      return Promise.resolve(url);
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        dispatch(fileSlice.actions.setStatus(error.response?.status || 500));
        dispatch(fileSlice.actions.setMessage(error.message));
        toast.error(error.message);
      } else {
        dispatch(fileSlice.actions.setStatus(500));
        dispatch(fileSlice.actions.setMessage(errMsg));
        toast.error(errMsg);
      }
      return Promise.reject(errMsg);
    } finally {
      dispatch(fileSlice.actions.setLoading(false));
    }
  };

export const resetFile = () => async (dispatch: AppDispatch) => {
  dispatch(fileSlice.actions.nullifyFileUrl());
};

export default fileSlice.reducer;
