import { Flex, FlexProps } from "@mantine/core";
import classes from "./style.module.css";

/**
 * A wrapper for multiple buttons, usually used for modals/drawers
 * @param children list of buttons to be rendered
 * @returns a flex container with buttons
 */
const ButtonGroup = ({ children, ...props }: FlexProps) => {
  return (
    <Flex
      gap="sm"
      align="center"
      direction={{ base: "column-reverse", sm: "row" }}
      classNames={classes}
      {...props}
    >
      {children}
    </Flex>
  );
};

export default ButtonGroup;
