import { Divider, Flex } from "@mantine/core";
import RequestCardHeader from "../../RequestCardHeader";
import RequestCardChange from "../../RequestCardChange";
import { RequestCardData } from "@/utils/types/request";
import { useMediaQuery } from "@mantine/hooks";
import React from "react";

const DistributorDataChangeRequestContent = ({
  ...request
}: RequestCardData) => {
  const isMobile = useMediaQuery("(max-width: 768px)");

  return (
    <Flex direction={"column"}>
      <RequestCardHeader
        leftText={`Request Ubah Data`}
        rightText="Dikirim"
        updatedAt={request.updatedAt}
        rightItalic={false}
      />
      <Divider />
      {request.metadatas.map((metadata, index) => {
        return (
          <React.Fragment key={metadata.field}>
            {index > 0 && isMobile && <Divider variant="dashed" />}
            <RequestCardChange
              field={metadata.field}
              oldValue={metadata.oldValue}
              newValue={metadata.newValue}
              reason={request.reason}
              requestId={request.id}
              status={metadata.status}
              isAdmin={false}
            />
          </React.Fragment>
        );
      })}
    </Flex>
  );
};

export default DistributorDataChangeRequestContent;
