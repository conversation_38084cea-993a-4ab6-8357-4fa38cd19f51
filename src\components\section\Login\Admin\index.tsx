import BrandLogo from "@/components/common/BrandLogo";
import {
  Center,
  Paper,
  PasswordInput,
  TextInput,
  Text,
  Title,
  Stack,
  Box,
  Button,
} from "@mantine/core";
import React, { useEffect } from "react";
import * as yup from "yup";
import { passwordValidationLogin } from "@/utils/common-functions";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { login } from "@/store/slices/authSlice";

const schema = yup.object().shape({
  email: yup.string().email("Email tidak valid").required("Email harus diisi"),
  password: passwordValidationLogin,
});

const AdminLogin = () => {
  const dispatch: AppDispatch = useDispatch();
  const { isLoading, role, accessToken } = useSelector(
    (state: RootState) => state.auth
  );

  const form = useForm({
    mode: "onChange",
    resolver: yupResolver(schema),
    defaultValues: { email: "", password: "" },
  });

  const handleSubmit = (data: yup.InferType<typeof schema>) => {
    if (!isLoading) {
      dispatch(
        login({
          loginData: { email: data.email, password: data.password },
        })
      );
    }
  };

  useEffect(() => {
    if (accessToken) {
      const currentUrl = new URL(window.location.href);
      const searchParams = currentUrl.searchParams;
      const queryString = searchParams.toString();
      window.location.href = queryString ? `/?${queryString}` : "/";
    }
  }, [accessToken, role]);

  return (
    <Center bg="bg" h="100dvh">
      <Paper
        p={{ base: "lg", sm: "3rem" }}
        w={{ base: "100%", sm: 600 }}
        mx="lg"
      >
        <Stack
          gap="xl"
          component="form"
          onSubmit={form.handleSubmit(handleSubmit)}
        >
          <Box ta="center">
            <BrandLogo mb="lg" />
            <Title fz={{ base: "h3", sm: "h1" }}>Selamat Datang Kembali!</Title>
            <Text c="text-light">Login ke akunmu untuk kembali</Text>
          </Box>
          <TextInput
            label="Email"
            placeholder="<EMAIL>"
            error={form.formState.errors?.email?.message}
            {...form.register("email")}
          />
          <PasswordInput
            label="Password"
            placeholder="contoh123"
            error={form.formState.errors?.password?.message}
            {...form.register("password")}
          />
          <Center>
            <Button
              type="submit"
              loading={isLoading}
              disabled={!form.formState.isValid}
            >
              Login
            </Button>
          </Center>
        </Stack>
      </Paper>
    </Center>
  );
};

export default AdminLogin;
