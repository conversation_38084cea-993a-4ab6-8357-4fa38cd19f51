import { Flex, Text } from "@mantine/core";
import Image from "next/image";

interface NotFoundPlaceholderProps {
  message?: string;
}

const NotFoundPlaceholder = ({
  message = "Kami tidak menemukan halaman yang anda tujui",
}: NotFoundPlaceholderProps) => {
  return (
    <Flex direction={"column"} align={"center"} justify={"center"} gap={"lg"}>
      <Image
        width={160}
        height={160}
        alt="not-found"
        src="/images/not-found.svg"
      />
      <Text size={"lg"} fw={700} ta={"center"}>
        {message}
      </Text>
    </Flex>
  );
};

export default NotFoundPlaceholder;
