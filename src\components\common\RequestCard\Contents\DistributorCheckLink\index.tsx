import { formatTimeStamp } from "@/utils/common-functions";
import { RequestCardData, RequestType } from "@/utils/types/request";
import { Box, Divider, Flex, Text } from "@mantine/core";
import InvalidMarketplaceLinks from "./InvalidMarketplaceLinks";
import { useDisclosure } from "@mantine/hooks";
import RequestModal from "@/components/common/RequestModal";
import { useState } from "react";
import RequestCardHeader from "../../RequestCardHeader";

const DistributorCheckLinkRequestContent = ({
  ...request
}: RequestCardData) => {
  const [opened, { open, close }] = useDisclosure(false);
  const [selectedShopType, setSelectedShopType] = useState<string | null>(null);

  return (
    <Flex direction={"column"}>
      {selectedShopType && (
        <RequestModal
          opened={opened}
          onClose={close}
          requestId={request.id}
          type={RequestType.DistributorCheckMarketplaceLinks}
          status="update"
          field={selectedShopType}
        />
      )}
      <RequestCardHeader
        leftText="Link Marketplacemu invalid. Silahkan segera update!"
        createdAt={request.createdAt}
      />
      <Divider />
      <Box p={"lg"}>
        <InvalidMarketplaceLinks
          invalidLinks={request.metadatas}
          openModal={(shopType: string) => {
            setSelectedShopType(shopType);
            open();
          }}
          reason={request.reason || "-"}
        />
      </Box>
    </Flex>
  );
};

export default DistributorCheckLinkRequestContent;
