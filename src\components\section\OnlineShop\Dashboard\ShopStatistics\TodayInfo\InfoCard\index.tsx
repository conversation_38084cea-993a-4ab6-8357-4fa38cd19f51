import { Anchor, Flex, Paper, Skeleton, Stack, Text } from "@mantine/core";

const InfoCard = ({
  label,
  value,
  href,
  loading,
}: {
  label: string;
  value: string | number;
  href: string;
  loading?: boolean;
}) => (
  <Paper p="lg" bg="bg" w="100%">
    <Stack gap={0}>
      {loading ? (
        <Skeleton height={16} width="80px" />
      ) : (
        <Text fw={500} size="sm" c="text-light">
          {label}
        </Text>
      )}
      <Flex justify="space-between" gap="sm" align="center">
        {loading ? (
          <Skeleton height={24} width="60%" mt="xs" />
        ) : (
          <Text fw={600} size="lg">
            {value}
          </Text>
        )}
        {!loading && (
          <Anchor href={href} size="sm">
            Lihat
          </Anchor>
        )}
      </Flex>
    </Stack>
  </Paper>
);

export default InfoCard;
