import SimpleInfo from "@/components/section/AdminAgentDistributor/SimpleInfo";
import { Stack, Text, Group } from "@mantine/core";
import CheckboxInput from "../CheckboxInput";
import {
  AdminRequestLinksByIslandData,
  AdminSubmitInvalidLinksReasonsData,
} from "@/utils/types/request";
import { AppDispatch } from "@/store";
import { useDispatch } from "react-redux";
import { setInvalidLinksReasons } from "@/store/slices/adminMarketplaceLinksSlice";

const Links: React.FC<AdminRequestLinksByIslandData> = ({
  uoc,
  userName,
  userRole,
  links,
}) => {
  const dispatch: AppDispatch = useDispatch();

  const handleInputChange = (
    data: AdminSubmitInvalidLinksReasonsData,
    remove?: boolean
  ) => {
    dispatch(setInvalidLinksReasons(data, remove));
  };

  const notEmpty = links && links?.length !== 0;

  return (
    <Stack gap="sm">
      <Text>
        {userRole} ‘{userName}’
      </Text>
      {notEmpty &&
        links.map((link) => {
          return (
            <Group justify="space-between" key={link.id} grow>
              <SimpleInfo icon={`social/${link.type}`} description={link.url} />
              <Group justify="end">
                <CheckboxInput
                  onChange={handleInputChange}
                  linkToCheck={link}
                  uoc={uoc}
                />
              </Group>
            </Group>
          );
        })}
    </Stack>
  );
};

export default Links;
