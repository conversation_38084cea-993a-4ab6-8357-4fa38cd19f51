import {
  Box,
  Group,
  Input,
  NumberFormatter,
  Paper,
  SimpleGrid,
  Stack,
  Text,
} from "@mantine/core";
import { FormStepperProps } from "../../../Forms";
import UploadedFileRow from "@/components/common/UploadedFileRow";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { setPaymentProof } from "@/store/slices/agentRegisterSlice";
import { useId } from "@mantine/hooks";
import { useEffect } from "react";
import { isEmpty } from "@/utils/common-functions";
import FileUploadInput from "@/components/common/FileUploadInput";

const UploadPaymentForm: React.FC<FormStepperProps> = ({ onFormValid }) => {
  const dispatch: AppDispatch = useDispatch();
  const { paymentProof } = useSelector(
    (state: RootState) => state.agentRegister
  );
  const id = useId().replace("mantine-", "");

  useEffect(() => {
    onFormValid(!isEmpty(paymentProof));
  }, [paymentProof]);

  return (
    <Stack gap={50}>
      <Paper bg="bg" p="xl" withBorder>
        <Stack>
          <Text>
            Sebelum sepenuhnya menjadi Agent Hotto, Anda perlu untuk membeli
            produk Hotto dari distributor sebesar Rp4.900.000.{" "}
          </Text>
          <Group justify="space-between">
            <Text fw={600}>Total Pembayaran</Text>
            <Text fw={600} fz="h4">
              <NumberFormatter
                value={process.env.NEXT_PUBLIC_HOTTO_PRODUCT_PRICE || 0}
              />
            </Text>
          </Group>
          <Box>
            <Text>Nomor Rekening</Text>
            <Text fw={600} fz="h3">
              {process.env.NEXT_PUBLIC_HOTTO_ACCOUNT_NUMBER || 0}
            </Text>
          </Box>
          <Text>Pastikan untuk membayar dengan angka yang tepat.</Text>
        </Stack>
      </Paper>
      <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="xl">
        <FileUploadInput
          required
          label="Upload Bukti Pembayaran"
          fileType="images"
          prefix={`distributor-bp/${id}`}
          onChange={(url) => {
            dispatch(setPaymentProof(url));
          }}
        />
        <Input.Wrapper label="Uploaded Bukti Pembayaran">
          {isEmpty(paymentProof) ? (
            <Text c="text">Belum ada file diupload!</Text>
          ) : (
            <UploadedFileRow
              fileUrl={paymentProof}
              onDelete={() => {
                if (confirm("Apakah anda yakin ingin menghapus file ini?")) {
                  dispatch(setPaymentProof(""));
                }
              }}
            />
          )}
        </Input.Wrapper>
      </SimpleGrid>
    </Stack>
  );
};

export default UploadPaymentForm;
