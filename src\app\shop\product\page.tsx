"use client";

import PageLayout from "@/components/common/PageLayout";
import ProductStatusFilter from "@/components/section/OnlineShop/Product/Filter/Status";
import ProductTable from "@/components/section/OnlineShop/Product/Table";
import { withAuth } from "@/utils/withAuth";

const ProductPage = () => {
  return (
    <PageLayout title="Produk">
      <ProductStatusFilter />
      <ProductTable />
    </PageLayout>
  );
};

export default withAuth(ProductPage);
