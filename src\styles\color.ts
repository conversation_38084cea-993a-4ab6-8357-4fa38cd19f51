/**
 * COLORS object defines the color palette used throughout the project.
 * Each key corresponds to a specific role in the design system.
 */
export const COLORS = {
  /** Primary text or main elements color (#464646) */
  primary: "#464646",
  /** Secondary text or muted elements color (#989898) */
  secondary: "#989898",
  /** Tertiary text or subdued elements color (#292929) */
  tertiary: "#292929",

  /** Accent color for highlights and interactive elements (#5249a0) */
  accent: "#5b539c",
  /** Secondary accent color for highlights and interactive elements (#423e6a) */
  accentHover: "#423e6a",
  /** Secondary accent color for background elements (#F4F5FA) */
  accentBg: "#F4F5FA",
  /** Disabled accent color for disabled elements (#b3b7dd) */
  accentDisabled: "#B3B7DD",

  /** Accent color for mame  (#728049) */
  mame: "#808e54",
  /** Secondary accent color for mame hover (#4e5734) */
  mameHover: "#4e5734",
  /** Secondary accent color for mame background (#d4dabc) */
  mameBg: "#d4dabc",

  /** Color for disabled or inactive elements (#bdbdbd) */
  disabled: "#BDBDBD",
  /** Secondary color for disabled or inactive elements (#DCDCDC) */
  disabled2: "#DCDCDC",

  /** Main background color for light areas (#fff) */
  background: "#fff",
  /** Secondary background color, slightly muted (#f5f5f5) */
  background2: "#f5f5f5",
  /** Tertiary background color, even more muted (#eaeaea) */
  background3: "#eaeaea",

  /** Success state color for positive feedback (#49a45d) */
  success: "#49a45d",
  /** Background for success states (#d8f8de) */
  successBg: "#d8f8de",
  /** Error state color for negative feedback (#d24b4b) */
  error: "#d24b4b",
  /** Background for error states (#F6DBDB) */
  errorBg: "#F6DBDB",
};
