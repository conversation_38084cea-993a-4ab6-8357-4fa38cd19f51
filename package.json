{"name": "hotto-adms-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@mantine/charts": "^7.17.7", "@mantine/colors-generator": "^7.17.7", "@mantine/core": "^7.17.7", "@mantine/dropzone": "^7.17.7", "@mantine/hooks": "^7.17.7", "@reduxjs/toolkit": "^2.6.1", "axios": "^1.8.4", "chroma-js": "^3.1.2", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "next": "^15.2.4", "nuqs": "^2.4.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-infinite-scroll-component": "^6.1.0", "react-pdf": "^9.2.1", "react-redux": "^9.2.0", "recharts": "2", "sonner": "^1.7.4", "styled-components": "^6.1.17", "yup": "^1.6.1"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^20.17.30", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "@types/styled-components": "^5.1.34", "eslint": "^9.24.0", "eslint-config-next": "^15.2.4", "postcss": "^8.5.3", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "typescript": "^5.8.3"}, "resolutions": {"react-is": "^19.0.0"}}