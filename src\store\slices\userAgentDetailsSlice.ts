import api from "@/lib/axios";
import { GET_USER_DETAILS } from "@/utils/api-routes";
import { createSlice } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { AppDispatch } from "..";
import {
  AgentDistributorDetailsData,
  AgentDistributorDetailsResponse,
} from "@/utils/types/distributor-agent";

interface UserAgentDetailsState {
  loading: boolean;
  error: string | undefined;
  data: AgentDistributorDetailsData | undefined;
}

const initialState: UserAgentDetailsState = {
  loading: false,
  error: undefined,
  data: undefined,
};

const userAgentDetailsSlice = createSlice({
  name: "userAgentDetails",
  initialState,
  reducers: {
    setError(state, action) {
      state.error = action.payload;
      toast.error(action.payload);
    },
    setLoading(state, action) {
      state.loading = action.payload;
    },
    setData(state, action) {
      state.data = action.payload;
    },
  },
});

export const fetchUserAgentDetails = () => async (dispatch: AppDispatch) => {
  try {
    dispatch(userAgentDetailsSlice.actions.setLoading(true));
    const res: { data: AgentDistributorDetailsResponse } = await api.get(
      GET_USER_DETAILS
    );
    if (res.data.status_code === 200 && res.data.data) {
      dispatch(userAgentDetailsSlice.actions.setData(res.data.data));
    } else {
      dispatch(userAgentDetailsSlice.actions.setError(res.data.message));
    }
  } catch {
    dispatch(
      userAgentDetailsSlice.actions.setError("Failed get user details.")
    );
  } finally {
    dispatch(userAgentDetailsSlice.actions.setLoading(false));
  }
};

export default userAgentDetailsSlice.reducer;
