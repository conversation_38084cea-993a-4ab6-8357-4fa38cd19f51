import {
  Flex,
  Stack,
  Table,
  Skeleton,
  Text,
  NumberFormatter,
  MantineStyleProp,
} from "@mantine/core";
import Pagination from "@/components/common/Pagination";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { ITEMS_PER_PAGE } from "@/utils/constants";
import { isEmpty } from "@/utils/common-functions";
import SectionHeader from "@/components/common/SectionHeader";
import useOverviewFilters from "@/hooks/useOverviewFilters";
import { fetchOrderHistory } from "@/store/slices/onlineShopSlices/overviewSlice";
import ActivationStatus from "@/components/common/ActivationStatus";
import {
  getOrderStatuslabel,
  OrderStatusType,
} from "@/utils/types/online-shop";

const TABLE_HEADERS = [
  "Order ID",
  "Status",
  "No. Telp",
  "Email",
  "Total Pesanan",
];

const OrderHistoryTable = ({ slug }: { slug?: string }) => {
  const dispatch: AppDispatch = useDispatch();
  const {
    orderHistory: { orderHistories, totalItems },
    loading: { orderHistory: loading },
  } = useSelector((state: RootState) => state.overview);
  const { page, setPage } = useOverviewFilters();

  useEffect(() => {
    if (!loading && slug) dispatch(fetchOrderHistory(slug, page));
  }, [page]);

  const tableStyle: MantineStyleProp = {
    borderTop: "1px solid var(--mantine-color-bg-gray-filled)",
    borderBottom: "1px solid var(--mantine-color-bg-gray-filled)",
  };

  const headerStyle: MantineStyleProp = {
    borderInlineEnd: "none",
  };

  const loadingRows = Array(5)
    .fill(null)
    .map((_, index) => (
      <Table.Tr key={index}>
        {Array(5)
          .fill(null)
          .map((_, cellIndex) => (
            <Table.Td key={cellIndex}>
              <Skeleton height={20} />
            </Table.Td>
          ))}
      </Table.Tr>
    ));

  const tableRows = !isEmpty(orderHistories) ? (
    orderHistories.map((data) => (
      <Table.Tr key={data.orderId}>
        <Table.Td>{data.orderId}</Table.Td>
        <Table.Td>
          <ActivationStatus
            customText={getOrderStatuslabel(data.status as OrderStatusType)}
            status="active"
          />
        </Table.Td>
        <Table.Td>{data.phone}</Table.Td>
        <Table.Td>{data.email}</Table.Td>
        <Table.Td>
          <NumberFormatter value={data.totalPrice} />
        </Table.Td>
      </Table.Tr>
    ))
  ) : (
    <Table.Tr>
      <Table.Td colSpan={7} ta="center">
        <Text c="text" p="xl" mih="16em">
          Data yang anda cari tidak ditemukan!
        </Text>
      </Table.Td>
    </Table.Tr>
  );

  return (
    <Stack>
      <SectionHeader
        title="Order History"
        description="Berikut adalah list order history."
      />

      <Table.ScrollContainer minWidth={768}>
        <Table
          withColumnBorders
          verticalSpacing="md"
          horizontalSpacing="md"
          style={tableStyle}
        >
          <Table.Thead>
            <Table.Tr c="text">
              {TABLE_HEADERS.map((header, index) => (
                <Table.Th key={index} style={headerStyle}>
                  {header}
                </Table.Th>
              ))}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{loading ? loadingRows : tableRows}</Table.Tbody>
        </Table>
      </Table.ScrollContainer>

      {!loading && totalItems !== undefined && !isEmpty(orderHistories) && (
        <Flex justify="flex-end" w="100%">
          <Pagination
            total={Math.ceil(totalItems / ITEMS_PER_PAGE)}
            page={page}
            onChange={setPage}
            disabled={loading}
          />
        </Flex>
      )}
    </Stack>
  );
};

export default OrderHistoryTable;
