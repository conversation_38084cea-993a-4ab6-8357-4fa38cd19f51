export interface AuthState {
  accessToken: string | null;
  refreshToken: string | null;
  role: string | null;
  isLoading: boolean;
  error: string | null;
}

export interface LoginAdminPayload {
  email: string;
  password: string;
}

export interface LoginUserPayload {
  phone: string;
  password: string;
}

export interface AuthResponse {
  status_code: number;
  message: string;
  data: AuthResponseData;
}

export interface AuthResponseData {
  accessToken: string;
  refreshToken: string;
  role: string;
}

export interface UserProfileResponse {
  status_code: number;
  message: string;
  data: UserProfileData;
}

export interface UserProfileData {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  adminPhoneNumber?: string;
  nik: string;
  npwp: string;
  island: string;
  province: string;
  city: string;
  district: string;
  postalCode: string;
  uniqueUserCode: string;
  address: string;
  warehouseAddress: string;
  role: {
    id: number;
    name: string;
    description: string;
  };
  sk: string;
  shops: {
    tokopedia: string;
    shopee: string;
    tiktok: string;
  };
  status: number;
  roleName: string;
  bankType: string;
  bankAccountNumber: string;
  isReceivingAgent: boolean;
}
