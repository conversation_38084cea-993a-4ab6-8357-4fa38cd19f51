import ButtonGroup from "@/components/common/ButtonGroup";
import Input from "@/components/common/Input";
import InputGroup from "@/components/common/InputGroup";
import ApplicationModalData from "@/components/common/RequestModal/RequestDetail/ApplicationRequest/ApplicationData";
import { AdminRequestProvider } from "@/context/AdminRequestContext";
import useBreakpoints from "@/hooks/useBreakpoints";
import { AppDispatch } from "@/store";
import { terminateDistributor } from "@/store/slices/adminAgentDetailsSlice";
import { AgentDistributorDetailsData } from "@/utils/types/distributor-agent";
import { Button, Checkbox, Flex, Modal, Paper, Text } from "@mantine/core";
import { Drawer } from "@mantine/core";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useDispatch } from "react-redux";

interface PHKDistributorProps {
  opened: boolean;
  onClose: () => void;
  agentDetails: AgentDistributorDetailsData;
}

const PHKDistributor = ({
  opened,
  onClose,
  agentDetails,
}: PHKDistributorProps) => {
  const dispatch: AppDispatch = useDispatch();
  const router = useRouter();
  const [reason, setReason] = useState("");
  const [checked, setChecked] = useState(false);

  const onSuccessDelete = () => {
    router.push("/admin/agent");
  };

  const handleSubmit = () => {
    dispatch(terminateDistributor(agentDetails.uoc, reason, onSuccessDelete));
    onClose();
  };

  const { mobile } = useBreakpoints();
  const renderModalChildren = () => {
    return (
      <Flex direction={"column"} gap={"sm"} justify={"center"} align={"center"}>
        <h1
          style={{
            textAlign: "center",
          }}
        >
          Pemberhentian Hubungan Kerja Distributor
        </h1>
        <Text c={"text-light"} ta={"center"}>
          Pemutusan hubungan kerja akan menghentikan hubungan kerja antara Anda
          dengan distributor terkait. Mohon untuk secara bijak membuat keputusan
          ini.
        </Text>
        <Text ta={"center"} fw={500}>
          {agentDetails.name}
        </Text>
        <AdminRequestProvider>
          <ApplicationModalData
            isPhkDistributor
            type={"Distributor"}
            requestStep={1} // just any number
            newAgent={{
              nik: agentDetails.nik,
              npwp: agentDetails.npwp,
              name: agentDetails.name,
              address: agentDetails.address,
              email: agentDetails.email || "",
              phone: agentDetails.ownerPhone || "",
              adminPhone: agentDetails.adminPhone || "",
              warehouseAddress: agentDetails.warehouseAddress || "",
              sk: agentDetails.sk || "",
              shops: agentDetails.shop || agentDetails.shops,
            }}
          />
        </AdminRequestProvider>
        <InputGroup label="Alasan">
          <Input
            onChange={(e) => setReason(e.target.value)}
            placeholder="Masukkan alasan pemberhentian"
          />
        </InputGroup>
        <Checkbox
          label="Saya memahami bahwa pemutusan hubungan kerja ini jika disetujui akan secara final memutuskan hubungan dan dampak berdampak ke distributor tersebut."
          checked={checked}
          onChange={(e) => setChecked(e.target.checked)}
          w={"100%"}
        />
        <Paper
          bg="error.1"
          px="lg"
          py="sm"
          radius="8px 8px 0 0"
          w={mobile ? "108%" : "104.3%"}
          mt={"md"}
        >
          <Text c="error" fw={500} fz="0.9rem">
            Pastikan Anda memilih distributor yang tepat!
          </Text>
        </Paper>
        <ButtonGroup justify={"space-between"} w={"100%"}>
          <Button variant="subtle" onClick={onClose}>
            Kembali
          </Button>
          <Button onClick={handleSubmit} disabled={!checked || !reason}>
            Submit
          </Button>
        </ButtonGroup>
      </Flex>
    );
  };

  return (
    <>
      {mobile ? (
        <Drawer
          opened={opened}
          onClose={onClose}
          size={"92vh"}
          radius={"8px 8px 0 0"}
          position="bottom"
        >
          {opened && renderModalChildren()}
        </Drawer>
      ) : (
        <Modal opened={opened} onClose={onClose} radius={8} size={"lg"}>
          {opened && renderModalChildren()}
        </Modal>
      )}
    </>
  );
};

export default PHKDistributor;
