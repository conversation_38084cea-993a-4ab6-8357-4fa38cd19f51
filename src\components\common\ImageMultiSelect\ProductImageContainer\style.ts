import { COLORS } from "@/styles/color";
import styled from "styled-components";

export const ImageOptionContainer = styled.div<{ selected: boolean }>`
  display: flex;
  justify-content: center;
  align-content: center;

  width: 200px;
  aspect-ratio: 1/1;
  border-radius: 12px;
  overflow: hidden;
  border: 2px
    ${({ selected }) =>
      selected ? `solid ${COLORS.accent}` : `dashed ${COLORS.disabled}`};
  cursor: pointer;
  outline: ${({ selected }) =>
    selected ? `1px solid ${COLORS.accent}` : "none"};
  transition: ease-in-out 0.3s;
  opacity: ${({ selected }) => (selected ? 1 : 0.5)};

  &:hover {
    opacity: 1;
  }

  & > img {
    width: 100%;
    height: auto;
    aspect-ratio: 1/1;
    object-fit: cover;
    transition: ease-in-out 0.3s;

    &:hover {
      transform: scale(1.05);
    }
  }
`;
