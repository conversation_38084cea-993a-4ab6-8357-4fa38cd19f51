# Hotto-ADMS

Hotto-ADMS is an administrative dashboard designed for Hotto's internal team to manage distributors and agents efficiently. The platform provides an intuitive interface for handling distributor and agent details, tracking activities, and ensuring smooth operational workflows.

![Hotto](https://hotto.co.id/wp-content/uploads/2024/09/Logo-3.png "Hotto Logo")

## Tech Stack

- React.js
- Next.js
- Styled Components
- Redux

## Project Structure

```
src/
├── app/ # Routing and Pages
├── components/ # Reusable UI components
├── lib/ # Library/Package related source code
├── store/ # Global state management
├── utils/ # Utility functions
└── services/ # API service logic
```

## Contributing

Contributions are welcome! Follow these steps to contribute:

### Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/kodio-id/hotto-adms-fe.git
   ```

2. Navigate to the project directory:

   ```bash
   cd hotto-adms
   ```

3. Install dependencies:

   ```bash
   npm install
   ```

4. Set up environment variables:
   Create a `.env` file in the root directory and add the following:

   ```env
   NEXT_PUBLIC_API_KEY=API_KEY
   ```

5. Start the development server:

   ```bash
   npm run dev
   ```

6. Open the app in your browser at http://localhost:3000.

### Contributing Steps

1. Fork the repository.

2. Create a new branch for your feature/fix:

   ```bash
   git checkout -b feature-name
   ```

3. Commit your changes:

   ```bash
   git commit -m "Add your message here"
   ```

4. Push to the branch:

   ```bash
   git push origin feature-name
   ```

5. Open a pull request.
