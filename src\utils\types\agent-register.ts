import { ShopsInfo } from "./distributor-register";

export interface AgentPersonalInfoFormValues {
  name: string;
  nik: string;
  email: string;
  phoneNumber: string;
  island: string;
  province: string;
  regency: string;
  district: string;
  village: string;
  postalCode: string;
  address: string;
}

export interface AgentRegisterPayload {
  name: string;
  email: string;
  phoneNumber: string;
  nik: string;
  ktp: string;
  selfieKtp: string;
  island: string;
  province: string;
  city: string;
  district: string;
  kelurahan: string;
  postalCode: string;
  address: string;
  warehouseAddress: string;
  warehouseIsland: string;
  warehouseProvince: string;
  warehouseCity: string;
  warehouseDistrict: string;
  warehouseKelurahan: string;
  warehousePostalCode: string;
  adminPhoneNumber?: string;
  shops?: ShopsInfo;
  distributorUOC: string;
}
