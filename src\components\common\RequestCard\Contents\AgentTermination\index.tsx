import { Di<PERSON><PERSON>, <PERSON>lex, <PERSON>, But<PERSON> } from "@mantine/core";
import RequestCardHeader from "../../RequestCardHeader";
import { RequestCardData, RequestType } from "@/utils/types/request";
import { useMediaQuery } from "@mantine/hooks";
import { useState } from "react";
import ReviewStatus from "@/components/common/ReviewStatus";
import RequestModal from "@/components/common/RequestModal";

const AgentTerminationRequestCard = ({ ...request }: RequestCardData) => {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [openedModal, setOpenedModal] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<"accept" | "reject">(
    "accept"
  );

  const sourceRole = request.requesterRole?.toLowerCase();

  const handleAcceptRequest = () => {
    setSelectedStatus("accept");
    setOpenedModal(true);
  };

  const handleRejectRequest = () => {
    setSelectedStatus("reject");
    setOpenedModal(true);
  };

  return (
    <Flex direction={"column"}>
      <RequestModal
        opened={openedModal}
        onClose={() => setOpenedModal(false)}
        requestId={request.id}
        status={selectedStatus}
        type={RequestType.Termination}
      />
      <RequestCardHeader
        createdAt={request.createdAt}
        leftText={
          sourceRole === "agent"
            ? `Agent '${request.requesterName}' ingin mengajukan pemutusan hubungan kerja`
            : `Distributor '${request.requesterName}' ingin mengajukan pemutusan hubungan kerja dengan agent '${request.metadatas[0].oldValue}'`
        }
      />
      <Divider />
      {request.metadatas[0] ? (
        <Flex
          direction={isMobile ? "column" : "row"}
          align={isMobile ? "flex-start" : "center"}
          justify={"space-between"}
          p={"lg"}
          gap={"lg"}
        >
          <Flex direction={"column"} gap={"xs"}>
            <Text fw={500}>{`Nama Agent: ${
              sourceRole === "agent"
                ? request.requesterName
                : request.metadatas[0].oldValue
            }
            `}</Text>
            <Text fw={500} c={"text-light"}>{`Alasan: ${request.reason}`}</Text>
          </Flex>
          <Flex w={isMobile ? "100%" : "fit-content"} justify={"flex-end"}>
            {request.metadatas[0].status === "pending" ? (
              <Flex
                direction={"row"}
                gap={"md"}
                w={isMobile ? "100%" : "fit-content"}
                justify={"center"}
              >
                <Button variant="subtle" onClick={handleRejectRequest}>
                  Reject
                </Button>
                <Flex w={isMobile ? "33%" : "fit-content"}>
                  <Button onClick={handleAcceptRequest}>Accept</Button>
                </Flex>
              </Flex>
            ) : (
              <ReviewStatus status={request.metadatas[0].status} />
            )}
          </Flex>
        </Flex>
      ) : (
        <Text c={"text-light"} p={"lg"} fw={500}>
          Tidak ada detail data
        </Text>
      )}
    </Flex>
  );
};

export default AgentTerminationRequestCard;
