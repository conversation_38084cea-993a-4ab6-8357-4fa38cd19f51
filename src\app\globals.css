@import "@/styles/normalize.css";
@import "@/styles/mantine.css";

@font-face {
  font-family: "Larsseit";
  src: url("/fonts/Larsseit-Thin.otf") format("opentype");
  font-weight: 100;
  font-style: normal;
}

@font-face {
  font-family: "Larsseit";
  src: url("/fonts/Larsseit-ThinItalic.otf") format("opentype");
  font-weight: 100;
  font-style: italic;
}

@font-face {
  font-family: "Larsseit";
  src: url("/fonts/Larsseit-Light.otf") format("opentype");
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: "Larsseit";
  src: url("/fonts/Larsseit-LightItalic.otf") format("opentype");
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: "Larsseit";
  src: url("/fonts/Larsseit-Regular.otf") format("opentype");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "Larsseit";
  src: url("/fonts/Larsseit-Italic.otf") format("opentype");
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: "Larsseit";
  src: url("/fonts/Larsseit-Medium.otf") format("opentype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "Larsseit";
  src: url("/fonts/Larsseit-MediumItalic.otf") format("opentype");
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: "Larsseit";
  src: url("/fonts/Larsseit-Bold.otf") format("opentype");
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: "Larsseit";
  src: url("/fonts/Larsseit-BoldItalic.otf") format("opentype");
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: "Larsseit";
  src: url("/fonts/Larsseit-ExtraBold.otf") format("opentype");
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: "Larsseit";
  src: url("/fonts/Larsseit-ExtraBoldItalic.otf") format("opentype");
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: "Recoleta";
  src: url("/fonts/Recoleta-Regular.otf") format("opentype");
  font-weight: 400; /* Typically 400 for regular */
  font-style: normal;
}

@font-face {
  font-family: "Recoleta";
  src: url("/fonts/Recoleta-Bold.ttf") format("truetype");
  font-weight: 700; /* Match the bold weight */
  font-style: normal;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

body {
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* add custom scrollbar using accent border radius 12 */
::-webkit-scrollbar {
  width: 5px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--mantine-color-primary-filled);
  border-radius: 4px;
}

.scrollable-form ::-webkit-scrollbar {
  width: 4px;
  border-radius: 8px;
}

.scrollable-form::-webkit-scrollbar-thumb {
  background: var(--mantine-color-primary-filled);
}

canvas.react-pdf__Page__canvas {
  margin-bottom: var(--mantine-spacing-xl);
}
