import UpdateLog from '@/components/section/AdminAgentDistributor/UpdateLog'
import { AppDispatch, RootState } from '@/store'
import { fetchRequestDetail } from '@/store/slices/adminRequestSlice'
import { RequestType } from '@/utils/types/request'
import { Flex, Text } from '@mantine/core'
import { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import PreviousUpdateLogs from './PreviousUpdateLogs'
import { useMediaQuery } from '@mantine/hooks'
import Warning from '@/components/common/Warning'

interface RequestDetailContentProps {
 requestId: number
 field?: string
}

const DataChangeRequestDetail = ({
 requestId,
 field = '',
}: RequestDetailContentProps) => {
 const dispatch: AppDispatch = useDispatch()
 const { dataChangeRequest } = useSelector(
  (state: RootState) => state.adminRequest
 )

 useEffect(() => {
  dispatch(fetchRequestDetail(requestId, RequestType.DataChange, field))
 }, [requestId])

 const isMobile = useMediaQuery('(max-width: 768px)')
 return (
  <>
   {dataChangeRequest && (
    <>
     <Flex direction={'column'} justify={'center'} align={'center'}>
      <Text
       fw={600}
      >{`${dataChangeRequest.sourceRole} '${dataChangeRequest.sourceAgent}'`}</Text>

      {dataChangeRequest.complaint && (
       <Warning
        title="Request ini mendapakan complaint dari distributor!"
        caption={`Complaint: ${dataChangeRequest.complaint}`}
       />
      )}

      <UpdateLog
       log={{
        old: dataChangeRequest.oldValue,
        new: dataChangeRequest.newValue,
        fieldChanged: dataChangeRequest.field,
        reason: dataChangeRequest.reason,
       }}
       align="center"
       fw={500}
      />
     </Flex>
     <Flex
      mih={isMobile ? '33vh' : 'fit-content'}
      mah={'33vh'}
      style={{ overflowY: 'scroll' }}
      w={'100%'}
     >
      {dataChangeRequest.updateLogs?.length > 0 && (
       <PreviousUpdateLogs updateLogs={dataChangeRequest.updateLogs} />
      )}
     </Flex>
    </>
   )}
  </>
 )
}

export default DataChangeRequestDetail
