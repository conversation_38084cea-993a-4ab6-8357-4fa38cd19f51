import { formatNumber } from "@/utils/common-functions";
import { getFilteredChartTooltipPayload } from "@mantine/charts";
import { Paper, Text, PaperProps } from "@mantine/core";

export interface ChartTooltipProps extends PaperProps {
  title: string;
  label: string;
  payload: Record<"name" | "value", string>[] | undefined;
  formatValueAs?: "currency" | "percentage" | "rating";
}

const ChartTooltip = ({
  title,
  label,
  payload,
  formatValueAs,
  ...props
}: ChartTooltipProps) => {
  if (!payload) return null;

  const data: ChartTooltipProps["payload"] =
    getFilteredChartTooltipPayload(payload);

  return (
    <Paper px="sm" py="xs" withBorder shadow="sm" {...props}>
      <Text c="text" fw={500}>
        {title}
      </Text>
      <Text fz="xs">{label}</Text>
      {data.map((item) => (
        <Text mt="xs" key={item.name}>
          {formatValueAs
            ? formatNumber(Number(item.value), formatValueAs)
            : item.value}
        </Text>
      ))}
    </Paper>
  );
};

export default ChartTooltip;
