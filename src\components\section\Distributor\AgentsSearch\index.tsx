'use client'

import BackButton from '@/components/common/BackButton'
import NotFoundPlaceholder from '@/components/common/NotFoundPlaceholder'
import dynamic from 'next/dynamic'
const SearchBar = dynamic(() => import('@/components/common/SearchBar'), {
 ssr: false,
})
import { Flex, SimpleGrid } from '@mantine/core'
import SearchHistory from '@/components/common/SearchHistory'
import { useSearchParams } from 'next/navigation'
import { useDispatch, useSelector } from 'react-redux'
import { AppDispatch, RootState } from '@/store'
import { useEffect } from 'react'
import { ADMIN_AGENTS_SEARCH_HISTORY_VAR } from '@/utils/constants'
import { useLocalStorage } from '@mantine/hooks'
import AgentCard from '../AgentCard'
import { fetchAgentList } from '@/store/slices/distributorAgentsSlice'

const AgentsSearch = () => {
 const dispatch: AppDispatch = useDispatch()
 const searchParams = useSearchParams()
 const search = searchParams?.get('search') || ''
 const [histories, setHistories] = useLocalStorage<string[]>({
  key: ADMIN_AGENTS_SEARCH_HISTORY_VAR,
  defaultValue: [],
  getInitialValueInEffect: true,
 })

 const { agentList: searchResult, loading } = useSelector(
  (state: RootState) => state.distributorAgents
 )

 useEffect(() => {
  if (!search) return
  dispatch(fetchAgentList(search))
  setHistories((prev) => [...new Set([search, ...prev])])
 }, [search])

 const handleRemoveHistory = (value: string) => {
  if (!histories) return
  setHistories(histories.filter((v) => v !== value))
 }

 const notEmpty = search && searchResult && searchResult.length > 0 && !loading

 const showError =
  search && (!searchResult || searchResult.length === 0) && !loading

 return (
  <Flex direction={'column'} gap={'md'}>
   <Flex direction={'row'} gap={'xs'} align={'center'} py={'md'}>
    <BackButton href="/agent" />
    <SearchBar />
   </Flex>
   {!search && (
    <SearchHistory histories={histories} onRemove={handleRemoveHistory} />
   )}
   {notEmpty && (
    <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }}>
     {searchResult.map((value, index) => (
      <AgentCard
       key={index}
       uoc={value.uoc}
       name={value.name}
       status={value.status}
       adminPhone={value.adminPhone}
       ownerPhone={value.phone}
      />
     ))}
    </SimpleGrid>
   )}
   {showError && (
    <NotFoundPlaceholder
     message={`Kami tidak menemukan agent dengan nama atau nomor telepon '${search}'`}
    />
   )}
  </Flex>
 )
}

export default AgentsSearch
