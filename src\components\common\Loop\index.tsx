import { ReactNode, Fragment } from "react";

export interface LoopProps {
  count: number;
  prefix?: string;
  children: ReactNode;
}

/**
 * Renders children count times
 * @param {number} count The number of times the children should be rendered
 * @param {string} [prefix='LoopComponent'] The prefix for the key
 * @param {ReactNode} children The children to render
 * @returns {ReactNode} The rendered children
 */
const Loop: React.FC<LoopProps> = ({
  count,
  prefix = "LoopComponent",
  children,
}) => {
  return Array.from({ length: count }).map((_, index) => (
    <Fragment key={`${prefix}-${index + 1}`}>{children}</Fragment>
  ));
};

export default Loop;
