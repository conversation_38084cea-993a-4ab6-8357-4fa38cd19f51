import { AppDispatch, RootState } from "@/store";
import { Center, Stack, Title, Button, Text, Flex } from "@mantine/core";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import UploadPaymentForm from "./Form";
import SuccessUploadPayment from "./Success";
import { MAX_WIDTH } from "@/utils/constants";

const AgentUploadPayment = ({ token }: { token: string }) => {
  const dispatch: AppDispatch = useDispatch();
  const { isLoading } = useSelector((state: RootState) => state.agentRegister);
  const [active, setActive] = useState(0);
  const [continuable, setContinuable] = useState(false);

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [active]);

  return (
    <Center w="100%" maw={MAX_WIDTH} mx="auto">
      <Stack gap="xs" align="center">
        <Title ta="center" order={2}>
          Selam<PERSON>, Aplikasi Anda Diterima!
        </Title>
        <Stack gap={32} w="100%">
          {active === 0 && (
            <>
              <Text c="text-light" mb="lg" ta="center">
                Isi form ini untuk menjadi partner Agent Hotto.
              </Text>
              <UploadPaymentForm
                onFormValid={(isValid) => {
                  setContinuable(isValid);
                }}
              />
              <Flex justify="center">
                <Button
                  w={{ base: "100%", sm: "fit-content" }}
                  loading={isLoading}
                  disabled={!continuable}
                  // TODO: implement submit payment in agent registration
                  // onClick={() => {
                  //   dispatch(submitPaymentProof(token)).then(() => {
                  //     setActive(1);
                  //   });
                  // }}
                >
                  Selanjutnya
                </Button>
              </Flex>
            </>
          )}
          {active === 1 && <SuccessUploadPayment />}
        </Stack>
      </Stack>
    </Center>
  );
};

export default AgentUploadPayment;
