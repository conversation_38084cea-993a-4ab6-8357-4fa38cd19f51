import ConfirmationModal from "@/components/common/ConfirmationModal";
import IconButton from "@/components/common/IconButton";
import { AppDispatch } from "@/store";
import { deleteBroadcast } from "@/store/slices/adminBroadcastSlice";
import { deleteRequestDraft } from "@/store/slices/distributorRequestSlice";
import { formatDateToString } from "@/utils/common-functions";
import { DraftType } from "@/utils/types";
import { ComposeRequestType } from "@/utils/types/request";
import { Flex, Paper, Text } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { useState } from "react";
import { useDispatch } from "react-redux";

interface DrafBroadcastCardProps {
  id: number;
  title: string;
  createdAt: string;
  requestType?: ComposeRequestType;
  setSelectedId: (id: number | undefined) => void;
  setSelectedRequestType?: (
    requestType: ComposeRequestType | undefined
  ) => void;
  draftType: DraftType;
}

const DraftCard = ({
  id,
  title,
  createdAt,
  requestType,
  setSelectedId,
  draftType,
  setSelectedRequestType,
}: DrafBroadcastCardProps) => {
  const dispatch: AppDispatch = useDispatch();

  const [openDeleteModal, setOpenDeleteModal] = useState(false);

  const handleDelete = () => {
    setOpenDeleteModal(true);
  };
  const handleConfirmDelete = () => {
    if (draftType === DraftType.Broadcast) {
      dispatch(deleteBroadcast(id));
    } else if (draftType === DraftType.Request) {
      dispatch(deleteRequestDraft(id));
    }
    setOpenDeleteModal(false);
  };

  const isMobile = useMediaQuery("(max-width: 768px)");
  return (
    <Paper bg={"bg"} radius={"8px"} p={"md"}>
      <ConfirmationModal
        title="Hapus Draft"
        message="Yakin ingin menghapus draft ini?"
        opened={openDeleteModal}
        onClose={() => {
          setOpenDeleteModal(false);
        }}
        confirmLabel="Hapus"
        onConfirm={handleConfirmDelete}
      />
      <Flex gap={"md"} justify={"space-between"} align={"flex-start"}>
        <Text
          fw={500}
          w={"66%"}
          onClick={() => {
            setSelectedId(id);
            if (requestType && setSelectedRequestType) {
              setSelectedRequestType(requestType);
            }
          }}
          style={{ cursor: "pointer" }}
        >
          {title}
        </Text>
        <Flex gap={"md"} align={"center"} justify={"flex-end"}>
          <Text
            fw={500}
            c={"text-light"}
            size={isMobile ? "xs" : "sm"}
            ta={"right"}
          >
            {createdAt && formatDateToString(createdAt, "number")}
          </Text>
          <IconButton icon="action/delete" onClick={handleDelete} />
        </Flex>
      </Flex>
    </Paper>
  );
};

export default DraftCard;
