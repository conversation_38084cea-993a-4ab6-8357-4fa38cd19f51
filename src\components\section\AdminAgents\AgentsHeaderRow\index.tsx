import { Button, Flex } from "@mantine/core";
import SearchBar from "@/components/common/SearchBar";
import Link from "next/link";
const AgentsHeaderRow = () => {
  return (
    <Flex
      direction={"row"}
      align={"center"}
      justify={"space-between"}
      pb={"md"}
    >
      <Flex maw={"75%"}>
        <SearchBar href="/admin/agent/browse" />
      </Flex>
      <Button component={Link} href="/admin/agent/waitlist">
        Lihat Waitlist
      </Button>
    </Flex>
  );
};

export default AgentsHeaderRow;
