import { COLORS } from "@/styles/color";
import { SPACES } from "@/styles/spacing";
import styled from "styled-components";

interface InputGroupWrapperProps {
  w?: string;
}

export const InputGroupWrapper = styled.div<InputGroupWrapperProps>`
  display: flex;
  gap: ${SPACES.xs};
  position: relative;
  width: 100%;
  flex-direction: column;
  width: ${({ w }) => w || "100%"};

  label {
    font-weight: 500;
    color: ${COLORS.primary};
    font-size: 1em;

    & > .star {
      color: ${COLORS.error};
    }
  }

  input,
  textarea {
    border: 1px solid ${COLORS.disabled};
    border-radius: ${SPACES.xs};
    width: 100%;

    &::placeholder {
      color: ${COLORS.disabled};
    }

    &:focus {
      outline: 1px solid ${COLORS.accent};
    }
  }

  .mantine-Input-input {
    border: 1px solid ${COLORS.disabled};
    border-radius: ${SPACES.xs};

    &::placeholder {
      color: ${COLORS.disabled};
    }

    &:focus {
      outline: 1px solid ${COLORS.accent};
    }
  }
  .mantine-MultiSelect-inputField {
    border: none;

    &:focus {
      outline: none;
    }
  }

  textarea {
    resize: none;
  }
`;
