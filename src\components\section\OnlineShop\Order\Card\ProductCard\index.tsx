import { OrderItem } from "@/utils/types/online-shop";
import {
  Group,
  Image,
  Text,
  Stack,
  Box,
  Grid,
  NumberFormatter,
} from "@mantine/core";

export interface OrderProductCardProps {
  product: OrderItem;
}

const OrderProductCard: React.FC<OrderProductCardProps> = ({ product }) => {
  return (
    <Grid overflow="hidden">
      <Grid.Col span="content">
        <Image
          radius="md"
          fit="cover"
          h="5.5rem"
          w="5.5rem"
          src={product.productImages[0]}
          alt={product.productName}
          bd="2px solid var(--mantine-primary-color-filled)"
          style={{ aspectRatio: "1/1" }}
        />
      </Grid.Col>
      <Grid.Col span="auto" miw={0}>
        <Stack justify="space-between" h="100%">
          <Box>
            <Text title={product.productName} fw={500} truncate>
              {product.productName}
            </Text>
            <Group justify="space-between">
              {/* TODO: change to real data when ready */}
              <Text c="text">Variasi: 1 Pouch</Text>
            </Group>
          </Box>
          <Group justify="space-between" mb="xs2">
            <Text fw={500}>
              {`${product.qty} x `}
              <NumberFormatter value={product.unitPrice} />
            </Text>
          </Group>
        </Stack>
      </Grid.Col>
    </Grid>
  );
};

export default OrderProductCard;
