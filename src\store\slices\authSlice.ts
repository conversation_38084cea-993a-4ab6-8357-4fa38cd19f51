import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import {
  LOGIN,
  LOGIN_WITH_PHONE,
  REFRESH_TOKEN,
  RESET_PASSWORD,
} from "@/utils/api-routes";
import {
  LoginAdminPayload,
  LoginUserPayload,
  AuthResponse,
  UserProfileResponse,
} from "@/utils/types/auth";
import { toast } from "sonner";
import <PERSON><PERSON> from "js-cookie";
import api from "../../lib/axios";
import {
  ACCESS_TOKEN_VAR,
  BLACKLISTED_STATUS_CODE,
  IS_BROWSER,
  REFRESH_TOKEN_VAR,
  ROLE_VAR,
} from "@/utils/constants";
import { AxiosError, AxiosResponse } from "axios";
import { InferType } from "yup";
import { resetPasswordSchema } from "@/components/section/ResetPasswordForm";

export type RoleType = "admin" | "agent" | "distributor";

const getTokenFromCookie = (key: string) => Cookie.get(key) || undefined;
const getRefreshTokenFromStorage = () =>
  IS_BROWSER ? localStorage.getItem(REFRESH_TOKEN_VAR) || undefined : undefined;
const getRoleFromStorage = () =>
  IS_BROWSER ? localStorage.getItem(ROLE_VAR) || undefined : undefined;

export interface AuthState {
  accessToken: string | undefined;
  refreshToken: string | undefined;
  role: RoleType | undefined;
  isLoading: boolean;
  error: string | undefined;
  loggedOut: boolean;
}

const initialState: AuthState = {
  accessToken: getTokenFromCookie(ACCESS_TOKEN_VAR) || undefined,
  refreshToken: getRefreshTokenFromStorage(),
  role: getRoleFromStorage() as RoleType,
  isLoading: false,
  error: undefined,
  loggedOut: false,
};

export const login = createAsyncThunk<
  AuthResponse,
  {
    loginData: LoginUserPayload | LoginAdminPayload;
    handleBlacklisted?: () => void;
  }
>(
  "auth/login",
  async ({ loginData, handleBlacklisted }, { rejectWithValue }) => {
    try {
      const url = (loginData as LoginUserPayload).phone
        ? LOGIN_WITH_PHONE
        : LOGIN;
      const res = await api.post<AuthResponse>(url, loginData);
      if (res.data.status_code === 200 && res.data.data) {
        const { accessToken, refreshToken, role } = res.data.data;
        if (accessToken) {
          Cookie.set(ACCESS_TOKEN_VAR, accessToken);
          toast.success("Welcome to Hotto ADMS!");
        }
        if (IS_BROWSER) {
          localStorage.setItem(REFRESH_TOKEN_VAR, refreshToken);
          localStorage.setItem(ROLE_VAR, role);
        }
        return res.data;
      } else {
        toast.error("Login failed. Please try again.");
        return rejectWithValue("Login failed. Please try again.");
      }
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        if (error.response?.data?.status_code === BLACKLISTED_STATUS_CODE) {
          handleBlacklisted?.();
        } else {
          toast.error(
            error.response?.data?.error || "Login failed. Please try again."
          );
        }
        return rejectWithValue(error.message);
      }
      toast.error("Login failed. Please try again.");
      return rejectWithValue("Login failed. Please try again.");
    }
  }
);

export const refreshToken = createAsyncThunk<AuthResponse, string>(
  "auth/refreshToken",
  async (refreshToken, { rejectWithValue }) => {
    try {
      const res = await api.post<AuthResponse>(REFRESH_TOKEN, { refreshToken });
      if (res.data.status_code === 200 && res.data.data) {
        const { accessToken, refreshToken, role } = res.data.data;
        if (accessToken) {
          Cookie.set(ACCESS_TOKEN_VAR, accessToken);
          if (IS_BROWSER) {
            localStorage.setItem(REFRESH_TOKEN_VAR, refreshToken);
            localStorage.setItem(ROLE_VAR, role);
          }
        }
        return res.data;
      } else {
        toast.error("Refresh login failed, please try again.");
        return rejectWithValue("Refresh login failed, please try again.");
      }
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        toast.error(
          error.response?.data?.error ||
            "Refresh login failed, please try again."
        );
        return rejectWithValue(error.message);
      }
      toast.error("Refresh login failed, please try again.");
      return rejectWithValue("Refresh login failed, please try again.");
    }
  }
);

export const resetPassword = createAsyncThunk<
  UserProfileResponse,
  InferType<typeof resetPasswordSchema>
>("auth/resetPassword", async (data, { rejectWithValue }) => {
  const failed = "Password gagal diubah, silahkan coba lagi.";
  try {
    const res: AxiosResponse<UserProfileResponse> = await api.patch(
      RESET_PASSWORD,
      {
        newPassword: data.password,
        confirmPassword: data.confirm,
      }
    );
    if (res.data.status_code === 200 && res.data.data) {
      toast.success("Password berhasil diubah!");
      toast.info("Simpan dan ingat passwordmu dengan baik!");
      return res.data;
    } else {
      toast.error(res.data.message);
      return rejectWithValue(res.data.message);
    }
  } catch (error: unknown) {
    if (error instanceof AxiosError) {
      toast.error(error.response?.data?.error || failed);
      return rejectWithValue(error.message);
    }
    toast.error(failed);
    return rejectWithValue(failed);
  }
});

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    logout(state) {
      const userRole = state.role;
      state.loggedOut = true;
      state.accessToken = undefined;
      state.refreshToken = undefined;
      state.role = undefined;
      state.error = undefined;
      Cookie.remove(ACCESS_TOKEN_VAR);
      if (IS_BROWSER) {
        localStorage.removeItem(REFRESH_TOKEN_VAR);
        localStorage.removeItem(ROLE_VAR);
        if (userRole === "admin") {
          window.location.href = "/login/admin";
        } else {
          window.location.href = "/login";
        }
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.accessToken = action.payload.data.accessToken;
        state.refreshToken = action.payload.data.refreshToken;
        state.role = action.payload.data.role as RoleType;
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.accessToken = action.payload.data.accessToken;
        state.refreshToken = action.payload.data.refreshToken;
      })
      .addCase(refreshToken.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      .addCase(resetPassword.pending, (state) => {
        state.isLoading = true;
        state.error = undefined;
      })
      .addCase(resetPassword.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(resetPassword.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { logout } = authSlice.actions;
export default authSlice.reducer;
