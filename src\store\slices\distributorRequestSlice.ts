import api from "@/lib/axios";
import {
  AgentTerminationDraftData,
  RequestCardData,
  RequestDetailInvalidMarketplace,
  RequestType,
} from "@/utils/types/request";
import { createSlice } from "@reduxjs/toolkit";
import { AppDispatch } from "..";
import { toast } from "sonner";
import { Draft } from "@/utils/types";
import {
  CREATE_AGENT_TERMINATION_REQUEST,
  CREATE_DATA_CHANGE_REQUEST,
  DELETE_REQUEST_DRAFT,
  GET_ADMIN_REQUEST_DETAIL,
  GET_ADMIN_REQUESTS,
  GET_REQUEST_DRAFT_DETAIL,
  GET_REQUESTS_DRAFTS,
  UPDATE_REQUEST_DRAFT,
  UPDATE_MARKETPLACE_LINK,
} from "@/utils/api-routes";
import {
  AgentDistributorDetailsData,
  AgentDistributorDetailsForceUpdateData,
  AgentDistributorUpdateDataDetails,
} from "@/utils/types/distributor-agent";
import { log } from "node:console";

interface RequestState {
  inRequests: RequestCardData[];
  outRequests: RequestCardData[];
  invalidMarketplaceRequest: RequestDetailInvalidMarketplace | undefined;
  draftRequests: Draft[];
  dataChangeDraft: AgentDistributorUpdateDataDetails | undefined;
  agentTerminationDraft: AgentTerminationDraftData | undefined;
  newRequestCount: number;
  error: string | undefined;
  loading: boolean;
}

const initialState: RequestState = {
  inRequests: [],
  outRequests: [],
  draftRequests: [],
  newRequestCount: 0,
  invalidMarketplaceRequest: undefined,
  dataChangeDraft: undefined,
  agentTerminationDraft: undefined,
  error: undefined,
  loading: false,
};

const reqeustSlice = createSlice({
  name: "requestSlice",
  initialState,
  reducers: {
    setError(state, action) {
      state.error = action.payload;
      toast.error(action.payload);
    },
    setLoading(state, action) {
      state.loading = action.payload;
    },
    setReceivedRequests(state, action) {
      state.inRequests = action.payload.requests;
    },
    setSentRequests(state, action) {
      state.outRequests = action.payload.requests;
    },
    setDraftRequests(state, action) {
      state.draftRequests = action.payload;
    },
    setRequestDetail(state, action) {
      switch (action.payload.requestType) {
        case RequestType.DistributorCheckMarketplaceLinks:
          state.invalidMarketplaceRequest = action.payload.data;
      }
    },
    setDataChangeDraft(state, action) {
      state.dataChangeDraft = action.payload;
    },
    setAgentTerminationDraft(state, action) {
      state.agentTerminationDraft = action.payload;
    },
    setNewRequestCount(state, action) {
      state.newRequestCount = action.payload.count;
    },
  },
});

export const fetchSentRequests = () => async (dispatch: AppDispatch) => {
  try {
    dispatch(reqeustSlice.actions.setLoading(true));
    const res = await api.get(GET_ADMIN_REQUESTS(1, "all", "desc", true));
    dispatch(reqeustSlice.actions.setSentRequests(res.data.data));
  } catch (error: unknown) {
    dispatch(
      reqeustSlice.actions.setError(
        error instanceof Error ? error.message : "Error fetching requests"
      )
    );
  } finally {
    dispatch(reqeustSlice.actions.setLoading(false));
  }
};

export const fetchReceivedRequests = () => async (dispatch: AppDispatch) => {
  try {
    dispatch(reqeustSlice.actions.setLoading(true));
    const res = await api.get(GET_ADMIN_REQUESTS(1, "all", "desc", false));
    dispatch(reqeustSlice.actions.setReceivedRequests(res.data.data));
  } catch (error: unknown) {
    dispatch(
      reqeustSlice.actions.setError(
        error instanceof Error ? error.message : "Error fetching requests"
      )
    );
  } finally {
    dispatch(reqeustSlice.actions.setLoading(false));
  }
};

export const fetchDraftRequests = () => async (dispatch: AppDispatch) => {
  try {
    dispatch(reqeustSlice.actions.setLoading(true));
    const res = await api.get(GET_REQUESTS_DRAFTS);
    dispatch(reqeustSlice.actions.setDraftRequests(res.data.data));
  } catch (error: unknown) {
    dispatch(
      reqeustSlice.actions.setError(
        error instanceof Error ? error.message : "Error fetching requests"
      )
    );
  } finally {
    dispatch(reqeustSlice.actions.setLoading(false));
  }
};

export const fetchRequestDetailForDistributor =
  (requestId: number, requestType: RequestType, field?: string) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(reqeustSlice.actions.setLoading(true));
      const res = await api.get(GET_ADMIN_REQUEST_DETAIL(requestId, field));
      res.data.requestType = requestType;
      dispatch(reqeustSlice.actions.setRequestDetail(res.data));
    } catch (error: unknown) {
      dispatch(
        reqeustSlice.actions.setError(
          error instanceof Error
            ? error.message
            : "Error fetching request detail"
        )
      );
    } finally {
      dispatch(reqeustSlice.actions.setLoading(false));
    }
  };

export const updateMarketplaceLink =
  (requestId: number, shopType: string, value: string) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(reqeustSlice.actions.setLoading(true));
      const res = await api.patch(UPDATE_MARKETPLACE_LINK, {
        requestId: requestId,
        type: shopType,
        url: value,
      });
      if (res) {
        dispatch(fetchReceivedRequests());
        return true;
      }
      return false;
    } catch (error: unknown) {
      dispatch(
        reqeustSlice.actions.setError(
          error instanceof Error
            ? error.message
            : "Error updating marketplace link"
        )
      );
      return false;
    } finally {
      dispatch(reqeustSlice.actions.setLoading(false));
    }
  };

export const deleteRequestDraft =
  (draftId: number) => async (dispatch: AppDispatch) => {
    try {
      dispatch(reqeustSlice.actions.setLoading(true));
      const res = await api.delete(DELETE_REQUEST_DRAFT(draftId));
      if (res) {
        dispatch(fetchDraftRequests());
      }
    } catch (error: unknown) {
      dispatch(
        reqeustSlice.actions.setError(
          error instanceof Error ? error.message : "Error deleting draft"
        )
      );
    } finally {
      dispatch(reqeustSlice.actions.setLoading(false));
    }
  };

export const fetchDataChangeRequestDraft =
  (draftId: number) => async (dispatch: AppDispatch) => {
    try {
      dispatch(reqeustSlice.actions.setLoading(true));
      const res = await api.get(GET_REQUEST_DRAFT_DETAIL(draftId));
      dispatch(reqeustSlice.actions.setDataChangeDraft(res.data.data));
    } catch (error: unknown) {
      dispatch(
        reqeustSlice.actions.setError(
          error instanceof Error ? error.message : "Error fetching draft detail"
        )
      );
    } finally {
      dispatch(reqeustSlice.actions.setLoading(false));
    }
  };

export const clearDataChangeDraft = () => async (dispatch: AppDispatch) => {
  dispatch(reqeustSlice.actions.setDataChangeDraft(undefined));
};

export const createDataChangeRequest =
  (
    dataChange: AgentDistributorDetailsForceUpdateData,
    reason: string,
    asDraft: boolean,
    requestId?: number
  ) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(reqeustSlice.actions.setLoading(true));
      const res = await api.post(CREATE_DATA_CHANGE_REQUEST(asDraft), {
        reason: reason,
        fieldsToUpdate: dataChange,
        requestID: requestId,
      });
      if (res) {
        if (asDraft) {
          toast.success("Request Draft saved successfully");
          dispatch(fetchDraftRequests());
        } else {
          toast.success("Data Change Request sent successfully");
          dispatch(fetchSentRequests());
        }
        if (requestId) {
          dispatch(fetchDraftRequests());
        }
      }
    } catch (error: unknown) {
      dispatch(
        reqeustSlice.actions.setError(
          error instanceof Error
            ? error.message
            : "Error creating data change request"
        )
      );
    } finally {
      dispatch(reqeustSlice.actions.setLoading(false));
    }
  };

export const updateDataChangeRequest =
  (
    id: number,
    dataChange: AgentDistributorDetailsForceUpdateData,
    reason: string,
    asDraft: boolean
  ) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(reqeustSlice.actions.setLoading(true));
      const res = await api.put(UPDATE_REQUEST_DRAFT(id), {
        reason: reason,
        fieldsToUpdate: dataChange,
      });
      if (res) {
        if (asDraft) {
          toast.success("Request Draft saved successfully");
          dispatch(fetchDraftRequests());
        } else {
          toast.success("Data Change Request sent successfully");
          dispatch(fetchSentRequests());
        }
      }
    } catch (error: unknown) {
      dispatch(
        reqeustSlice.actions.setError(
          error instanceof Error
            ? error.message
            : "Error creating data change request"
        )
      );
    } finally {
      dispatch(reqeustSlice.actions.setLoading(false));
    }
  };

export const createAgentTerminationRequest =
  (agentId: string, reason: string, asDraft: boolean, requestId?: number) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(reqeustSlice.actions.setLoading(true));
      const res = await api.post(CREATE_AGENT_TERMINATION_REQUEST(asDraft), {
        agentID: agentId,
        reason: reason,
        requestID: requestId,
      });
      if (res) {
        if (asDraft) {
          toast.success("Agent Termination Draft saved successfully");
          dispatch(fetchDraftRequests());
        } else {
          toast.success("Agent Termination Request sent successfully");
          dispatch(fetchSentRequests());
          if (requestId) {
            dispatch(fetchDraftRequests());
          }
        }
      }
    } catch (error: unknown) {
      dispatch(
        reqeustSlice.actions.setError(
          error instanceof Error
            ? error.message
            : "Error creating agent termination request"
        )
      );
    } finally {
      dispatch(reqeustSlice.actions.setLoading(false));
    }
  };

export const createAgentSelfTerminationRequest =
  (reason: string, evidences: string[], asDraft: boolean, requestId?: number) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(reqeustSlice.actions.setLoading(true));
      const res = await api.post(
        CREATE_AGENT_TERMINATION_REQUEST(asDraft, true),
        {
          reason: reason,
          evidences: evidences,
          requestID: requestId,
        }
      );
      if (res) {
        if (asDraft) {
          toast.success("Termination Draft saved successfully");
          dispatch(fetchDraftRequests());
        } else {
          toast.success("Termination Request sent successfully");
          dispatch(fetchSentRequests());
          if (requestId) {
            dispatch(fetchDraftRequests());
          }
        }
      }
    } catch (error: unknown) {
      dispatch(
        reqeustSlice.actions.setError(
          error instanceof Error
            ? error.message
            : "Error creating agent termination request"
        )
      );
    } finally {
      dispatch(reqeustSlice.actions.setLoading(false));
    }
  };

export const updateAgentTerminationRequest =
  (requestId: number, agentId: string, reason: string, evidences?: string[]) =>
  async (dispatch: AppDispatch) => {
    const data = evidences
      ? {
          reason: reason,
          evidences: evidences,
          requestID: requestId,
        }
      : {
          reason: reason,
          agentID: agentId,
          requestID: requestId,
        };

    try {
      dispatch(reqeustSlice.actions.setLoading(true));
      const res = await api.put(UPDATE_REQUEST_DRAFT(requestId), data);
      if (res) {
        toast.success("Request Draft saved successfully");
        dispatch(fetchDraftRequests());
      }
    } catch (error: unknown) {
      dispatch(
        reqeustSlice.actions.setError(
          error instanceof Error
            ? error.message
            : "Error creating data change request"
        )
      );
    } finally {
      dispatch(reqeustSlice.actions.setLoading(false));
    }
  };

export const fetchAgentTerminationRequestDraft =
  (draftId: number) => async (dispatch: AppDispatch) => {
    try {
      dispatch(reqeustSlice.actions.setLoading(true));
      const res = await api.get(GET_REQUEST_DRAFT_DETAIL(draftId));
      dispatch(reqeustSlice.actions.setAgentTerminationDraft(res.data.data));
    } catch (error: unknown) {
      dispatch(
        reqeustSlice.actions.setError(
          error instanceof Error ? error.message : "Error fetching draft detail"
        )
      );
    } finally {
      dispatch(reqeustSlice.actions.setLoading(false));
    }
  };

export default reqeustSlice.reducer;

export const clearAgentTerminationDraft =
  () => async (dispatch: AppDispatch) => {
    dispatch(reqeustSlice.actions.setAgentTerminationDraft(undefined));
  };
