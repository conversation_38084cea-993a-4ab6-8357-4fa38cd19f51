import Icon from "@/components/common/Icon";
import useBreakpoints from "@/hooks/useBreakpoints";
import {
  formatDateToString,
  parseTimeToBahasa,
} from "@/utils/common-functions";
import { Flex, Spoiler, Text } from "@mantine/core";

interface DistributorTerminationNotificationCardContentProps {
  reason?: string;
  date: string;
  daysRemaining: string;
}

const DistributorTerminationNotificationCardContentProps = ({
  reason = "-",
  date,
  daysRemaining,
}: DistributorTerminationNotificationCardContentProps) => {
  const { tablet, mobile } = useBreakpoints();

  return (
    <Flex
      direction={mobile ? "column" : "row"}
      justify={"space-between"}
      gap={mobile ? "xs" : "md"}
      p={"md"}
    >
      <Flex gap={"md"} justify={"flex-start"} align={"flex-start"} w={"100%"}>
        <Icon icon={`broadcast/request`} size={20} />
        <Flex direction={"column"} w={"100%"}>
          <Text fw={500} maw={"95%"}>
            {`Anda diberhentikan sebagai Distributor – Selesaikan Bisnis Anda dalam ${parseTimeToBahasa(
              daysRemaining
            )}`}
          </Text>
          <Spoiler
            maxHeight={42}
            showLabel="See all"
            hideLabel="See less"
            pb={"md"}
            variant="subtle"
          >
            <Text size="sm" c={"text-light"} w={"100%"} maw={"95%"}>
              {`Alasan: ${reason}`}
            </Text>
          </Spoiler>
        </Flex>
      </Flex>

      <Flex
        direction={"column"}
        ta={mobile ? "left" : "right"}
        align={mobile ? "flex-start" : "flex-end"}
      >
        <Text fw={300} c={"text-light"} size="sm">
          Sent
        </Text>
        <Text fw={500} c={"text-light"} size="sm">
          {formatDateToString(date, "number")}
        </Text>
      </Flex>
    </Flex>
  );
};

export default DistributorTerminationNotificationCardContentProps;
