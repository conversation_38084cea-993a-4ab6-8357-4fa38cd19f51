import {
  CreateOrderPayload,
  DetailInfoFormValues,
  DistributorOrderItem,
  PersonalInfoFormValues,
  ProductListItem,
  ProductListResponse,
  RegisterData,
  RegisterPayload,
  RegisterResponse,
  ShippingOptionsItem,
  ShippingOptionsResponse,
  ValidDeliveryOptions,
  VerificationInfoFormValues,
} from "@/utils/types/distributor-register";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { AppDispatch, RootState } from "..";
import api from "@/lib/axios";
import {
  CREATE_FIRST_ORDERS,
  DISTRIBUTOR_REGISTER,
  DISTRIBUTOR_SUBMIT_NEW_SK,
  DISTRIBUTOR_SUBMIT_PAYMENT_PROOF,
  GET_FIRST_ORDERS,
  GET_PRODUCT_LIST,
  GET_SHIPPING_OPTIONS,
} from "@/utils/api-routes";
import {
  distributorRegisterSchema,
  isEmpty,
  parseRegion,
} from "@/utils/common-functions";
import * as yup from "yup";
import axios, { AxiosResponse } from "axios";
import { ApiResponse } from "@/utils/types";
import api2 from "@/lib/ecommerce";

export interface DistributorRegisterState {
  personalInfo: PersonalInfoFormValues;
  detailInfo: DetailInfoFormValues;
  verificationInfo: VerificationInfoFormValues;
  sk: string;
  paymentProofs: {
    orderId: number;
    paymentProof: string;
  }[];
  productList: ProductListItem[];
  shippingOptionList: ShippingOptionsItem[];
  orderList: DistributorOrderItem[];
  order: {
    products: { id: string }[];
    shippingOption: string;
    shippingCost: number;
  };
  isLoading: boolean;
  status?: "waitlisted" | "considered";
}

const initialState: DistributorRegisterState = {
  personalInfo: {
    name: "",
    nik: "",
    npwp: "",
    email: "",
    phoneNumber: "",
    island: "",
    province: "",
    regency: "",
    district: "",
    village: "",
    postalCode: "",
    address: "",
  },
  detailInfo: {
    warehouseIsland: "",
    warehouseProvince: "",
    warehouseRegency: "",
    warehouseDistrict: "",
    warehouseVillage: "",
    warehousePostalCode: "",
    warehouseAddress: "",
    adminPhoneNumber: "",
    tokopedia: "",
    shopee: "",
    tiktok: "",
  },
  verificationInfo: {
    idCardImage: "",
    selfieImage: "",
  },
  sk: "",
  paymentProofs: [],
  productList: [],
  shippingOptionList: [],
  orderList: [],
  order: {
    products: [],
    shippingOption: "",
    shippingCost: -1, // -1 mean TBA
  },
  isLoading: false,
  status: undefined,
};

const distributorRegisterSlice = createSlice({
  name: "distributorRegister",
  initialState,
  reducers: {
    setLoading: (
      state,
      action: PayloadAction<DistributorRegisterState["isLoading"]>
    ) => {
      state.isLoading = action.payload;
    },
    setStatus: (
      state,
      action: PayloadAction<DistributorRegisterState["status"]>
    ) => {
      state.status = action.payload;
      toast.info("Pendaftaranmu sedang diproses!");
    },
    setPersonalInfo: (
      state,
      action: PayloadAction<DistributorRegisterState["personalInfo"]>
    ) => {
      state.personalInfo = action.payload;
    },
    setDetailInfo: (
      state,
      action: PayloadAction<DistributorRegisterState["detailInfo"]>
    ) => {
      state.detailInfo = action.payload;
    },
    setVerificationInfo: (
      state,
      action: PayloadAction<DistributorRegisterState["verificationInfo"]>
    ) => {
      state.verificationInfo = action.payload;
    },
    setNewSk: (
      state,
      action: PayloadAction<DistributorRegisterState["sk"]>
    ) => {
      state.sk = action.payload;
    },
    setProductList: (
      state,
      action: PayloadAction<DistributorRegisterState["productList"]>
    ) => {
      state.productList = action.payload;
    },
    setShippingOptionList: (
      state,
      action: PayloadAction<DistributorRegisterState["shippingOptionList"]>
    ) => {
      state.shippingOptionList = action.payload;
    },
    setOrderList: (
      state,
      action: PayloadAction<DistributorRegisterState["orderList"]>
    ) => {
      state.orderList = action.payload;
    },
    setOrderProducts: (
      state,
      action: PayloadAction<DistributorRegisterState["order"]["products"]>
    ) => {
      state.order.products = action.payload
        .filter((product) => !isEmpty(product.id))
        .map((product) => ({ id: product.id }));
    },
    setOrderShippingOption: (
      state,
      action: PayloadAction<DistributorRegisterState["order"]["shippingOption"]>
    ) => {
      // const deliveryCost = Number(process.env.NEXT_PUBLIC_DELIVERY_COST || 0);
      // const validDeliveryOptions: ValidDeliveryOptions[] = [
      //   "Truck Hotto",
      //   "J&T Cargo",
      //   "Central Cargo",
      // ];

      const calculateShippingCost = (): number => {
        // No shipping option selected
        if (!action.payload) return -1;

        // Free shipping for single product orders
        if (state.order.products.length === 1) return 0;

        // Check if second product is 120 Pouch for free shipping
        const secondProduct = state.productList.find(
          ({ id }) => id.toString() === state.order.products[1]?.id.toString()
        );
        if (secondProduct?.name.includes("120 Pouch")) return 0;

        // Apply delivery cost for valid shipping options (sementara dibikin TBA dulu)
        return -1;
        // return validDeliveryOptions.includes(
        //   action.payload as ValidDeliveryOptions
        // )
        //   ? deliveryCost
        //   : -1;
      };

      state.order.shippingOption = action.payload;
      state.order.shippingCost = calculateShippingCost();
    },
    setPaymentProof: (
      state,
      action: PayloadAction<{
        orderId: number;
        paymentProof: string;
      }>
    ) => {
      if (isEmpty(action.payload.paymentProof)) {
        state.paymentProofs = state.paymentProofs.filter(
          (proof) => proof.orderId !== action.payload.orderId
        );
        return;
      }

      const existingIndex = state.paymentProofs.findIndex(
        (proof) => proof.orderId === action.payload.orderId
      );
      if (existingIndex !== -1) {
        state.paymentProofs[existingIndex] = action.payload;
      } else {
        state.paymentProofs.push(action.payload);
      }
    },
  },
});

const act = distributorRegisterSlice.actions;

export const resetInfo = () => async (dispatch: AppDispatch) => {
  dispatch(act.setPersonalInfo(initialState.personalInfo));
  dispatch(act.setDetailInfo(initialState.detailInfo));
  dispatch(act.setVerificationInfo(initialState.verificationInfo));
};

export const submitRegister =
  () => async (dispatch: AppDispatch, getState: () => RootState) => {
    const state = getState().distributorRegister;
    const data = {
      ...state.personalInfo,
      ...state.detailInfo,
      ...state.verificationInfo,
    };
    try {
      dispatch(act.setLoading(true));
      await distributorRegisterSchema.validate(data, {
        abortEarly: false,
      });
      const dataToSubmit: RegisterPayload = {
        name: data.name,
        nik: data.nik,
        npwp: data.npwp,
        email: data.email,
        phoneNumber: data.phoneNumber,
        island: data.island,
        province: parseRegion(data.province).name,
        city: parseRegion(data.regency).name,
        district: parseRegion(data.district).name,
        kelurahan: parseRegion(data.village).name,
        postalCode: data.postalCode,
        address: data.address,
        warehouseIsland: data.warehouseIsland,
        warehouseProvince: parseRegion(data.warehouseProvince).name,
        warehouseCity: parseRegion(data.warehouseRegency).name,
        warehouseDistrict: parseRegion(data.warehouseDistrict).name,
        warehouseKelurahan: parseRegion(data.warehouseVillage).name,
        warehousePostalCode: data.warehousePostalCode,
        warehouseAddress: data.warehouseAddress,
        adminPhoneNumber: data.adminPhoneNumber,
        ktp: data.idCardImage,
        selfieKtp: data.selfieImage,
        shops: {
          tokopedia: data.tokopedia,
          shopee: data.shopee,
          tiktok: data.tiktok,
        },
      };
      const res: AxiosResponse<RegisterResponse> = await api.post(
        DISTRIBUTOR_REGISTER,
        dataToSubmit
      );
      dispatch(resetInfo());
      dispatch(
        act.setStatus(res.data.data.isWaitlisted ? "waitlisted" : "considered")
      );
      return Promise.resolve(res.data.data);
    } catch (error: unknown) {
      if (error instanceof yup.ValidationError) {
        toast.error(`Validasi gagal: ${error.errors.join(", ")}`);
      } else {
        toast.error(
          "Terjadi kesalahan saat mendaftar, silahkan coba beberapa saat lagi."
        );
      }
      return Promise.reject(error);
    } finally {
      dispatch(act.setLoading(false));
    }
  };

export const submitNewSk =
  (token: string) =>
  async (dispatch: AppDispatch, getState: () => RootState) => {
    const data = getState().distributorRegister.sk;
    try {
      if (!token) throw new Error("Token is required to submit new SK");
      dispatch(act.setLoading(true));
      await yup.string().required().validate(data);
      await axios.put(
        DISTRIBUTOR_SUBMIT_NEW_SK,
        { sk: data },
        {
          baseURL: `${process.env.NEXT_PUBLIC_API_BASE_URL}/api`,
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return Promise.resolve(data);
    } catch (error: unknown) {
      if (error instanceof yup.ValidationError) {
        toast.error(`Validasi gagal: ${error.errors.join(", ")}`);
      } else {
        toast.error(
          "Terjadi kesalahan saat mengupload sk, silahkan coba beberapa saat lagi."
        );
      }
      return Promise.reject(error);
    } finally {
      dispatch(act.setLoading(false));
    }
  };

export const submitPaymentProof =
  (token: string) =>
  async (
    dispatch: AppDispatch,
    getState: () => RootState
  ): Promise<boolean> => {
    try {
      if (!token) throw new Error("Token is required to submit payment proof");
      dispatch(act.setLoading(true));
      const data = getState().distributorRegister.paymentProofs;
      await axios.put(DISTRIBUTOR_SUBMIT_PAYMENT_PROOF, data, {
        baseURL: `${process.env.NEXT_PUBLIC_API_BASE_URL}/api`,
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return true;
    } catch {
      toast.error(
        "Terjadi kesalahan saat mengupload bukti pembayaran, silahkan coba beberapa saat lagi."
      );
      return false;
    } finally {
      dispatch(act.setLoading(false));
    }
  };

export const fetchProductList =
  (type: 1 | 2) => async (dispatch: AppDispatch) => {
    try {
      dispatch(act.setLoading(true));
      const res: AxiosResponse<ProductListResponse> = await api.get(
        GET_PRODUCT_LIST(type)
      );
      if (!isEmpty(res.data.data)) {
        dispatch(act.setProductList(res.data.data));
        return res.data.data;
      }
    } catch {
      toast.error(
        "Terjadi kesalahan saat mengambil data produk, silahkan coba beberapa saat lagi."
      );
    } finally {
      dispatch(act.setLoading(false));
    }
  };

export const fetchShippingOptions =
  (user: RegisterData) => async (dispatch: AppDispatch) => {
    try {
      dispatch(act.setLoading(true));
      const data = {
        distributorSlug: "",
        destProvince: user.warehouseProvince,
        destCity: user.warehouseCity,
        destDistrict: user.warehouseDistrict,
        destSubdistrict: user.warehouseKelurahan,
        destPostalCode: user.warehousePostalCode,
        couriers: "sentral:jnt",
        productWeight: 55, // NOTE: untuk sementara, asumsikan untuk 120 pouch (480g * 120)
      };
      const res: AxiosResponse<ShippingOptionsResponse> = await api2.post(
        GET_SHIPPING_OPTIONS,
        data
      );
      if (!isEmpty(res.data.data)) {
        dispatch(act.setShippingOptionList(res.data.data.shippingOptions));
      }
    } catch (error) {
      toast.error(
        "Terjadi kesalahan saat mengambil data opsi pengiriman, silahkan coba beberapa saat lagi."
      );
    } finally {
      dispatch(act.setLoading(false));
    }
  };

export const createOrder =
  (userId: string) =>
  async (dispatch: AppDispatch, getState: () => RootState) => {
    try {
      if (!userId) throw new Error();
      dispatch(act.setLoading(true));
      const order = getState().distributorRegister.order;
      const data: CreateOrderPayload = {
        userId,
        courier: order.shippingOption,
        shippingCost: Math.max(0, order.shippingCost),
        products: order.products.map((product) => Number(product.id)),
      };
      const res: AxiosResponse<ApiResponse<null>> = await api.post(
        CREATE_FIRST_ORDERS,
        data
      );
      if (res.data.status_code === 200 || res.data.status_code === 201) {
        toast.success("Order created successfully");
        return true;
      }
      throw new Error();
    } catch {
      toast.error(
        "Terjadi kesalahan saat membuat order, silahkan coba beberapa saat lagi."
      );
      return false;
    } finally {
      dispatch(act.setLoading(false));
    }
  };

export const fetchOrders = (uoc: string) => async (dispatch: AppDispatch) => {
  try {
    dispatch(act.setLoading(true));
    const res: AxiosResponse<ApiResponse<DistributorOrderItem[]>> =
      await api.get(GET_FIRST_ORDERS(uoc));
    if (res.data.data) {
      dispatch(act.setOrderList(res.data.data));
      return true;
    }
    throw new Error();
  } catch {
    toast.error(
      "Terjadi kesalahan saat mengambil data order, silahkan coba beberapa saat lagi."
    );
    return false;
  } finally {
    dispatch(act.setLoading(false));
  }
};

export const {
  setPersonalInfo,
  setDetailInfo,
  setVerificationInfo,
  setNewSk,
  setPaymentProof,
  setOrderProducts,
  setOrderShippingOption,
} = distributorRegisterSlice.actions;

export default distributorRegisterSlice.reducer;
