import { useEffect, useState } from "react";
import { FormStepperProps } from "..";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { agentRegisterSchema, generateUUID } from "@/utils/common-functions";
import { Checkbox, Input, SimpleGrid, Stack, Text } from "@mantine/core";
import UploadedFileRow from "@/components/common/UploadedFileRow";
import classes from "../../Forms/UserAgreement/style.module.css";
import { saveVerificationInfo } from "@/store/slices/agentRegisterSlice";
import FileUploadInput from "@/components/common/FileUploadInput";

const schema = agentRegisterSchema.pick(["idCardImage", "selfieImage"]);

const AgentVerificationInfoForm: React.FC<FormStepperProps> = ({
  onFormValid,
}) => {
  const id = generateUUID();
  const [checked, setChecked] = useState(false);

  const dispatch: AppDispatch = useDispatch();
  const { verificationInfo } = useSelector(
    (state: RootState) => state.agentRegister
  );

  const form = useForm({
    mode: "onChange",
    resolver: yupResolver(schema),
    defaultValues: verificationInfo,
  });

  useEffect(() => {
    onFormValid(form.formState.isValid && checked);
    if (!form.formState.isValid) {
      setChecked(false);
    }
  }, [form.formState.isValid, checked]);

  useEffect(() => {
    const subscription = form.watch(() => {
      dispatch(saveVerificationInfo(form.getValues()));
    });
    return () => subscription.unsubscribe();
  }, []);

  return (
    <Stack>
      <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="xl">
        <FileUploadInput
          required
          label="Upload file KTP di sini"
          error={form.formState.errors.idCardImage?.message}
          prefix={`foto-ktp-${id}`}
          fileType="images"
          onChange={(url) => {
            form.setValue("idCardImage", url);
            form.trigger("idCardImage");
          }}
        />
        <Input.Wrapper label="Uploaded file">
          {form.watch("idCardImage") ? (
            <UploadedFileRow
              fileUrl={form.watch("idCardImage")}
              onDelete={() => {
                form.setValue("idCardImage", "");
                form.trigger("idCardImage");
              }}
            />
          ) : (
            <Text c="text">Belum ada file diupload!</Text>
          )}
        </Input.Wrapper>
      </SimpleGrid>

      <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="xl">
        <FileUploadInput
          required
          label="Upload file selfie Anda bersama KTP di sini"
          error={form.formState.errors.selfieImage?.message}
          prefix={`foto-selfie-${id}`}
          fileType="images"
          onChange={(url) => {
            form.setValue("selfieImage", url);
            form.trigger("selfieImage");
          }}
        />
        <Input.Wrapper label="Uploaded file">
          {form.watch("selfieImage") ? (
            <UploadedFileRow
              fileUrl={form.watch("selfieImage")}
              onDelete={() => {
                form.setValue("selfieImage", "");
                form.trigger("selfieImage");
              }}
            />
          ) : (
            <Text c="text">Belum ada file diupload!</Text>
          )}
        </Input.Wrapper>
      </SimpleGrid>

      <Checkbox
        classNames={classes}
        checked={checked}
        disabled={!form.formState.isValid}
        onChange={(target) => setChecked(target.currentTarget.checked)}
        label="Saya dapat mempertanggungjawabkan KTP ini adalah asli dan benar."
        description="Saya memahami bahwa pelanggaran terhadap syarat & ketentuan ini dapat membuat NIK saya diblacklist dari aplikasi agent Hotto ke depannya."
      />
    </Stack>
  );
};

export default AgentVerificationInfoForm;
