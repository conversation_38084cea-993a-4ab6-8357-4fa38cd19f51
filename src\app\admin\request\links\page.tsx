"use client";

import IslandSelections from "@/components/common/IslandSelections";
import PageLayout from "@/components/common/PageLayout";
import MarketplaceLinks from "@/components/section/AdminRequests/MarketplaceLinks";
import { withAuth } from "@/utils/withAuth";

const AdminRequestPage = () => {
  return (
    <PageLayout
      title="Request"
      navigationContent={{
        title: "Periksa Link Marketplace",
      }}
    >
      <IslandSelections withStatus />
      <MarketplaceLinks />
    </PageLayout>
  );
};

export default withAuth(AdminRequestPage);
