import {
  PiBasket,
  PiBasketFill,
  PiTruckFill,
  PiPencilSimpleFill,
  PiShieldCheckFill,
  PiFileArrowUpFill,
  PiTrashFill,
  PiMapPinAreaFill,
  PiCaretLeftFill,
  PiCaretRightFill,
  PiCaretUpFill,
  PiCaretDownFill,
  PiPhoneFill,
  PiSealCheckFill,
  PiFileFill,
  PiStarFill,
  PiCheckCircleFill,
  PiChatDots,
  PiMapPinFill,
  PiAddressBook,
  PiAddressBookFill,
  PiMegaphone,
  PiMegaphoneFill,
  PiSignOutFill,
  PiBell,
  PiBellFill,
  PiUploadSimple,
  PiWarningOctagonFill,
  PiWhatsappLogoFill,
  PiDownloadSimpleBold,
} from "react-icons/pi";
import {
  TbWorld,
  TbMail,
  TbSearch,
  TbRosetteDiscountFilled,
} from "react-icons/tb";
import { FaPlus, FaMinus, FaCircle, FaRupiahSign } from "react-icons/fa6";
import { <PERSON><PERSON><PERSON><PERSON> } from "react-icons/si";
import { IoClose } from "react-icons/io5";
import { GoDiff } from "react-icons/go";
import { LuArrowUpDown } from "react-icons/lu";
import { BsInfoCircle, BsTicketFill, BsTicket } from "react-icons/bs";
import { AiFillTikTok } from "react-icons/ai";

export {
  PiPhoneFill as Phone,
  PiTrashFill as Delete,
  PiPencilSimpleFill as Edit,
  PiBasket as Cart,
  PiBasketFill as CartFilled,
  PiTruckFill as Delivery,
  PiShieldCheckFill as Verified,
  PiFileArrowUpFill as FileUpload,
  PiMapPinAreaFill as Area,
  PiCaretUpFill as CaretUp,
  PiCaretDownFill as CaretDown,
  PiCaretRightFill as CaretRight,
  PiCaretLeftFill as CaretLeft,
  PiSealCheckFill as CheckBadge,
  PiFileFill as File,
  PiStarFill as Star,
  PiCheckCircleFill as Success,
  PiChatDots as Chat,
  PiMapPinFill as Location,
  PiUploadSimple as Upload,
  PiWhatsappLogoFill as Whatsapp,
  TbWorld as Globe,
  TbMail as Email,
  TbSearch as Search,
  TbRosetteDiscountFilled as Discount,
  SiShopee as Shopee,
  IoClose as Close,
  GoDiff as Diff,
  FaPlus as Plus,
  FaMinus as Minus,
  BsInfoCircle as Info,
  LuArrowUpDown as UpDown,
  AiFillTikTok as Tiktok,
  FaCircle as Dot,
  PiBell as Bell,
  PiBellFill as BellFilled,
  BsTicketFill as TicketFilled,
  BsTicket as Ticket,
  PiAddressBook as AddressBook,
  PiAddressBookFill as AddressBookFilled,
  PiMegaphone as Megaphone,
  PiMegaphoneFill as MegaphoneFilled,
  PiSignOutFill as Logout,
  PiWarningOctagonFill as Warning,
  FaRupiahSign as Rupiah,
  PiDownloadSimpleBold as Download,
};
