"use client";

import {
  But<PERSON>,
  Checkbox,
  createTheme,
  <PERSON>er,
  NativeSelect,
  NumberFormatter,
  ScrollArea,
  Select,
  Stepper,
  Image,
} from "@mantine/core";
import { generateColors } from "@mantine/colors-generator";
import { CaretDown } from "@/utils/icons";
import StepperClasses from "@/styles/stepper.module.css";
import { PLACEHOLDER_IMAGE } from "@/utils/constants";

export const theme = createTheme({
  components: {
    Select: Select.extend({
      defaultProps: {
        rightSection: CaretDown({
          size: 12,
          color: "var(--mantine-color-primary-filled)",
        }),
        allowDeselect: false,
        comboboxProps: {
          offset: 0,
          transitionProps: { transition: "scale-y", duration: 200 },
          middlewares: { flip: false, shift: false },
        },
      },
    }),
    NativeSelect: NativeSelect.extend({
      defaultProps: {
        rightSection: CaretDown({
          size: 12,
          color: "var(--mantine-color-primary-filled)",
        }),
      },
    }),
    ScrollArea: ScrollArea.extend({
      defaultProps: {
        scrollbarSize: 4,
        scrollHideDelay: 0,
        offsetScrollbars: true,
      },
    }),
    Checkbox: Checkbox.extend({
      defaultProps: {
        radius: "sm",
      },
    }),
    NumberFormatter: NumberFormatter.extend({
      defaultProps: {
        prefix: "Rp",
        thousandSeparator: ".",
        decimalSeparator: ",",
      },
    }),
    Drawer: Drawer.extend({
      defaultProps: {
        styles: {
          content: {
            height: "auto",
          },
        },
      },
    }),
    Button: Button.extend({
      defaultProps: {
        loaderProps: {
          type: "dots",
        },
      },
    }),
    Stepper: Stepper.extend({
      defaultProps: {
        classNames: StepperClasses,
      },
    }),
    Image: Image.extend({
      defaultProps: {
        fallbackSrc: PLACEHOLDER_IMAGE,
      },
    }),
  },
  fontFamily: "Larsseit, Helvetica, Arial, sans-serif",
  headings: {
    fontFamily: "Recoleta, Larsseit, Helvetica, Arial, sans-serif",
  },
  black: "#423E6A",
  white: "#FFFFFF",
  primaryColor: "primary",
  defaultRadius: "md",
  cursorType: "pointer",
  spacing: {
    // 8px
    xs: "0.5rem",
    // 12px
    sm: "0.75rem",
    // 16px
    md: "1rem",
    // 20px
    lg: "1.25rem",
    // 24px
    xl: "1.5rem",
    // 32px
    xl2: "2rem",
    // 64px
    xl3: "2.25rem",
  },
  colors: {
    // dynamic colors
    primary: generateColors("#5b539c"),
    secondary: generateColors("#808e54"),
    text: generateColors("#423E6A"),
    "text-light": generateColors("#8988A0"),
    "text-black": generateColors("#464646"),
    success: generateColors("#49a45d"),
    warning: generateColors("#f4bc47"),
    error: generateColors("#d24b4b"),

    // static colors
    bg: [
      "#F4F5FA",
      "#F4F5FA",
      "#F4F5FA",
      "#F4F5FA",
      "#F4F5FA",
      "#F4F5FA",
      "#F4F5FA",
      "#F4F5FA",
      "#F4F5FA",
      "#F4F5FA",
    ],
    "bg-gray": [
      "#E5E5F4",
      "#E5E5F4",
      "#E5E5F4",
      "#E5E5F4",
      "#E5E5F4",
      "#E5E5F4",
      "#E5E5F4",
      "#E5E5F4",
      "#E5E5F4",
      "#E5E5F4",
    ],
  },
});
