import { Anchor, Flex, Paper, Text } from "@mantine/core";
import Link from "next/link";

interface AgentCardSectionProps {
  top?: string;
  bottom?: string;
  href: string;
}
const AgentCardSection = ({ top, bottom, href }: AgentCardSectionProps) => {
  return (
    <Paper bg={"bg-gray"} maw={"100%"} miw={"100%"} px={"xl"} py={"md"}>
      <Flex
        direction={"row"}
        gap={"xs"}
        justify={"space-between"}
        align={"center"}
      >
        <Flex direction={"column"} gap={"4px"}>
          {top && <Text fw={500}>{top}</Text>}{" "}
          {bottom && (
            <Text c={"text-light"} fw={500}>
              {bottom}
            </Text>
          )}
        </Flex>
        <Anchor component={Link} href={href} fw={500}>
          View Detail
        </Anchor>
      </Flex>
    </Paper>
  );
};

export default AgentCardSection;
