import { BroadcastType } from "./broadcast";
import { ComposeRequestType } from "./request";

export interface ApiResponse<T = void> {
  data: T extends void ? never : T;
  message: string;
  status_code: number;
}

export interface Segment<T> {
  params: Promise<T>;
}

/**
 * ProvinceAgents is an interface that represents the agents in a province.
 * It contains the name of the province, the count of the agents, and an array of the agent details.
 * The agent details will be an array of objects with the following properties:
 * - uoc: string
 * - name: string
 * - status: string
 * - address: string
 * - orderSummary: AgentCardOrderDetail
 * - countAgent: AgentCardCountAgent
 */
export interface ProvinceAgents {
  name: string;
  countAgent: number;
  agents: AgentCardDataDetailed[];
}

/**
 * AgentCardDataDetailed represent the agent's data for when getting by Island/ City
 */
export interface AgentCardDataDetailed {
  uoc: string;
  name: string;
  status: 0 | 1 | 2; //0: Active 1: Waitlist: 2: Deactivated
  address: string;
  orderSummary: AgentCardOrderDetail;
  countAgent?: AgentCardCountAgent;
}

export interface AgentCardOrderDetail {
  totalOrder: number;
  lastOrder: string;
}

export interface AgentCardCountAgent {
  count: number;
  lastAddedAgent: string;
}

/**
 * AgentCardDataSimple represent the agent's data for when getting by Name/Phone
 */
export interface AgentCardDataSimple {
  uoc: string;
  name: string;
  email?: string;
  status: 0 | 1 | 2; //0: Active 1: Waitlist: 2: Deactivated
  ownerPhone: string;
  adminPhone: string;
}

export interface AgentOptionResponse {
  uoc: string;
  name: string;
}

export interface Draft {
  id: number;
  title: string;
  createdAt: string;
  type?: ComposeRequestType | BroadcastType;
}

export interface IslandsWithStatusResponse {
  status: number;
  message: string;
  data: IslandsWithStatusData[];
}

export interface IslandsWithStatusData {
  island: string;
  status: boolean;
}

export enum DraftType {
  Broadcast = "Broadcast",
  Request = "Request",
}
