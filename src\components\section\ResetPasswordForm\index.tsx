"use client";

import ResponsiveModal from "@/components/common/ResponsiveModal";
import { AppDispatch, RootState } from "@/store";
import { resetPassword } from "@/store/slices/authSlice";
import { passwordValidationReset } from "@/utils/common-functions";
import { ONBOARDING_VAR } from "@/utils/constants";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  PasswordInput,
  Stack,
  Box,
  Text,
  Title,
  Paper,
  Button,
  Flex,
  List,
} from "@mantine/core";
import { useDisclosure, useLocalStorage } from "@mantine/hooks";
import { parseAsBoolean, useQueryState } from "nuqs";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import * as yup from "yup";

export const resetPasswordSchema = yup.object().shape({
  password: passwordValidationReset,
  confirm: yup
    .string()
    .oneOf([yup.ref("password")], "Konfirmasi password harus sama")
    .required("Konfirmasi password harus diisi"),
});

const ResetPasswordForm: React.FC = () => {
  const dispatch: AppDispatch = useDispatch();
  const { isLoading, role, accessToken } = useSelector(
    (state: RootState) => state.auth
  );

  const [storageOnboarding, setStorageOnboarding] = useLocalStorage({
    key: ONBOARDING_VAR,
    defaultValue: false,
  });
  const [onboarding, setOnboarding] = useQueryState(
    "onboarding",
    parseAsBoolean.withDefault(storageOnboarding)
  );
  const [opened, handlers] = useDisclosure(false);

  useEffect(() => {
    if (storageOnboarding && accessToken && role) {
      handlers.open();
    } else if (onboarding && !storageOnboarding) {
      setStorageOnboarding(true);
    }
  }, [storageOnboarding, onboarding, accessToken, role]);

  const form = useForm({
    mode: "onChange",
    resolver: yupResolver(resetPasswordSchema),
    defaultValues: { password: "", confirm: "" },
  });

  const handleSubmit = (data: yup.InferType<typeof resetPasswordSchema>) => {
    if (!isLoading && accessToken && role) {
      dispatch(resetPassword(data)).then(({ type }) => {
        if (type.includes("fulfilled")) {
          form.reset();
          setOnboarding(false);
          setStorageOnboarding(false);
          handlers.close();
        }
      });
    }
  };

  return (
    <ResponsiveModal
      opened={opened}
      onClose={() => {}}
      withCloseButton={false}
      closeOnClickOutside={false}
      closeOnEscape={false}
      modalProps={{ padding: "lg" }}
      drawerProps={{ padding: "lg" }}
    >
      <Stack
        component="form"
        onSubmit={form.handleSubmit((data) => handleSubmit(data))}
      >
        <Box ta="center" my="lg">
          <Title order={2}>Reset Password</Title>
          <Text c="text">Silahkan ganti passwordmu si sini!</Text>
        </Box>
        <PasswordInput
          required
          label="Password Baru"
          {...form.register("password")}
          value={form.watch("password")}
          error={form.formState.errors.password?.message}
        />
        <PasswordInput
          required
          label="Konfirmasi Password"
          {...form.register("confirm")}
          value={form.watch("confirm")}
          error={form.formState.errors.confirm?.message}
        />
        <Box c="text">
          <Text fz="sm">Password harus terdiri atas:</Text>
          <List withPadding size="sm" mr="sm">
            <List.Item>Minimal 8 karakter.</List.Item>
            <List.Item>
              Terdiri atas kombinasi huruf besar, huruf kecil, angka, dan
              karakter spesial.
            </List.Item>
          </List>
        </Box>
        <Paper mx="-lg" bg="error.1" px="lg" py="sm" radius="8px 8px 0 0">
          <Text c="error" fw={500} fz="0.9rem">
            Pastikan password ini bersifat rahasia!
          </Text>
        </Paper>
        <Flex justify="center">
          <Button
            type="submit"
            disabled={!form.formState.isValid}
            loading={isLoading}
          >
            Ganti Password
          </Button>
        </Flex>
      </Stack>
    </ResponsiveModal>
  );
};

export default ResetPasswordForm;
