import Image from "next/image";
import styled from "styled-components";

interface IconButtonProps {
  icon: string;
  onClick: () => void;
  size?: number;
}

const IconButton = ({ icon, onClick, size = 24 }: IconButtonProps) => {
  return (
    <IconButtonWrapper type="button" onClick={onClick} className="icon-button">
      <Image
        width={size}
        height={size}
        src={`/icons/${icon}.svg`}
        alt={`${icon}`}
      />
    </IconButtonWrapper>
  );
};

export default IconButton;

const IconButtonWrapper = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  height: fit-content;
  width: fit-content;
  cursor: pointer;

  background: none;
  border: none;
  transition: ease-in-out 0.25s;

  &:hover {
    filter: brightness(95%);
  }
`;
