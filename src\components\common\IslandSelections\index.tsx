"use client";

import { AppDispatch, RootState } from "@/store";
import { <PERSON><PERSON>, <PERSON>bsList, TabsTab } from "@mantine/core";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import classes from "./style.module.css";
import Check from "@/components/icons/Check";
import { fetchIslandsWithStatus } from "@/store/slices/adminMarketplaceLinksSlice";
import { parseAsString, useQueryState } from "nuqs";

export interface IslandSelectionsProps {
  withStatus?: boolean;
  disabled?: boolean;
}

const IslandSelections: React.FC<IslandSelectionsProps> = ({
  withStatus,
  disabled,
}) => {
  const dispatch: AppDispatch = useDispatch();
  const { islands, loading } = useSelector(
    (state: RootState) => state.adminMarketplaceLinks
  );

  const [islandQuery, setIslandQuery] = useQueryState("island", parseAsString);

  useEffect(() => {
    if (!islands || islands.length === 0) {
      dispatch(fetchIslandsWithStatus());
    } else {
      if (!islandQuery) {
        setIslandQuery(islands[0].island);
      }
    }
  }, [islands, dispatch]);

  const notEmptyIslands = islands && islands.length > 0;

  return (
    <Tabs
      value={islandQuery}
      defaultValue={islandQuery}
      classNames={classes}
      onChange={setIslandQuery}
    >
      <TabsList>
        {notEmptyIslands &&
          islands.map(({ island, status }, index) => (
            <TabsTab
              key={`${island}-${index}`}
              value={island}
              title={island}
              disabled={loading || disabled}
              rightSection={withStatus && !status && <Check fontSize={24} />}
            >
              {island}
            </TabsTab>
          ))}
      </TabsList>
    </Tabs>
  );
};

export default IslandSelections;
