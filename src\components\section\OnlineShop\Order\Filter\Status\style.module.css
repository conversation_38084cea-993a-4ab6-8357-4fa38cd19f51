.root {
  margin-left: calc(var(--mantine-spacing-lg) * -1);
  margin-right: calc(var(--mantine-spacing-lg) * -1);
}

.list {
  flex-wrap: nowrap;
  overflow-x: scroll;
  padding-top: 5px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom: 2px solid var(--mantine-color-bg-filled);

  &::-webkit-scrollbar {
    display: none;
  }

  &::before {
    border: none;
  }
}

.tab {
  padding: var(--mantine-spacing-md) 3rem;
  transition-property: background-color, color;
  transition-duration: 0.2s;
  transition-timing-function: ease;

  &[data-active] {
    color: var(--mantine-primary-color-filled);
    border-bottom: 3px solid var(--mantine-primary-color-filled);
  }
}

.tabLabel {
  font-size: var(--mantine-font-size-lg);
  font-weight: 500;
}
