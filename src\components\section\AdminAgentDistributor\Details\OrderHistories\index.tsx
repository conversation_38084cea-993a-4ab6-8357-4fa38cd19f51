import { OrderHistoryData } from "@/utils/types/distributor-agent";
import {
  Box,
  Divider,
  Flex,
  Group,
  NumberFormatter,
  Stack,
  Text,
  Button,
} from "@mantine/core";
import OrderHistory from "../../OrderHistory";
import TotalProduct from "../../TotalProduct";
import Link from "next/link";

export interface OrderHistoriesProps {
  data: OrderHistoryData;
  uoc: string;
}

const OrderHistories: React.FC<OrderHistoriesProps> = ({ data, uoc }) => {
  const histories = data?.histories;
  const total = histories?.reduce((total, item) => total + item.nominal, 0);

  return (
    <Stack gap="xs">
      <Group justify="space-between" align="center">
        <Box>
          <Text size="xl" fw={600}>
            Order Histories
          </Text>
          <Text c="text">Menampilkan 3 update terbaru</Text>
        </Box>
        <Button
          component={Link}
          href={`/admin/agent/${uoc}/order-histories`}
          color="text-light"
          variant="subtle"
        >
          See all
        </Button>
      </Group>
      <Divider size="sm" />
      {histories ? (
        <Stack>
          {histories.map((order, index) => (
            <OrderHistory key={index} history={order} />
          ))}
        </Stack>
      ) : (
        <Box>
          <Text>Belum ada pembelian</Text>
        </Box>
      )}
      {histories && (
        <>
          <Divider size="sm" />
          <Flex justify="space-between" align="center">
            <Group gap="xs">
              <TotalProduct
                total={data.categories.mame}
                type="Mame"
                color="secondary"
              />
              <TotalProduct
                total={data.categories.purto}
                type="Purto"
                color="primary"
              />
            </Group>
            <NumberFormatter value={total} />
          </Flex>
        </>
      )}
    </Stack>
  );
};

export default OrderHistories;
