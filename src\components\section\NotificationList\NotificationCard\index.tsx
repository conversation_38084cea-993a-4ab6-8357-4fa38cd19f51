import { NotificationData, NotificationType } from "@/utils/types/notification";
import { Paper } from "@mantine/core";
import BroadcastNotificationCardContent from "./NotificationCardContent/Broadcast";
import RequestDataChangeNotificationCardContent from "./NotificationCardContent/RequestDataChangeStatus";
import AgentDataChangeNotificationCardContent from "./NotificationCardContent/AgentDataChange";
import AgentTerminationNotificationCardContent from "./NotificationCardContent/AgentTermination";
import ForceUpdateDataNotificationCardContent from "./NotificationCardContent/ForceUpdateData";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { RoleType } from "@/store/slices/authSlice";
import AgentApplicationRequestCard from "@/components/common/RequestCard/Contents/AgentApplicationRequest";
import { RequestType } from "@/utils/types/request";
import DistributorTerminationNotificationCardContentProps from "./NotificationCardContent/DistributorTermination";

const RenderNotificationCardContent = (
  notification: NotificationData,
  currentRole?: RoleType
) => {
  switch (notification.type) {
    case NotificationType.ForceDataChange:
      return (
        <ForceUpdateDataNotificationCardContent
          date={notification.updatedAt || notification.createdAt || ""}
          field={notification.metadata[0].field}
          oldValue={notification.metadata[0].oldValue}
          newValue={notification.metadata[0].newValue}
        />
      );
    case NotificationType.AgentApplication:
      return (
        <AgentApplicationRequestCard
          requestType={RequestType.Application}
          id={notification.id}
          requesterName={notification.requesterName}
          createdAt={notification.createdAt || ""}
          metadatas={notification.metadata}
        />
      );
    case NotificationType.AgentTermination:
      return (
        <AgentTerminationNotificationCardContent
          status={notification.metadata[0].status as "accept" | "reject"}
          date={notification.updatedAt || notification.createdAt || ""}
        />
      );
    case NotificationType.ForceTermination:
      return (
        <DistributorTerminationNotificationCardContentProps
          date={notification.updatedAt || notification.createdAt || ""}
          reason={notification.reason || ""}
          daysRemaining={notification.metadata[0].oldValue}
        />
      );
    case NotificationType.DataChangeRequestStatus:
      return notification.requesterRole === "Agent" &&
        currentRole === "distributor" ? (
        <AgentDataChangeNotificationCardContent
          metadata={notification.metadata}
          updatedAt={notification.updatedAt || ""}
          createdAt={notification.createdAt || ""}
          sourceAgent={notification.requesterName || ""}
          reason={notification.reason || ""}
        />
      ) : (
        notification.metadata
          .filter((filtered) => {
            return filtered.status !== "pending";
          })
          .map((meta) => {
            return (
              <RequestDataChangeNotificationCardContent
                key={meta.id}
                status={meta.status as "accept" | "reject"}
                field={meta.field}
                reason={
                  meta.status === "reject"
                    ? meta.rejectReason
                    : notification.reason
                }
                date={notification.updatedAt || notification.createdAt || ""}
              />
            );
          })
      );
    case NotificationType.PublicAnnouncement:
    case NotificationType.PrivateAnnouncement:
    case NotificationType.Promotion:
    case NotificationType.WarningLetter:
      return (
        <BroadcastNotificationCardContent
          title={notification.title}
          type={notification.type}
          description={notification.body}
          documents={notification.documents}
          date={notification.updatedAt || notification.createdAt || ""}
        />
      );
  }
};

const NotificationCard = (notifcation: NotificationData) => {
  const { role } = useSelector((state: RootState) => state.auth);

  return (
    <Paper radius="md" w={"100%"} bg="bg">
      {RenderNotificationCardContent(notifcation, role)}
    </Paper>
  );
};

export default NotificationCard;
