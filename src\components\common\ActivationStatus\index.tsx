import { Flex, Text, Paper, PaperProps, Group } from "@mantine/core";
import { ACTIVATION_STATUS_MAPPING } from "@/utils/constants";
import { Dot } from "@/utils/icons";

export interface ActivationStatusProps extends PaperProps {
  status: "active" | "waitlisted" | "deactivated" | 1 | 2 | 0;
  compact?: boolean;
  withBorder?: PaperProps["withBorder"];
  radius?: PaperProps["radius"];
  customText?: string;
}

/**
 * A label that displays the activation status.
 * Based on the Mantine Paper component.
 * See more https://mantine.dev/core/paper/
 */
const ActivationStatus = ({
  status,
  compact,
  withBorder,
  radius,
  customText,
  ...props
}: ActivationStatusProps) => {
  const { label, color } = ACTIVATION_STATUS_MAPPING[status] || {
    label: "Unknown",
    color: "default",
  };

  return (
    <Paper
      radius={radius || 8}
      withBorder={withBorder}
      px={compact ? 6 : 12}
      py={compact ? 3 : 6}
      bg={withBorder ? "bg" : "bg-gray"}
      w="fit-content"
      aria-label={customText || label}
      role="activation status"
      {...props}
    >
      <Group align="center" gap="xs">
        <Dot color={`var(--mantine-color-${color}-7)`} size={compact ? 6 : 8} />
        <Text size={compact ? "xs" : "sm"} fw={500}>
          {customText || label}
        </Text>
      </Group>
    </Paper>
  );
};

export default ActivationStatus;
