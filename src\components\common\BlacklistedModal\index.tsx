import { Flex, Modal, Text, Title } from "@mantine/core";
import Warning from "../Warning";

interface BlacklistedModalProps {
  opened: boolean;
  onClose: () => void;
  fieldBlocked?: "phone" | "id";
}
const BlacklistedModal = ({
  opened,
  onClose,
  fieldBlocked = "id",
}: BlacklistedModalProps) => {
  const fieldBlockedText =
    fieldBlocked === "id" ? "NIK & NPWP" : "Nomor Telepon";
  return (
    <Modal opened={opened} onClose={onClose} size={"lg"} centered>
      <Flex ta={"center"} direction={"column"} gap={"sm"}>
        <Title>Akun Anda Diblokir</Title>
        <Text c={"text-light"} fw={500}>
          Akun ditemukan melanggar peraturan & ketentuan yang telah ditetapkan
          Hotto.
        </Text>
        <Warning
          title={`${fieldBlockedText} Anda telah kami blokir.`}
          caption={`Anda tidak dapat melakukan registrasi ulang kembali dengan ${fieldBlockedText} ini`}
        />
      </Flex>
    </Modal>
  );
};

export default BlacklistedModal;
