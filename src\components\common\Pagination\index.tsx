import {
  Text,
  Flex,
  Group,
  Button,
  BoxProps,
  Pagination as MantinePagination,
} from "@mantine/core";
import classes from "./style.module.css";
import { CaretLeft, CaretRight } from "@/utils/icons";

export interface PaginationProps extends BoxProps {
  page?: number;
  total: number;
  onChange?: (page: number) => void;
  nextText?: string;
  prevText?: string;
  disabled?: boolean;
}

const Pagination: React.FC<PaginationProps> = ({
  page = 1,
  total,
  onChange,
  nextText = "Next",
  prevText = "Previous",
  disabled,
  ...props
}) => {
  return (
    <MantinePagination.Root
      disabled={disabled}
      value={page}
      onChange={onChange}
      total={total}
      styles={{ control: { border: "none" } }}
      {...props}
    >
      <Group gap="xs">
        <Button
          fw={400}
          px={10}
          onClick={() => onChange?.(page - 1)}
          aria-label="Previous page"
          disabled={page <= 1 || disabled}
          className={classes.button}
        >
          <Flex align="center" gap={4}>
            <CaretLeft />
            <Text component="span" display={{ base: "none", md: "block" }}>
              {prevText}
            </Text>
          </Flex>
        </Button>
        <MantinePagination.Items />
        <Button
          fw={400}
          px={10}
          onClick={() => onChange?.(page + 1)}
          aria-label="Next page"
          disabled={page >= total || disabled}
          className={classes.button}
        >
          <Flex align="center" gap={4}>
            <Text component="span" display={{ base: "none", md: "block" }}>
              {nextText}
            </Text>
            <CaretRight />
          </Flex>
        </Button>
      </Group>
    </MantinePagination.Root>
  );
};

export default Pagination;
