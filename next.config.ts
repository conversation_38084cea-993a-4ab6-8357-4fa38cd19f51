/* eslint-disable @typescript-eslint/no-explicit-any */

import { NextConfig } from "next";

/** @type {import('next').NextConfig} */
const nextConfig: NextConfig = {
  compiler: {
    styledComponents: true,
  },
  experimental: {
    optimizePackageImports: [
      "@mantine/core",
      "@mantine/hooks",
      "@mantine/charts",
      "@mantine/dropzone",
    ],
    authInterrupts: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "hotto-bucket.sgp1.cdn.digitaloceanspaces.com",
        port: "",
        pathname: "**",
      },
    ],
  },
  // devIndicators: false,
  webpack: (config: any) => {
    config.resolve.alias.canvas = false;
    return config;
  },
};

export default nextConfig;
