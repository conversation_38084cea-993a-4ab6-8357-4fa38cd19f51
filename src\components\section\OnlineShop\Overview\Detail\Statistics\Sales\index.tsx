import { Stack } from "@mantine/core";
import SalesStatisticsCards from "../../../Statistics/Sales/Cards";
import SalesStatisticsChart from "../../../Statistics/Sales/Chart";
import OrderHistoryTable from "./OrderHistory";
import { useDetailOverviewContext } from "@/context/DetailOverviewContext";
import { isEmpty } from "@/utils/common-functions";

const DetailSalesStatistics = () => {
  const { slug } = useDetailOverviewContext();

  if (isEmpty(slug)) return null;

  return (
    <Stack gap="xl">
      <SalesStatisticsCards slug={slug} />
      <SalesStatisticsChart slug={slug} />
      <OrderHistoryTable slug={slug} />
    </Stack>
  );
};

export default DetailSalesStatistics;
