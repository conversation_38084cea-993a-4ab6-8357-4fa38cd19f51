import { RootState } from "@/store";
import React from "react";
import { useSelector } from "react-redux";
import ApplicationModalData from "../../ApplicationRequest/ApplicationData";
import { Skeleton } from "@mantine/core";

const ReviewStepModal: React.FC = () => {
  const { applicationRequest, loading } = useSelector(
    (state: RootState) => state.adminRequest
  );

  return applicationRequest && !loading ? (
    <ApplicationModalData {...applicationRequest} />
  ) : (
    <Skeleton height="50rem" />
  );
};

export default ReviewStepModal;
