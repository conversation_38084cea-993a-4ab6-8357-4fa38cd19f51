"use client";

import Caret from "@/components/icons/Caret";
import { fetchUserAgentDetails } from "@/store/slices/userAgentDetailsSlice";
import { Box, BoxProps, Button, Menu, Skeleton } from "@mantine/core";
import { useEffect, useState } from "react";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import Icon from "../Icon";
import { logout } from "@/store/slices/authSlice";

const UserProfilePopover: React.FC<BoxProps> = ({ ...props }) => {
  const dispatch: AppDispatch = useDispatch();
  const [opened, setOpened] = useState(false);
  const { data, loading } = useSelector(
    (state: RootState) => state.userAgentDetails
  );

  useEffect(() => {
    if (!data) dispatch(fetchUserAgentDetails());
  }, [data]);

  return (
    <Box {...props}>
      {loading ? (
        <Skeleton h="32px" w="240px" />
      ) : (
        <Menu
          shadow="md"
          width="12em"
          offset={-3}
          opened={opened}
          onChange={setOpened}
          transitionProps={{ transition: "scale-y", duration: 150 }}
        >
          <Menu.Target>
            <Button
              rightSection={<Caret direction={opened ? "up" : "down"} />}
              variant="transparent"
              color="default"
              fw={500}
              size="lg"
            >
              {data?.name || "Hi User 👋!"}
            </Button>
          </Menu.Target>
          <Menu.Dropdown>
            <Menu.Item
              leftSection={<Icon icon="nav/exit" />}
              onClick={() => dispatch(logout())}
            >
              Log out
            </Menu.Item>
          </Menu.Dropdown>
        </Menu>
      )}
    </Box>
  );
};

export default UserProfilePopover;
