import api from "@/lib/axios";
import { GET_WAITLIST_DATA, UPDATE_MAX_KUOTA } from "@/utils/api-routes";
import { createSlice } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { AppDispatch } from "..";
import { ApplicantData, WaitlistData } from "@/utils/types/distributor-agent";
import { ApiResponse } from "@/utils/types";
import { AxiosResponse } from "axios";

interface WaitlistState {
  loading: boolean;
  error: string | undefined;
  agentWaitlist?: WaitlistData;
  distributorWaitlist?: WaitlistData;
  selectedApplicant: ApplicantData | null;
}

const initialState: WaitlistState = {
  loading: false,
  error: undefined,
  agentWaitlist: undefined,
  distributorWaitlist: undefined,
  selectedApplicant: null,
};

const waitlistSlice = createSlice({
  name: "waitlist",
  initialState,
  reducers: {
    setError(state, action) {
      state.error = action.payload;
      toast.error(action.payload);
    },
    setLoading(state, action) {
      state.loading = action.payload;
    },
    setAgentWaitlist(state, action) {
      state.agentWaitlist = action.payload;
    },
    setDistributorWaitlist(state, action) {
      state.distributorWaitlist = action.payload;
    },
    setSelectedApplicant(state, action) {
      state.selectedApplicant = action.payload;
    },
  },
});

export const fetchWaitlistData =
  (role: "agent" | "distributor") => async (dispatch: AppDispatch) => {
    try {
      dispatch(waitlistSlice.actions.setLoading(true));
      const res = await api.get(GET_WAITLIST_DATA(role));
      if (res.data.status_code === 200) {
        if (role === "agent") {
          dispatch(waitlistSlice.actions.setAgentWaitlist(res.data.data));
        } else {
          dispatch(waitlistSlice.actions.setDistributorWaitlist(res.data.data));
        }
      }
    } catch (error) {
      dispatch(waitlistSlice.actions.setError(error));
    } finally {
      dispatch(waitlistSlice.actions.setLoading(false));
    }
  };

export const updateRadiusKuota =
  (kuota: number, role: "agent" | "distributor", onSuccess?: () => void) =>
  async (dispatch: AppDispatch) => {
    try {
      const res = await api.patch(UPDATE_MAX_KUOTA, {
        roleName: role,
        kuota: Number(kuota),
      });
      if (res.data.status_code === 200) {
        toast.success(`Kuota ${role} berhasil diubah`);
        onSuccess?.();
      }
    } catch (error) {
      dispatch(waitlistSlice.actions.setError(error));
    }
  };

export const setSelectedApplicant =
  (applicant: ApplicantData | null) => (dispatch: AppDispatch) => {
    dispatch(waitlistSlice.actions.setSelectedApplicant(applicant));
  };

export default waitlistSlice.reducer;
