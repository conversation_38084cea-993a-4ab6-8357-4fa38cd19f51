import {
  DistributorAgents,
  SearchAgentListResponse,
  SubmitReceiveAgentPayload,
} from "@/utils/types/distributor-agent";
import { createSlice } from "@reduxjs/toolkit";
import { AppDispatch } from "..";
import { AxiosResponse } from "axios";
import api from "@/lib/axios";
import {
  GET_AGENT_LIST,
  GET_UPDATE_LOGS,
  SUBMIT_RECEIVE_AGENT,
} from "@/utils/api-routes";
import { toast } from "sonner";
import {
  UpdateLogsRequestData,
  UpdateLogsRequestResponse,
} from "@/utils/types/request";
import { ITEMS_PER_PAGE, SortFilterValue } from "@/utils/constants";
import { fetchUserProfile } from "./userProfileSlice";

export interface DistributorAgentsSliceState {
  loading: boolean;
  error?: string;
  agentList: DistributorAgents[];
  updateLogsRequest: UpdateLogsRequestData;
}

const initialState: DistributorAgentsSliceState = {
  loading: false,
  error: undefined,
  agentList: [],
  updateLogsRequest: {} as UpdateLogsRequestData,
};

const distributorAgentsSlice = createSlice({
  name: "distributor",
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
      toast.error(action.payload);
    },
    setAgentList: (state, action) => {
      state.agentList = action.payload;
    },
    setUpdateLogsRequest: (state, action) => {
      state.updateLogsRequest = action.payload;
    },
  },
});

export const fetchAgentList =
  (search?: string) => async (dispatch: AppDispatch) => {
    try {
      dispatch(distributorAgentsSlice.actions.setLoading(true));
      const res: AxiosResponse<SearchAgentListResponse> = await api.get(
        GET_AGENT_LIST(search)
      );
      if (res.status === 200 && res.data.data) {
        dispatch(distributorAgentsSlice.actions.setAgentList(res.data.data));
      } else {
        dispatch(distributorAgentsSlice.actions.setError(res.data.message));
      }
    } catch {
      dispatch(
        distributorAgentsSlice.actions.setError("Failed to get agent list")
      );
    } finally {
      dispatch(distributorAgentsSlice.actions.setLoading(false));
    }
  };

export const fetchUpdateLogs =
  (
    uoc: string,
    page: number = 1,
    order: SortFilterValue = "desc",
    status: "accept" | "reject" | "" = ""
  ) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(distributorAgentsSlice.actions.setLoading(true));
      const res: AxiosResponse<UpdateLogsRequestResponse> = await api.get(
        GET_UPDATE_LOGS(uoc, page, order, ITEMS_PER_PAGE, status)
      );
      if (res.status === 200 && res.data.data) {
        dispatch(
          distributorAgentsSlice.actions.setUpdateLogsRequest(res.data.data)
        );
      } else {
        dispatch(distributorAgentsSlice.actions.setError(res.data.message));
      }
    } catch {
      dispatch(
        distributorAgentsSlice.actions.setError("Failed to get update logs")
      );
    } finally {
      dispatch(distributorAgentsSlice.actions.setLoading(false));
    }
  };

export const submitReceiveAgent =
  (data: SubmitReceiveAgentPayload) => async (dispatch: AppDispatch) => {
    try {
      dispatch(distributorAgentsSlice.actions.setLoading(true));
      await api.patch(SUBMIT_RECEIVE_AGENT, data);
      dispatch(fetchUserProfile(true));
      toast.success("Application form berhasil disubmit.");
    } catch (error) {
      toast.error(
        "Terjadi kesalah saat submit application form, silahkan coba beberapa saat lagi."
      );
      return Promise.reject(error);
    } finally {
      dispatch(distributorAgentsSlice.actions.setLoading(false));
    }
  };

export default distributorAgentsSlice.reducer;
