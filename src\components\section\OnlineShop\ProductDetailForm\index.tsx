import ImageMultiSelect from "@/components/common/ImageMultiSelect";
import Input from "@/components/common/Input";
import InputGroup from "@/components/common/InputGroup";
import useBreakpoints from "@/hooks/useBreakpoints";
import { AppDispatch, RootState } from "@/store";
import {
  createProduct,
  fetchProductImages,
  updateProductDetail,
} from "@/store/slices/onlineShopSlices/productDetailSlice";
import { PRODUCT_DESCRIPTION_TEMPLATES } from "@/utils/constants";
import { ProductDetail } from "@/utils/types/online-shop";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  Anchor,
  Button,
  Flex,
  NumberInput,
  Switch,
  Text,
  Textarea,
} from "@mantine/core";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "sonner";
import * as yup from "yup";

const schema = yup.object({
  name: yup.string().required("Nama produk harus diisi"),
  description: yup.string().required("Deskripsi produk harus diisi"),
  status: yup.boolean().required("Status produk harus diisi").default(true),
  images: yup
    .array()
    .of(yup.string().defined())
    .min(3, "Minimal 3 gambar produk harus dipilih")
    .required("Gambar produk harus diplih"),
  minimumOrder: yup
    .number()
    .min(1, "Minimum order tidak boleh 0")
    .required("Minimum order harus diisi")
    .typeError("Minimum order harus berupa angka"),

  totalStocks: yup
    .number()
    .required("Stok produk harus diisi")
    .typeError("Stok harus berupa angka"),

  unitPrice: yup
    .number()
    .min(1, "Harga produk tidak boleh 0")
    .required("Harga produk harus diisi")
    .typeError("Harga harus berupa angka"),

  // Variants
  // TODO: uncomment when variants feature is ready
  // firstVariantKey: yup.string().optional().nullable(),
  // secondVariantKey: yup.string().optional().nullable(),
  // firstVariantValue: yup.array().of(yup.string().defined()).nullable(),
  // secondVariantValue: yup.array().of(yup.string().defined()).nullable(),
  // variants: yup
  //   .array()
  //   .of(
  //     yup
  //       .object({
  //         id: yup.number().optional(),
  //         firstVariant: yup.string().required(),
  //         secondVariant: yup.string().required(),
  //         stock: yup.number().required(),
  //         price: yup.number().required(),
  //       })
  //       .defined()
  //   )
  //   .nullable(),
});

interface ProductDetailFormProps {
  productId?: string;
  product?: ProductDetail;
}

const ProductDetailForm = ({ productId, product }: ProductDetailFormProps) => {
  const dispatch: AppDispatch = useDispatch();
  const { images, loadingImages } = useSelector(
    (state: RootState) => state.productDetail
  );
  const { role } = useSelector((state: RootState) => state.auth);
  const { mobile } = useBreakpoints();
  const router = useRouter();
  const [formTouched, setFormTouched] = useState(false);

  const {
    register,
    trigger,
    watch,
    handleSubmit: formHandleSubmit,
    formState: { errors, isValid, touchedFields },
    setValue,
  } = useForm<ProductDetail>({
    mode: "onBlur",
    resolver: yupResolver<ProductDetail>(schema),
    defaultValues: product || {
      status: true,
      images: [],
      // firstVariantKey: null,
      // secondVariantKey: null,
      // firstVariantValue: null,
      // secondVariantValue: null,
      // variants: null,
    },
  });

  const handleDescriptionChange = (value: string) => {
    setValue("description", value);
    setFormTouched(true);
    trigger("description");
  };

  const handleSubmit = formHandleSubmit(
    (data) => {
      if (productId) {
        dispatch(updateProductDetail(productId, data, onSuccess));
      } else {
        dispatch(createProduct(data, onSuccess));
      }
    },
    (errors) => {
      console.error("Form validation errors:", errors);
      setFormTouched(true);
      toast.error("Informasi produk masih belum lengkap! Mohon check kembali.");
    }
  );

  const onSuccess = () => {
    if (role === "admin") {
      router.push("/admin/shop/product");
    } else {
      router.push("/shop/product");
    }
  };

  useEffect(() => {
    dispatch(fetchProductImages());

    // Only set default values if product is not provided
    if (!product) {
      setValue("status", true);
      setValue("images", []);
      // setValue("firstVariantValue", null);
      // setValue("secondVariantValue", null);
      // setValue("variants", null);
    } else {
      // If product is provided, set all values from product
      Object.entries(product).forEach(([key, value]) => {
        setValue(key as keyof ProductDetail, value);
      });
    }

    // Don't trigger validation on initial load
  }, [product, setValue, dispatch]);

  const priceInputCaption = () => {
    return (
      <Text size="sm">
        Harga produk harus sesuai dengan{" "}
        {/* TODO: redirect to hotto terms & con */}
        <Anchor
          size="sm"
          href={process.env.NEXT_PUBLIC_HOTTO_TERMS_AND_CONDITIONS_URL}
          td={"underline"}
        >
          syarat & ketentuan
        </Anchor>{" "}
        yang ditetapkan Hotto.
      </Text>
    );
  };

  // Only show errors if the form has been touched or the field has been touched
  const getFieldError = (fieldName: keyof ProductDetail) => {
    if (!formTouched && !touchedFields[fieldName]) {
      return undefined;
    }
    return errors[fieldName]?.message;
  };

  return (
    <form
      onSubmit={handleSubmit}
      style={{
        width: "100%",
      }}
    >
      <Flex direction={"column"} gap={"lg"} w={"100%"}>
        <Text size="xl" fw={600}>
          Informasi Produk
        </Text>
        <Flex gap={mobile ? "lg" : "xl"} direction={mobile ? "column" : "row"}>
          <InputGroup
            label="Nama Produk"
            mandatory
            w={mobile ? "100%" : "66%"}
            caption="Nama tidak bisa diubah setelah produk terjual"
            error={getFieldError("name")}
          >
            {/* TODO: set disabled if product has been sold */}
            <Input
              placeholder="Hotto Purto"
              {...register("name", {
                onBlur: () => setFormTouched(true),
              })}
            />
          </InputGroup>
          <InputGroup
            label="Status Produk"
            mandatory
            w={mobile ? "100%" : "33%"}
            caption="Jika status aktif, produkmu dapat dicari oleh pembeli"
            error={getFieldError("status")}
          >
            <Switch
              checked={watch("status")}
              onChange={(event) => {
                setValue("status", event.currentTarget.checked);
                setFormTouched(true);
                trigger("status");
              }}
            />
          </InputGroup>
        </Flex>
        <InputGroup
          label="Deskripsi Produk"
          mandatory
          error={getFieldError("description")}
        >
          <Textarea disabled value={watch("description")} rows={8} />
          <Flex gap={"md"} align={"center"} wrap={"wrap"} py={"xs"}>
            <Text fw={500}>Pilih template deskripsi:</Text>
            <Flex gap={"sm"} wrap={"wrap"} align={"center"}>
              {PRODUCT_DESCRIPTION_TEMPLATES.map((desc) => (
                <Button
                  key={desc.value}
                  size={mobile ? "xs" : "sm"}
                  variant="transparent"
                  c={"black"}
                  styles={{
                    root: {
                      border: `1px solid ${
                        watch("description") == desc.value
                          ? "var(--mantine-color-primary-filled)"
                          : "var(--mantine-color-gray-filled)"
                      }`,
                    },
                  }}
                  bg={
                    watch("description") == desc.value
                      ? "var(--mantine-color-primary-1)"
                      : "transparent"
                  }
                  onClick={() => handleDescriptionChange(desc.value)}
                >
                  {desc.label}
                </Button>
              ))}
            </Flex>
          </Flex>
        </InputGroup>

        <InputGroup
          label="Foto Produk"
          mandatory
          caption="Pilihlah min. 3 foto"
          error={getFieldError("images")}
        >
          <ImageMultiSelect
            selectedImages={watch("images") || []}
            imageSelection={images}
            // TODO: delete when image selection is ready
            // imageSelection={[
            //   PLACEHOLDER_IMAGE,
            //   "https://hotto-bucket.sgp1.cdn.digitaloceanspaces.com/staging/email-images/banner.png",
            //   "https://hotto-bucket.sgp1.cdn.digitaloceanspaces.com/staging/email-images/logo.svg",
            //   PLACEHOLDER_IMAGE,
            //   "https://hotto-bucket.sgp1.cdn.digitaloceanspaces.com/staging/email-images/banner.png",
            // ]}
            onChange={(images) => {
              setValue("images", images);
              setFormTouched(true);
              trigger("images");
            }}
            loading={loadingImages}
          />
        </InputGroup>

        <Text size="xl" fw={600}>
          Detail Produk
        </Text>
        <InputGroup
          label="Minimum Pesanan"
          mandatory
          caption="Atur jumlah minimum yang harus dibeli untuk produk ini."
          error={errors.minimumOrder?.message}
        >
          <NumberInput
            thousandSeparator="."
            decimalSeparator=","
            placeholder="100"
            value={watch("minimumOrder")}
            onChange={(value) => {
              setValue("minimumOrder", value as number);
              setFormTouched(true);
              trigger("minimumOrder");
            }}
            hideControls
          />
        </InputGroup>
        <Flex gap={mobile ? "lg" : "xl"} direction={mobile ? "column" : "row"}>
          <InputGroup
            label="Harga Satuan"
            mandatory
            caption={priceInputCaption()}
            error={errors.unitPrice?.message}
          >
            <NumberInput
              thousandSeparator="."
              decimalSeparator=","
              placeholder="Rp 300.000"
              value={watch("unitPrice")}
              onChange={(value) => {
                setValue("unitPrice", value as number);
                setFormTouched(true);
                trigger("unitPrice");
              }}
              hideControls
            />
          </InputGroup>
          <InputGroup
            label="Jumlah Stok"
            mandatory
            error={errors.totalStocks?.message}
          >
            <NumberInput
              thousandSeparator="."
              decimalSeparator=","
              placeholder="100"
              value={watch("totalStocks")}
              onChange={(value) => {
                setValue("totalStocks", value as number);
                setFormTouched(true);
                trigger("totalStocks");
              }}
              hideControls
            />
          </InputGroup>
        </Flex>
        <Flex
          w={"100%"}
          justify={mobile ? "center" : "flex-end"}
          gap={"md"}
          direction={mobile ? "column" : "row"}
        >
          {mobile ? (
            <Anchor ta="center" href="/shop/product" fw={500} size="sm">
              Kembali
            </Anchor>
          ) : (
            <Button variant="outline" onClick={() => router.back()}>
              Kembali
            </Button>
          )}
          <Button type="submit" onClick={() => setFormTouched(true)}>
            Simpan
          </Button>
        </Flex>
      </Flex>
    </form>
  );
};

export default ProductDetailForm;
