"use client";

import PageLayout from "@/components/common/PageLayout";
import AgentDistributorDetails from "@/components/section/AdminAgentDistributor/Details";
import { AppDispatch, RootState } from "@/store";
import { fetchAdminAgentDetails } from "@/store/slices/adminAgentDetailsSlice";
import { withAuth } from "@/utils/withAuth";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { use } from "react";
import { notFound } from "next/navigation";
import { useMounted } from "@mantine/hooks";

export interface DistributorDetailsProps {
  params: Promise<{
    uoc: string;
  }>;
}

const DistributorDetailsPage = ({ params }: DistributorDetailsProps) => {
  const mounted = useMounted();
  const dispatch: AppDispatch = useDispatch();
  const { data, error } = useSelector(
    (state: RootState) => state.adminAgentDetails
  );
  const { uoc } = use(params);

  useEffect(() => {
    dispatch(fetchAdminAgentDetails(uoc));
  }, [uoc, dispatch]);

  if (error && mounted) notFound();

  return (
    <PageLayout title="Agent & Distributor">
      <AgentDistributorDetails data={data} />
    </PageLayout>
  );
};

export default withAuth(DistributorDetailsPage);
