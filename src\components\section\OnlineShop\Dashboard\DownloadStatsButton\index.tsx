import { Button } from "@mantine/core";
import Icon from "@/components/common/Icon";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { downloadCustomerStats } from "@/store/slices/onlineShopSlices/dashboardSlice";
import { useQueryState } from "nuqs";

const DownloadStatsButton = () => {
  const dispatch: AppDispatch = useDispatch();
  const {
    loading: { customerStatsDownload: downloading },
  } = useSelector((state: RootState) => state.dashboard);

  const [chart] = useQueryState("chart", {
    defaultValue: "topSpender",
  });

  const handleDownload = () => {
    dispatch(downloadCustomerStats(chart as "topSpender" | "demography"));
  };

  return (
    <Button
      variant="outline"
      rightSection={
        downloading ? null : <Icon icon="info/document-fill" size={12} />
      }
      onClick={handleDownload}
      disabled={downloading}
    >
      {downloading ? "Downloading..." : "Unduh"}
    </Button>
  );
};

export default DownloadStatsButton;
