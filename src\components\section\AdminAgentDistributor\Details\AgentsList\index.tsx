'use client'

import {
 TableData,
 Group,
 Text,
 ActionIcon,
 Stack,
 Flex,
 Divider,
 Table,
 Anchor,
} from '@mantine/core'
import Icon from '@/components/common/Icon'
import ActivationStatus from '@/components/common/ActivationStatus'
import Link from 'next/link'
import { AppDispatch, RootState } from '@/store'
import { useDispatch, useSelector } from 'react-redux'
import { useEffect } from 'react'
import { fetchAgentListInDistributor } from '@/store/slices/adminAgentListInDistributorSlice'

export interface AgentsListProps {
 uoc: string
 role: string
}

const AgentsList = ({ uoc, role }: AgentsListProps) => {
 const dispatch: AppDispatch = useDispatch()
 const { data, loading } = useSelector(
  (state: RootState) => state.adminAgentListInDistributor
 )
 useEffect(() => {
  if (role === 'admin') dispatch(fetchAgentListInDistributor(uoc))
 }, [uoc, dispatch])

 const tableData: TableData = {
  head: [
   'Nama Agent',
   'Al<PERSON><PERSON>',
   'Nomor Telepon',
   'Agent Since',
   'Last Order Date',
   'Last Ordered Item',
   'Action',
  ],
  body:
   data?.agents?.length > 0
    ? data.agents.map((agent) => [
       <Group key={agent.uoc}>
        <Anchor component={Link} href={`/admin/agent/${agent.uoc}`}>
         {agent.name}
        </Anchor>
        <ActivationStatus status={agent.status} withBorder />
       </Group>,
       agent.address,
       agent.phone,
       agent.joinedAt,
       agent.lastOrderDate,
       agent.lastOrderItem,
       <Flex gap="sm" key={agent.uoc}>
        <ActionIcon variant="transparent" aria-label="Delete">
         <Icon icon="action/delete" />
        </ActionIcon>
        <ActionIcon variant="transparent" aria-label="Edit">
         <Icon icon="action/edit" />
        </ActionIcon>
       </Flex>,
      ])
    : undefined,
 }

 return (
  !loading && (
   <Stack gap="xs">
    <Text size="xl" fw={600}>
     List of Agents
    </Text>
    <Divider size="sm" />
    {tableData.body ? (
     <Table.ScrollContainer minWidth={1024}>
      <Table
       withColumnBorders
       highlightOnHover
       verticalSpacing="md"
       data={tableData}
      />
     </Table.ScrollContainer>
    ) : (
     <Text>Tidak ada agent</Text>
    )}
   </Stack>
  )
 )
}

export default AgentsList
