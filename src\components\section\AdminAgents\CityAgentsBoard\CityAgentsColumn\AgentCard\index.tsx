import { Anchor, Flex, Paper, Text } from "@mantine/core";
import AgentCardSection from "./AgentCardSection";
import ActivationStatus from "@/components/common/ActivationStatus";
import Icon from "@/components/common/Icon";
import { AgentCardDataDetailed, AgentCardDataSimple } from "@/utils/types";
import { formatDateToString } from "@/utils/common-functions";

type AgentCardProps =
  | { variant: "simple"; data: AgentCardDataSimple }
  | { variant: "detailed"; data: AgentCardDataDetailed };

const AgentCard = ({ variant, data }: AgentCardProps) => {
  return (
    <Paper bg={"bg"} p={"xl"} maw={"min(480px, 88vw)"} miw={"min(480px, 88vw)"}>
      <Flex direction="column" gap="xs">
        <Flex align="center" gap="sm">
          <Anchor fw="bold" c={"black"} href={`/admin/agent/${data.uoc}`}>
            {data.name}
          </Anchor>
          <ActivationStatus status={data.status} />
        </Flex>

        {variant === "detailed" && (
          <>
            <Flex direction="row" gap="xs" align="center">
              <Icon size={20} icon="info/location" />
              <Text size="sm" c={"text-light"}>
                {data.address}
              </Text>
            </Flex>
            {data.orderSummary && (
              <AgentCardSection
                top={`Total Order: ${data.orderSummary.totalOrder}RO`}
                bottom={
                  data.orderSummary.totalOrder > 0
                    ? `Last Order: ${formatDateToString(
                        data.orderSummary.lastOrder
                      )}`
                    : ""
                }
                href={`/admin/agent/${data.uoc}`}
              />
            )}
            {data.countAgent && data.countAgent.count > 0 && (
              <AgentCardSection
                top={`# of agent: ${data.countAgent.count}`}
                bottom={`Last added: ${data.countAgent.lastAddedAgent}`}
                href={`/admin/agent/${data.uoc}`}
              />
            )}
          </>
        )}
        {variant === "simple" && (
          <>
            {data.adminPhone && (
              <Flex direction="row" gap="xs" align="center">
                <Icon size={20} icon="info/location" />
                <Text size="sm" c={"text-light"}>
                  {data.adminPhone}
                </Text>
              </Flex>
            )}
            {data.ownerPhone && (
              <Flex direction="row" gap="xs" align="center">
                <Icon size={20} icon="info/location" />
                <Text size="sm" c={"text-light"}>
                  {data.ownerPhone}
                </Text>
              </Flex>
            )}
          </>
        )}
      </Flex>
    </Paper>
  );
};

export default AgentCard;
