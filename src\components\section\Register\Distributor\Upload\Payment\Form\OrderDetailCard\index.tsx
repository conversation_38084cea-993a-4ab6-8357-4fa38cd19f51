import {
  Box,
  Divider,
  Group,
  NumberFormatter,
  Paper,
  Stack,
  Text,
} from "@mantine/core";
import { DistributorOrderItem } from "@/utils/types/distributor-register";

const OrderDetailCard = ({ order }: { order: DistributorOrderItem }) => {
  return (
    <Box>
      <Paper bg="bg" p="xl" withBorder>
        <Stack>
          <Text>
            Sebelum sepenuhnya menjadi Distributor Hotto, Anda perlu untuk
            membeli produk Hotto dari pusat sebesar total pembayaran.
          </Text>
          <Box>
            <Text fw={600}>Rincian Pembayaran</Text>
            <Group justify="space-between">
              <Text>
                Subtotal untuk{" "}
                {order.products.map((product) => product.name).join(" & ")}
              </Text>
              <Text fz="h4" fw={500}>
                <NumberFormatter value={order.subTotalPayment} />
              </Text>
            </Group>
            <Group justify="space-between">
              <Text>Subtotal pengiriman ({order.courier})</Text>
              <Text fz="h4" fw={500}>
                {order.shippingCost > 0 ? (
                  <NumberFormatter value={order.shippingCost} />
                ) : (
                  "FREE"
                )}
              </Text>
            </Group>
            <Divider my="sm" size="sm" mx="-xl" />
            <Group justify="space-between" mt="xs">
              <Text fw={600}>Total Pembayaran</Text>
              <Text fz="h4" fw={500}>
                <NumberFormatter value={order.totalPayment} />
              </Text>
            </Group>
          </Box>
          <Divider size="lg" mx="-xl" />
          <Box>
            <Text>Nomor Rekening</Text>
            <Text fw={600} fz="h3">
              {process.env.NEXT_PUBLIC_BANK_ACCOUNT_NUMBER ||
                "NEXT_PUBLIC_BANK_ACCOUNT_NUMBER"}
            </Text>
            <Text>Catatan</Text>
            <Text fw={600} fz="h3">
              {order.notes}
            </Text>
          </Box>
          <Text>Pastikan untuk membayar dengan angka yang tepat.</Text>
        </Stack>
      </Paper>

      <Text fz="h3" fw={500} mt="lg">
        Variant Produk
      </Text>
      <Stack gap="xs">
        {order.products.map((product, index) => (
          <Group key={index} justify="space-between">
            <Text>{product.name}</Text>
            <Box ta="end">
              <Text fz="lg" fw={500}>
                <NumberFormatter value={product.price} />
              </Text>
              <Text c="text-light" td="line-through">
                <NumberFormatter value={product.price} />
              </Text>
            </Box>
          </Group>
        ))}
      </Stack>
    </Box>
  );
};

export default OrderDetailCard;
