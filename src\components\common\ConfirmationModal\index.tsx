import { Drawer, Flex, Modal, Text, Title, Button } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";

interface ConfirmationModalProps {
  opened: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmLabel?: string;
}

const ConfirmationModal = ({
  opened,
  onClose,
  onConfirm,
  title,
  message,
  confirmLabel = "Terima",
}: ConfirmationModalProps) => {
  const isMobile = useMediaQuery("(max-width: 768px)");

  const renderModalContent = () => {
    return (
      <Flex
        direction={"column"}
        justify={"space-between"}
        align={"center"}
        gap={"lg"}
      >
        <Flex
          direction={"column"}
          align={"center"}
          gap={"xs"}
          py={"xs"}
          px={"sm"}
          ta={"center"}
          w={"100%"}
        >
          <Title>{title}</Title>
          <Text c={"text-light"} fw={600}>
            {message}
          </Text>
        </Flex>
        <Flex
          direction={isMobile ? "column-reverse" : "row"}
          align={"center"}
          justify={"center"}
          gap={"sm"}
          w={"100%"}
          mt={isMobile ? "sm" : "xl"}
        >
          <Button variant="subtle" onClick={onClose}>
            Kembali
          </Button>
          <Button onClick={onConfirm}>{confirmLabel}</Button>
        </Flex>
      </Flex>
    );
  };

  return (
    <>
      {!isMobile ? (
        <Modal
          opened={opened}
          onClose={onClose}
          size={"md"}
          radius={"8px"}
          centered
        >
          {renderModalContent()}
        </Modal>
      ) : (
        <Drawer
          opened={opened}
          onClose={onClose}
          position="bottom"
          radius={"8px 8px 0 0"}
          size={"33vh"}
        >
          {renderModalContent()}
        </Drawer>
      )}
    </>
  );
};

export default ConfirmationModal;
