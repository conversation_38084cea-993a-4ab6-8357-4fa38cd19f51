import {
  Button,
  Paper,
  Stack,
  TextInput,
  Title,
  Text,
  ModalProps,
  DrawerProps,
  Group,
} from "@mantine/core";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import {
  resetFirstOrdersToUpdate,
  updateRequestStatus,
} from "@/store/slices/adminRequestSlice";
import {
  fetchReceivedRequests,
  fetchSentRequests,
  updateMarketplaceLink,
} from "@/store/slices/distributorRequestSlice";
import { RequestType } from "@/utils/types/request";
import { capitalize } from "@/utils/common-functions";
import ButtonGroup from "@/components/common/ButtonGroup";
import RequestDetail from "./RequestDetail";
import ResponsiveModal from "../ResponsiveModal";
import useBreakpoints from "@/hooks/useBreakpoints";
import { AdminRequestProvider } from "../../../context/AdminRequestContext";

interface RequestModalProps {
  type: RequestType;
  status: "accept" | "reject" | "update";
  requestId: number;
  opened: ModalProps["opened"];
  onClose: ModalProps["onClose"];
  field?: string;
  modalSize?: ModalProps["size"];
  drawerSize?: DrawerProps["size"];
  applicationStep?: number;
  role?: "Agent" | "Distributor";
}

const RequestModal = ({
  type,
  status = "accept",
  opened,
  onClose,
  requestId,
  field = "",
  modalSize = "lg",
  drawerSize = "lg",
  applicationStep = 1,
  role,
}: RequestModalProps) => {
  const { mobile } = useBreakpoints();
  const dispatch: AppDispatch = useDispatch();
  const [reason, setReason] = useState("");
  const [active, setActive] = useState(applicationStep - 1);
  const { loading } = useSelector((state: RootState) => state.adminRequest);
  const { role: userRole } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    setReason("");
    setActive(applicationStep - 1);
    dispatch(resetFirstOrdersToUpdate());
  }, [opened]);

  const handleSubmit = async (selectedStatus: "accept" | "reject") => {
    const data = {
      status: selectedStatus,
      reason:
        selectedStatus === "reject" || type === RequestType.Application
          ? reason || selectedStatus
          : undefined,
      field,
    };

    const success = await dispatch(
      updateRequestStatus(requestId, data, userRole)
    );

    if (!success) return;

    if (userRole !== "admin") {
      dispatch(fetchReceivedRequests());
      dispatch(fetchSentRequests());
    }

    onClose();
  };

  const handleUpdateMarketplace = async () => {
    const success = await dispatch(
      updateMarketplaceLink(requestId, field, reason)
    );
    if (success) onClose();
  };

  const handleSubmitClick = () => {
    if (type === RequestType.Application && applicationStep === 1) {
      setActive(active + 1);
      return;
    }
    if (status === "update") {
      handleUpdateMarketplace();
    } else {
      handleSubmit(status);
    }
  };

  const getModalTitle = useCallback((): string => {
    if (
      type === RequestType.Application ||
      type === RequestType.ChangeDistributor
    ) {
      return role === "Agent"
        ? "Review aplikasi pendaftaran agent baru"
        : "Review Aplikasi Distributor baru";
    }
    if (type === RequestType.DistributorCheckMarketplaceLinks)
      return "Update Link Marketplace";
    return `Yakin ingin ${status === "accept" ? "menerima" : "menolak"} ${
      type === RequestType.Reactivation ? "re-activation" : "request"
    } ini?`;
  }, [type, role, status]);

  const getInputLabel = useCallback((): string => {
    if (status === "update") return `Link ${capitalize(field)} baru`;
    return `Alasan${status === "reject" ? " Ditolak" : ""}`;
  }, [status, field]);

  const getInputPlaceholder = useCallback((): string => {
    if (status === "update") return "Masukkan data baru";
    if (status === "reject") return "Contoh: Terlalu sering mengganti data";
    return "Masukkan alasan";
  }, [status]);

  const getSubmitButtonLabel = useCallback((): string => {
    if (type === RequestType.Application && applicationStep === 2)
      return "Submit";
    if (status === "accept") return "Terima";
    if (status === "reject") return "Tolak";
    return "Update";
  }, [type, applicationStep, status]);

  const showInput: boolean = useMemo(
    () =>
      status === "update" ||
      status === "reject" ||
      type === RequestType.ChangeDistributor ||
      type === RequestType.Application,
    [status, type]
  );

  const showWarning: boolean = useMemo(
    () => role === "Distributor" || type === RequestType.ChangeDistributor,
    [type]
  );

  const showRejectButton: boolean = useMemo(
    () =>
      type === RequestType.Application ||
      type === RequestType.ChangeDistributor,
    [type]
  );

  const disableSubmit: boolean = useMemo(
    () =>
      (status === "reject" && !reason) ||
      (type === RequestType.Application && !reason) ||
      (type === RequestType.ChangeDistributor && !reason),
    [status, type, reason]
  );

  const hideActions: boolean = useMemo(
    () => type === RequestType.Application && (active === 1 || active === 2),
    [type, active]
  );

  return (
    <ResponsiveModal
      opened={opened}
      onClose={onClose}
      loading={loading}
      modalProps={{ size: modalSize, padding: mobile ? "sm" : "xl" }}
      drawerProps={{ size: drawerSize, padding: mobile ? "sm" : "xl" }}
    >
      <Stack align="center" justify="center" gap="sm">
        <Title ta="center" order={2} mb="md">
          {getModalTitle()}
        </Title>
        <AdminRequestProvider
          active={active}
          submitRequest={handleSubmit}
          closeModal={onClose}
        >
          <RequestDetail
            id={requestId}
            type={type}
            field={field}
            applicationStep={applicationStep}
            role={role}
          />
        </AdminRequestProvider>
        {!hideActions && (
          <>
            {showInput && (
              <Stack w="100%">
                <TextInput
                  required
                  type="text"
                  value={reason}
                  label={getInputLabel()}
                  placeholder={getInputPlaceholder()}
                  onChange={(e) => setReason(e.target.value)}
                />
                {showWarning && (
                  <Paper
                    bg="error.1"
                    px="lg"
                    py="sm"
                    radius="8px 8px 0 0"
                    mx={{ base: "-sm", sm: "-xl" }}
                  >
                    <Text c="error" fw={500} fz="sm">
                      Pastikan Anda telah memeriksa informasi{" "}
                      {role === "Distributor" ? "distributor" : "agent"} ini
                      dengan benar!
                    </Text>
                    <Text c="text-black.9" fz="sm">
                      {role === "Distributor" ? "Distributor" : "Agent"} yang
                      Anda terima tidak dapat ditolak kembali tanpa alasan yang
                      valid.
                    </Text>
                  </Paper>
                )}
              </Stack>
            )}
            <ButtonGroup mt="xl" justify="space-between" w="100%">
              <Button c="black" variant="subtle" onClick={onClose}>
                Kembali
              </Button>
              <Group
                gap="xs"
                justify="end"
                w={{ base: "100%", sm: "fit-content" }}
              >
                {showRejectButton && (
                  <Button
                    loading={loading}
                    color="error"
                    onClick={() => handleSubmit("reject")}
                    disabled={!reason}
                  >
                    Reject
                  </Button>
                )}
                <Button
                  loading={loading}
                  disabled={disableSubmit}
                  onClick={handleSubmitClick}
                >
                  {getSubmitButtonLabel()}
                </Button>
              </Group>
            </ButtonGroup>
          </>
        )}
      </Stack>
    </ResponsiveModal>
  );
};

export default RequestModal;
