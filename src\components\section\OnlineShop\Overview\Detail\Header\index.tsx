import { PageLayoutProps } from "@/components/common/PageLayout";
import useOverviewFilters from "@/hooks/useOverviewFilters";
import { AppDispatch, RootState } from "@/store";
import { fetchAdminAgentDetails } from "@/store/slices/adminAgentDetailsSlice";
import { downloadOverview } from "@/store/slices/onlineShopSlices/overviewSlice";
import { getTodayDateTime } from "@/utils/common-functions";
import { Download } from "@/utils/icons";
import { Button } from "@mantine/core";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

const OverviewDetailHeader = (
  uoc: string
): PageLayoutProps["navigationContent"] => {
  const dispatch: AppDispatch = useDispatch();
  const { data, loading } = useSelector(
    (state: RootState) => state.adminAgentDetails
  );
  const {
    loading: { download: downloading },
  } = useSelector((state: RootState) => state.overview);
  const { stats } = useOverviewFilters();

  useEffect(() => {
    if (uoc) dispatch(fetchAdminAgentDetails(uoc));
  }, [uoc]);

  const handleDownload = () => {
    dispatch(
      downloadOverview(
        data.slug,
        stats === "sales" ? "order-history" : "top-purchasing-customers"
      )
    );
  };

  return {
    title: data.name,
    description: `Update terakhir: ${getTodayDateTime()}`,
    backUrl: "/admin/shop/overview",
    loading: loading,
    rightContent: (
      <Button
        variant="outline"
        color="gray"
        c="primary"
        loading={downloading}
        onClick={handleDownload}
        rightSection={<Download />}
      >
        Unduh
      </Button>
    ),
  };
};

export default OverviewDetailHeader;
