import { Flex, Text, Title } from "@mantine/core";
import NotificationCard from "./NotificationCard";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { useEffect, useState } from "react";
import { fetchDistributorNotifications } from "@/store/slices/distributorNotificationSlice";
import InfiniteScroll from "react-infinite-scroll-component";
import InfiniteScrollWrapper from "@/components/common/InfiniteScrollWrapper";

const NotificationList = () => {
  const dispatch: AppDispatch = useDispatch();
  const { notifications, notificationResponse } = useSelector(
    (state: RootState) => state.distributorNotification
  );

  const [page, setPage] = useState(1);

  const handleNextPage = () => {
    const nextPage = page + 1;
    setPage(nextPage);
    dispatch(fetchDistributorNotifications(nextPage));
  };

  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  useEffect(() => {
    dispatch(fetchDistributorNotifications(1));
  }, []);

  return (
    <Flex direction={"column"} gap={"sm"} pb={"xl"}>
      <Title order={5}>Notifikasi</Title>
      <InfiniteScrollWrapper>
        {notificationResponse && notifications && notifications.length > 0 ? (
          <InfiniteScroll
            className="infinite-scroll"
            dataLength={notifications.length}
            hasMore={notifications.length < notificationResponse.totalItems}
            next={handleNextPage}
            loader={
              <Text c={"text-light"} ta={"center"} py={"md"}>
                Loading more notifications...
              </Text>
            }
            endMessage={
              <Text
                c="text-light"
                ta={"center"}
                py={"md"}
                onClick={handleScrollToTop}
                className="end-of-requests"
              >
                End of notifications
              </Text>
            }
          >
            {notifications.map((notif, index) => (
              <NotificationCard key={notif.id || index} {...notif} />
            ))}
          </InfiniteScroll>
        ) : (
          <Text c={"text-light"} py={"lg"}>
            Belum ada notifikasi
          </Text>
        )}
      </InfiniteScrollWrapper>
    </Flex>
  );
};

export default NotificationList;
