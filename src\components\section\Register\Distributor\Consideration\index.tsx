import { DistributorRegisterState } from "@/store/slices/distributorRegisterSlice";
import { Warning } from "@/utils/icons";
import { Title, Text, Box, Alert } from "@mantine/core";
import classes from "../Order/DetailCard/style.module.css";

interface ConsiderationProps {
  role?: "distributor" | "agent";
  status?: DistributorRegisterState["status"];
}

const Consideration: React.FC<ConsiderationProps> = ({
  role = "distributor",
  status = "considered",
}) => {
  return (
    <Box ta="center" h="50dvh">
      <Title order={2} my="lg">
        {status === "considered"
          ? "Aplikasimu Sedang Dipertimbangkan!"
          : "Aplikasimu Masuk ke Waitlist Kami!"}
      </Title>
      <Text>
        Terima kasih atas antusianme Anda untuk bergabung sebagai {role} resmi
        kami.
      </Text>
      <Text>
        {status === "considered"
          ? "Kami akan segera mereview aplikasi Anda dan memberikan balasan via email dalam maksimal 5 hari kerja."
          : `Saat ini, kuota distributor kami sedang penuh. Kami akan segera menghubungi Anda jika ada slot untuk menjadi ${role} kami.`}
      </Text>
      {role === "distributor" && status === "considered" && (
        <Alert
          ta="start"
          mt="xl3"
          classNames={classes}
          variant="light"
          title="Pesanan Anda akan diproses setelah aplikasi Anda diterima!"
          icon={<Warning size={24} />}
        >
          Jika aplikasi Anda ditolak, pesanan Anda akan dibatalkan.
        </Alert>
      )}
    </Box>
  );
};

export default Consideration;
