import RequestCard from "@/components/common/RequestCard";
import { RootState } from "@/store";
import { Divider, Flex, Text, Title } from "@mantine/core";
import { useSelector } from "react-redux";

const RequestSent = () => {
  const { outRequests } = useSelector((state: RootState) => state.request);
  return (
    <Flex direction={"column"} w={"100%"} gap={"md"} pb={"md"}>
      <Title order={5}>Request Dikirim</Title>
      <Divider />
      {outRequests.length > 0 ? (
        <Flex direction={"column"} gap={"xs"}>
          {outRequests.map((value, index) => (
            <RequestCard key={index} {...value} readonly={true} />
          ))}
        </Flex>
      ) : (
        <Text c={"text-light"}>Belum ada request yang dikirim</Text>
      )}
    </Flex>
  );
};

export default RequestSent;
