import { Stack, Stepper, Title } from "@mantine/core";
import { useMemo, useState } from "react";
import ComplaintResolution from "./Resolution";

interface NotReceivedProps {
  onClose?: () => void;
}

const NotReceived = ({ onClose }: NotReceivedProps) => {
  const [active, setActive] = useState(0);
  const STEPS = useMemo(
    () => [
      {
        label: "Resolusi Komplain",
        step: <ComplaintResolution onClose={onClose} />,
      },
      {
        label: "Bukti Resolusi",
        step: 2,
      },
    ],
    []
  );

  return (
    <Stack gap="xl">
      <Title order={2} ta="center">
        Resolusi Komplain
      </Title>
      <Stepper active={active} styles={{ steps: { minWidth: 0 } }}>
        {STEPS.map(({ label, step }) => (
          <Stepper.Step key={label} label={label}>
            {step}
          </Stepper.Step>
        ))}
      </Stepper>
    </Stack>
  );
};

export default NotReceived;
