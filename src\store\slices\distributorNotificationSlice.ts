import { createSlice } from "@reduxjs/toolkit";
import { AppDispatch } from "..";
import api from "@/lib/axios";
import {
  CREATE_COMPLAINT,
  GET_NOTIFICATIONS,
  GET_NOTIFICATION_COUNT,
} from "@/utils/api-routes";
import { toast } from "sonner";
import { AxiosError } from "axios";
import {
  NotificationData,
  NotificationMetadata,
  NotificationResponse,
} from "@/utils/types/notification";
import { NOTIFICATION_COUNT_VAR, ROLE_VAR } from "@/utils/constants";

export interface DistributorNotificationSliceState {
  loading: boolean;
  error?: string;
  notifications: NotificationData[];
  notificationResponse: NotificationResponse | undefined;
  newNotificationCount: number;
  notifCountKey: string;
  notificationMetadatas: NotificationMetadata[];
}

const initialState: DistributorNotificationSliceState = {
  loading: false,
  error: undefined,
  notifications: [],
  notificationResponse: undefined,
  newNotificationCount: 0,
  notifCountKey: "",
  notificationMetadatas: [],
};

const distributorNotificationSlice = createSlice({
  name: "distributorNotification",
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
      if (action.payload) {
        toast.error(action.payload);
      }
    },
    setNotifications: (state, action) => {
      state.notificationResponse = action.payload;
      if (action.payload.page === 1) {
        state.notifications = [];
        state.notificationMetadatas = action.payload.metadatas;
      }
      state.notifications.push(...action.payload.notifications);
    },
    setNotifCountKey: (state, action) => {
      state.notifCountKey = action.payload;
    },

    updateNotificationCount: (state, action) => {
      if (state.newNotificationCount !== 0) return;

      const localStorageKey = NOTIFICATION_COUNT_VAR(action.payload.role);
      const prevCount = Number(localStorage.getItem(localStorageKey) || 0);

      if (!localStorage.getItem(localStorageKey)) {
        localStorage.setItem(localStorageKey, "0");
      }

      state.newNotificationCount = action.payload.count - prevCount;
    },

    clearNewNotificationCount: (state, action) => {
      const key =
        state.notifCountKey ||
        NOTIFICATION_COUNT_VAR(localStorage.getItem(ROLE_VAR) || "");
      if (key) localStorage.setItem(key, String(action.payload));
      state.newNotificationCount = 0;
    },
  },
});

export const fetchDistributorNotifications =
  (page: number, pageSize: number = 10, orderBy: "asc" | "desc" = "desc") =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(distributorNotificationSlice.actions.setLoading(true));
      const res = await api.get(GET_NOTIFICATIONS(page, pageSize, orderBy));
      if (res && res.data) {
        dispatch(
          distributorNotificationSlice.actions.setNotifications(res.data.data)
        );
        dispatch(
          distributorNotificationSlice.actions.clearNewNotificationCount(
            res.data.data.totalItems
          )
        );
      }
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        dispatch(
          distributorNotificationSlice.actions.setError(
            error.response?.data.message
          )
        );
      }
    } finally {
      dispatch(distributorNotificationSlice.actions.setLoading(false));
    }
  };

export const fetchNotificationCount =
  (role: string) => async (dispatch: AppDispatch) => {
    try {
      const res = await api.get(GET_NOTIFICATION_COUNT);
      if (res && res.data) {
        const notifCountData = {
          count: res.data.data.count,
          role: role,
        };
        dispatch(
          distributorNotificationSlice.actions.updateNotificationCount(
            notifCountData
          )
        );
        dispatch(
          distributorNotificationSlice.actions.setNotifCountKey(
            NOTIFICATION_COUNT_VAR(role)
          )
        );
      }
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        dispatch(
          distributorNotificationSlice.actions.setError(
            error.response?.data.message
          )
        );
      }
    }
  };

export const createComplaint =
  (complaint: string, requestId: number, onSuccess?: () => void) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(distributorNotificationSlice.actions.setLoading(true));
      const res = await api.patch(CREATE_COMPLAINT(requestId), {
        complain: complaint,
      });
      if (res) {
        toast("Complaint berhasil diajukan!");
        dispatch(fetchDistributorNotifications(1));
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        dispatch(
          distributorNotificationSlice.actions.setError(
            error.response?.data.message
          )
        );
      } else {
        dispatch(
          distributorNotificationSlice.actions.setError(
            "Failed to create complaint"
          )
        );
      }
    } finally {
      dispatch(distributorNotificationSlice.actions.setLoading(false));
    }
  };
export default distributorNotificationSlice.reducer;
