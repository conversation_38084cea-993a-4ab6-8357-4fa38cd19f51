import api from "@/lib/axios";
import { pushHistory } from "@/utils/common-functions";
import { ADMIN_AGENTS_SEARCH_HISTORY_VAR } from "@/utils/constants";
import { AgentCardDataSimple, ProvinceAgents } from "@/utils/types";
import { createSlice } from "@reduxjs/toolkit";
import { AppDispatch } from "..";
import {
  GET_AGENTS_BY_SEARCH,
  GET_ISLAND_PROVINCES,
  GET_ISLANDS,
} from "@/utils/api-routes";
import { toast } from "sonner";

const isBrowser = typeof window !== "undefined";

const getSearchHistoryFromStorage = (): string[] | undefined => {
  if (!isBrowser) return undefined;
  const storedHistory = localStorage.getItem(ADMIN_AGENTS_SEARCH_HISTORY_VAR);
  return storedHistory ? JSON.parse(storedHistory) : undefined;
};

interface AdminAgentListState {
  islands: string[];
  provinces: ProvinceAgents[];
  searchResult: AgentCardDataSimple[];
  searchHistory: string[] | undefined;
  error: string | undefined;
  loading: boolean;
}

const initialState: AdminAgentListState = {
  islands: [],
  provinces: [],
  searchResult: [],
  searchHistory: getSearchHistoryFromStorage(),
  error: undefined,
  loading: false,
};

const adminAgentListSlice = createSlice({
  name: "adminAgentList",
  initialState,
  reducers: {
    setError(state, action, toastError?: boolean) {
      state.error = action.payload;
      if (toastError) {
        toast.error(action.payload);
      }
    },
    setLoading(state, action) {
      state.loading = action.payload;
    },
    updateHistory(state, action) {
      const updatedHistory = pushHistory(
        state.searchHistory || [],
        action.payload,
        ADMIN_AGENTS_SEARCH_HISTORY_VAR
      );
      state.searchHistory = updatedHistory;
    },
    setIslands(state, action) {
      state.islands = action.payload;
    },
    setProvinces(state, action) {
      state.provinces = action.payload;
    },
    setSearchResults(state, action) {
      state.searchResult = action.payload;
    },
  },
});

export const fetchIslands = () => async (dispatch: AppDispatch) => {
  try {
    dispatch(adminAgentListSlice.actions.setLoading(true));
    const res = await api.get(GET_ISLANDS);
    dispatch(adminAgentListSlice.actions.setIslands(res.data.data.islands));
  } catch (error: unknown) {
    if (error instanceof Error) {
      dispatch(adminAgentListSlice.actions.setError(error.message));
    } else {
      dispatch(adminAgentListSlice.actions.setError("Error fetching islands"));
    }
  } finally {
    dispatch(adminAgentListSlice.actions.setLoading(false));
  }
};

export const fetchProvincesByIsland =
  (island: string) => async (dispatch: AppDispatch) => {
    try {
      dispatch(adminAgentListSlice.actions.setLoading(true));
      const res = await api.get(GET_ISLAND_PROVINCES(island));
      dispatch(
        adminAgentListSlice.actions.setProvinces(res.data.data.provinces)
      );
    } catch (error: unknown) {
      if (error instanceof Error) {
        dispatch(adminAgentListSlice.actions.setError(error.message));
      } else {
        dispatch(
          adminAgentListSlice.actions.setError("Error fetching provinces")
        );
      }
    } finally {
      dispatch(adminAgentListSlice.actions.setLoading(false));
    }
  };

export const fetchAgentsBySearchQuery =
  (search: string, recordHistory: boolean = false) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(adminAgentListSlice.actions.setLoading(true));
      const res = await api.get(GET_AGENTS_BY_SEARCH(search));
      if (recordHistory) {
        dispatch(adminAgentListSlice.actions.updateHistory(search));
      }
      dispatch(
        adminAgentListSlice.actions.setSearchResults(res.data.data.agents)
      );
    } catch (error: unknown) {
      if (error instanceof Error) {
        dispatch(adminAgentListSlice.actions.setError(error.message));
      } else {
        dispatch(adminAgentListSlice.actions.setError("Error fetching agents"));
      }
    } finally {
      dispatch(adminAgentListSlice.actions.setLoading(false));
    }
  };

export default adminAgentListSlice.reducer;
