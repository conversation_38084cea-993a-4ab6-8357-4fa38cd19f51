export interface ShopData {
  tokopedia?: string;
  shopee?: string;
  tiktok?: string;
  [key: string]: unknown;
}

export interface HistoriesData {
  qty: number;
  type: string;
  date: string;
  nominal: number;
}

export interface CategoriesData {
  purto: number;
  mame: number;
}

export interface OrderHistoryData {
  histories?: HistoriesData[];
  categories: CategoriesData;
}

export interface UpdateLogData {
  id?: number;
  old: string;
  new: string;
  fieldChanged: string;
  reason?: string;
  status?: number;
  date?: string;
}

export interface AgentDistributorUpdateDataDetails {
  fieldChange: AgentDistributorDetailsData;
  reason: string;
  requestEvidences: string[];
}

export interface AgentDistributorDetailsData {
  id: number;
  slug: string;
  roleName: "distributor" | "agent" | "admin";
  uoc: string;
  name: string;
  status: 0 | 1 | 2; //0: Active 1: Waitlist: 2: Deactivated
  address: string;
  warehouseAddress: string;
  ownerPhone: string;
  // alternative to owner phone
  phoneNumber: string;
  adminPhone: string;
  // alternative to adminPhone
  adminPhoneNumber?: string;
  npwp: string;
  nik: string;
  shop: ShopData;
  // alternative to shop
  shops: ShopData;
  sk: string;
  orderHistory: OrderHistoryData;
  updateLogs: UpdateLogData[];
  email?: string;
  buktiRequests?: string[];
  [key: string]: unknown;
}

export interface ForceUpdateData {
  fieldsToUpdate: AgentDistributorDetailsForceUpdateData;
  reason: string;
  requestEvidences: string[];
}

export interface AgentDistributorDetailsForceUpdateData {
  name?: string;
  email?: string;
  phoneNumber?: string;
  adminPhoneNumber?: string;
  nik?: string;
  npwp?: string;
  island?: string;
  province?: string;
  city?: string;
  district?: string;
  address?: string;
  warehouseAddress?: string;
  SK?: string;
  shops?: ShopData;
  status?: number;
  reason?: string;
  [key: string]: unknown;
}

export interface AgentDistributorDetailsResponse {
  status_code: number;
  message: string;
  data: AgentDistributorDetailsData;
}

export interface AgentListData {
  agents: DistributorAgents[];
}

export interface DistributorAgents {
  uoc: string;
  name: string;
  status: 0 | 1 | 2; //0: Active 1: Waitlist: 2: Deactivated
  address: string;
  phone: string;
  adminPhone: string;
  joinedAt: string;
  lastOrderDate: string;
  lastOrderItem: string;
}

export interface AgentListResponse {
  status_code: number;
  message: string;
  data: AgentListData;
}

export interface SearchAgentListResponse {
  status_code: number;
  message: string;
  data: DistributorAgents[];
}

export interface AgentDistributorErrors {
  name?: string;
  email?: string;
  phoneNumber?: string;
  adminPhoneNumber?: string;
  nik?: string;
  npwp?: string;
  address?: string;
  warehouseAddress?: string;
  SK?: string;
  tokopedia?: string;
  shopee?: string;
}

export interface UserAgentDetail {
  id: number;
  roleName: "distributor" | "agent" | "admin";
  uoc: string;
  name: string;
  status: 0 | 1 | 2; //0: Active 1: Waitlist: 2: Deactivated
  address: string;
  warehouseAddress: string;
  ownerPhone: string;
  // alternative to owner phone
  phoneNumber: string;
  adminPhone: string;
  // alternative to adminPhone
  adminPhoneNumber?: string;
  npwp: string;
  nik: string;
  shop: ShopData;
  // alternative to shop
  shops: ShopData;
  sk: string;
  email?: string;
  role?: UserRole;
}

export interface UserRole {
  id: number;
  name: "distributor" | "agent" | "admin";
  description: string;
}
export interface SubmitReceiveAgentResponse {
  status_code: number;
  message: string;
}

export interface SubmitReceiveAgentPayload {
  bankType: string;
  bankAccountNumber: string;
}

export interface WaitlistData {
  kuota: number;
  radius: number;
  users: ApplicantData[];
}

export interface ApplicantData {
  id: number;
  name: string;
  slug: string;
  email: string;
  phoneNumber: string;
  adminPhoneNumber: string;
  nik: string;
  npwp: string;
  ktp: string;
  selfieKtp: string;
  island: string;
  province: string;
  city: string;
  district: string;
  kelurahan: string;
  uniqueUserCode: string;
  roleId: number;
  passwordHash: string;
  warehouseAddress: string;
  createdAt: string;
  updatedAt: string;
  role: {
    id: number;
    name: string;
    createdAt: string;
    updatedAt: string;
  };
  sk: string;
  shops: {
    tokopedia: string;
    shopee: string;
    tiktok: string;
  };
  status: number;
  bankType: string;
  bankAccountNumber: string;
  isReceivingAgent: boolean;
  latitude: number;
  longitude: number;
  isWaitlisted: boolean;
  postalCode: string;
  warehouseIsland: string;
  warehouseProvince: string;
  warehouseCity: string;
  warehouseDistrict: string;
  warehouseKelurahan: string;
  warehousePostalCode: string;
}
