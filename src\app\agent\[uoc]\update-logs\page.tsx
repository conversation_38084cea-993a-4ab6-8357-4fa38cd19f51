"use client";

import PageLayout from "@/components/common/PageLayout";
import { Box, Text } from "@mantine/core";
import { DistributorDetailsProps } from "../../../admin/agent/[uoc]/page";
import { use, useEffect } from "react";
import UpdateLogsDetails from "@/components/section/AdminAgentDistributor/Details/UpdateLogs/Details";
import { withAuth } from "@/utils/withAuth";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { fetchAdminAgentDetails } from "@/store/slices/adminAgentDetailsSlice";
import { notFound } from "next/navigation";
import { useMounted } from "@mantine/hooks";

const UpdateLogsPage = ({ params }: DistributorDetailsProps) => {
  const mounted = useMounted();
  const { uoc } = use(params);
  const dispatch: AppDispatch = useDispatch();
  const { data, error } = useSelector(
    (state: RootState) => state.adminAgentDetails
  );

  useEffect(() => {
    dispatch(fetchAdminAgentDetails(uoc));
  }, [uoc, dispatch]);

  if (error && mounted) notFound();

  return (
    <PageLayout
      title="Agent & Distributor"
      navigationContent={{
        title: (
          <Box>
            <Text c="text">{data?.name}</Text>
            <Text fw={500} size="xl">
              Update Logs
            </Text>
          </Box>
        ),
        backUrl: `/agent/${uoc}`,
      }}
    >
      <UpdateLogsDetails uoc={uoc} />
    </PageLayout>
  );
};

export default withAuth(UpdateLogsPage);
