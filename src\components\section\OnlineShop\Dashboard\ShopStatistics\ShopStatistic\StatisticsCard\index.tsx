import { Paper, Skeleton, Stack, Text } from "@mantine/core";

const StatisticCard = ({
  label,
  value,
  loading,
}: {
  label: string;
  value: React.ReactNode;
  loading?: boolean;
}) => (
  <Paper p="lg" bg="bg" w="100%">
    <Stack gap={0}>
      {loading ? (
        <Skeleton height={16} width="80px" />
      ) : (
        <Text fw={500} size="sm" c="text-light">
          {label}
        </Text>
      )}
      {loading ? (
        <Skeleton height={24} width="60%" mt="xs" />
      ) : (
        <Text fw={600} size="lg">
          {value}
        </Text>
      )}
    </Stack>
  </Paper>
);

export default StatisticCard;
