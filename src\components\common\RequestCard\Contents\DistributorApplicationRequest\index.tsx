import { RequestCardData, RequestType } from "@/utils/types/request";
import { Divider, Flex, Text, Button } from "@mantine/core";
import RequestCardHeader from "../../RequestCardHeader";
import ReviewStatus from "@/components/common/ReviewStatus";
import useBreakpoints from "@/hooks/useBreakpoints";
import { useDisclosure } from "@mantine/hooks";
import { isEmpty } from "@/utils/common-functions";
import dynamic from "next/dynamic";
const RequestModal = dynamic(() => import("@/components/common/RequestModal"), {
  ssr: false,
});

const DistributorApplicationRequestCard = ({ ...request }: RequestCardData) => {
  const { mobile } = useBreakpoints();
  const [opened, handler] = useDisclosure(false);

  const data = request.metadatas;

  const findFieldValue = (field: string) =>
    data.find((item) => item.field === field)?.newValue ||
    data.find((item) => item.field === field)?.oldValue;

  const bp = findFieldValue("Payment Proof");
  const sk = findFieldValue("SK");

  const getHeaderText = (step: 1 | 2 | 3 | 4 = 1): string => {
    switch (step) {
      case 1:
        return "Pendaftaran Distributor baru! Mencapai tahap 1: Review Aplikasi Distributor baru";
      //NOTE: skip step 2 because it is already combined with step 1
      case 3:
        return "Pendaftaran Distributor baru! Mencapai tahap 2: Review Pembayaran Distributor";
      case 4:
        return "Pendaftaran Distributor baru! Mencapai tahap 3: Pengecekan Surat Kerjasama";
      default:
        return "";
    }
  };

  const getStatusAction = (
    status: "pending" | "accept" | "reject",
    step: 1 | 2 | 3 | 4 = 1
  ): React.ReactNode => {
    const ViewButton = (
      <Button onClick={handler.open} size="sm" fs="normal" fullWidth={mobile}>
        View
      </Button>
    );

    switch (step) {
      case 1:
        switch (status) {
          case "reject":
            return <ReviewStatus status="rejected" />;
          default:
            return ViewButton;
        }
      //NOTE: skip step 2 because it is already combined with step 1
      case 3:
        if (!isEmpty(bp) && status !== "accept") return ViewButton;
        switch (status) {
          case "reject":
            return "Email Pembayaran dikirimkan ulang";
          default:
            return "Email Pembayaran dikirimkan";
        }
      case 4:
        if (!isEmpty(sk) && status !== "accept") return ViewButton;
        switch (status) {
          case "accept":
            return <ReviewStatus status="accepted" />;
          case "reject":
            return "Surat Kerjasama dikirimkan ulang";
          default:
            return "Surat Kerjasama dikirimkan";
        }
      default:
        return null;
    }
  };

  if (isEmpty(data)) return null;

  return (
    <>
      <RequestModal
        opened={opened}
        onClose={handler.close}
        status="accept"
        drawerSize="xl"
        modalSize="xl"
        requestId={request.id}
        role={request.requesterRole as "Agent" | "Distributor"}
        type={RequestType.Application}
        applicationStep={data[0].requestStep}
      />
      <Flex direction="column">
        <RequestCardHeader
          createdAt={request.createdAt}
          leftText={getHeaderText(data[0].requestStep)}
        />
        <Divider />
        <Flex
          direction={{ base: "column", md: "row" }}
          align={{ base: "flex-start", md: "center" }}
          justify="space-between"
          p="lg"
          gap="lg"
        >
          <Flex direction="column" gap="xs">
            <Text fw={500}>{`Nama Distributor: ${data[0].newValue}`}</Text>
          </Flex>
          {!request.readonly && (
            <Flex
              w={{ base: "100%", md: "fit-content" }}
              justify="flex-end"
              fs="italic"
            >
              {getStatusAction(data[0].status, data[0].requestStep)}
            </Flex>
          )}
        </Flex>
      </Flex>
    </>
  );
};

export default DistributorApplicationRequestCard;
