import Icon from "@/components/common/Icon";
import { formatTimeStamp } from "@/utils/common-functions";
import { Grid, Group, Text, Checkbox, Box } from "@mantine/core";

export interface OrderCardHeaderProps {
  invoice: string;
  customer: string;
  date: string;
  checked?: boolean;
  onCheckboxChange?: (checked: boolean) => void;
}

const OrderCardHeader: React.FC<OrderCardHeaderProps> = ({
  invoice,
  customer,
  date,
  checked,
  onCheckboxChange,
}) => {
  return (
    <Grid align="center">
      <Grid.Col span="content">
        <Checkbox
          checked={checked}
          onChange={(e) => onCheckboxChange?.(e.currentTarget.checked)}
        />
      </Grid.Col>
      <Grid.Col span="auto">
        <Group justify="space-between">
          <Box>
            <Text c="text">{invoice}</Text>
            <Group gap="sm">
              <Text size="lg" fw={600}>
                {customer}
              </Text>
              <Icon icon="chat" size={18} />
            </Group>
          </Box>
          <Box>
            <Text c="text" fs="italic">
              Pesanan dibuat
            </Text>
            <Text c="text" fw={500}>
              {formatTimeStamp(date)}
            </Text>
          </Box>
        </Group>
      </Grid.Col>
    </Grid>
  );
};

export default OrderCardHeader;
