import IconButton from "@/components/common/IconButton";
import PreviewImageModal from "@/components/common/PreviewImageModal";
import { downloadFile } from "@/utils/common-functions";
import { Flex, Image, Paper, Text } from "@mantine/core";
import { useState } from "react";

interface ApplicationTransferEvidenceProps {
  evidenceImageUrl?: string;
}

const ApplicationTransferEvidence = ({
  evidenceImageUrl,
}: ApplicationTransferEvidenceProps) => {
  const handleDownload = () => {
    if (evidenceImageUrl) {
      downloadFile(evidenceImageUrl);
    }
  };

  const [previewOpened, setPreviewOpened] = useState(false);

  const handlePreview = () => {
    if (evidenceImageUrl) {
      setPreviewOpened(true);
    }
  };

  return (
    <Paper radius={"8px"} bg={"bg"} p={"md"} w={"100%"}>
      {evidenceImageUrl && (
        <PreviewImageModal
          opened={previewOpened}
          onClose={() => {
            setPreviewOpened(false);
          }}
          imageUrl={evidenceImageUrl}
        />
      )}
      <Flex direction={"column"} w={"100%"} gap={"sm"}>
        <Text fw={600}>Transfer Evidence</Text>
        <Flex justify={"space-between"} px={"xs"}>
          <Text c={"text-light"}>Bukti Transfer:</Text>
          <Flex
            gap={"sm"}
            align={"center"}
            style={{
              opacity: 0.5,
            }}
          >
            <IconButton icon="eye/show" onClick={handlePreview} size={24} />
            <IconButton
              icon="action/download"
              onClick={handleDownload}
              size={20}
            />
          </Flex>
        </Flex>

        <Paper w={"100%"} h={"480px"} radius={"8px"} bg={"bg-gray"}>
          <Flex w={"100%"} h={"100%"} justify={"center"} align={"center"}>
            {evidenceImageUrl ? (
              <Image
                width={"100%"}
                height={"100%"}
                fit="contain"
                src={evidenceImageUrl}
                alt="evidence"
              />
            ) : (
              <Text ta={"center"} c={"text-light"}>
                Oops! Bukti Transfer tidak ditemukan
              </Text>
            )}
          </Flex>
        </Paper>
      </Flex>
    </Paper>
  );
};

export default ApplicationTransferEvidence;
