import { RequestCardData, RequestType } from "@/utils/types/request";
import { Divider, Flex, Text, Button } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { useState } from "react";
import ReviewStatus from "@/components/common/ReviewStatus";
import RequestModal from "@/components/common/RequestModal";
import RequestCardHeader from "../../RequestCardHeader";

const AgentReapplicationRequestCard = ({ ...request }: RequestCardData) => {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [openedModal, setOpenedModal] = useState(false);

  return (
    <Flex direction={"column"}>
      <RequestModal
        opened={openedModal}
        onClose={() => setOpenedModal(false)}
        requestId={request.id}
        type={RequestType.ChangeDistributor}
        status={"accept"} // set as accept, can be reject via the modal
        drawerSize="90vh"
        role={"Agent"}
      />
      <RequestCardHeader
        leftText="Pendaftaran Agent yang dipindahkan. Review Aplikasi Agent!"
        createdAt={request.createdAt}
      />
      <Divider />
      {request.metadatas[0] ? (
        <Flex
          direction={isMobile ? "column" : "row"}
          align={isMobile ? "flex-start" : "center"}
          justify={"space-between"}
          p={"lg"}
          gap={"lg"}
        >
          <Text fw={500}>{`Nama: ${request.metadatas[0].oldValue}`}</Text>
          <Flex w={isMobile ? "100%" : "fit-content"} justify={"flex-end"}>
            {request.metadatas[0].status === "pending" && (
              <Button onClick={() => setOpenedModal(true)}>View</Button>
            )}

            {(request.metadatas[0].status === "accept" ||
              request.metadatas[0].status === "reject") && (
              <ReviewStatus status={`${request.metadatas[0].status}`} />
            )}
          </Flex>
        </Flex>
      ) : (
        <Text c={"text-light"} p={"lg"} fw={500}>
          Tidak ada detail data
        </Text>
      )}
    </Flex>
  );
};

export default AgentReapplicationRequestCard;
