import { UpdateLogData } from "@/utils/types/distributor-agent";
import { Stack, Text, Divider, Box, Group, Button } from "@mantine/core";
import UpdateLog from "../../UpdateLog";
import Link from "next/link";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

export interface UpdateLogsProps {
  data: UpdateLogData[];
  uoc: string;
}

const UpdateLogs: React.FC<UpdateLogsProps> = ({ data, uoc }) => {
  const { role } = useSelector((state: RootState) => state.auth);

  return (
    <Stack gap="xs">
      <Group justify="space-between" align="center">
        <Box>
          <Text size="xl" fw={600}>
            Update Logs
          </Text>
          <Text c="text">Menampilkan 3 update terbaru</Text>
        </Box>
        <Button
          component={Link}
          href={`${role === "admin" ? "/admin" : ""}/agent/${uoc}/update-logs`}
          color="text-light"
          variant="subtle"
        >
          See all
        </Button>
      </Group>
      <Divider size="sm" />
      {data?.length > 0 ? (
        <Stack>
          {data.map((log) => (
            <UpdateLog key={log.id} log={log} />
          ))}
        </Stack>
      ) : (
        <Box>
          <Text>Belum ada perubahan</Text>
        </Box>
      )}
    </Stack>
  );
};

export default UpdateLogs;
