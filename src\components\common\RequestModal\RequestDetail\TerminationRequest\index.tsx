import Icon from "@/components/common/Icon";
import RequestOrderHistories from "@/components/section/AdminRequests/RequestOrdeHistories";
import { AppDispatch, RootState } from "@/store";
import { fetchRequestDetail } from "@/store/slices/adminRequestSlice";
import { RequestType } from "@/utils/types/request";
import { Anchor, Flex, Text } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

interface RequestDetailContentProps {
  requestId: number;
}
const TerminationRequestDetail = ({ requestId }: RequestDetailContentProps) => {
  const dispatch: AppDispatch = useDispatch();
  const { terminationRequest } = useSelector(
    (state: RootState) => state.adminRequest
  );

  useEffect(() => {
    dispatch(fetchRequestDetail(requestId, RequestType.Termination));
  }, [requestId]);

  const isMobile = useMediaQuery("(max-width: 768px)");
  return (
    <>
      {terminationRequest && (
        <>
          <Flex
            direction={"column"}
            justify={"center"}
            align={"center"}
            gap={"xs"}
          >
            <Text fw={600}>{`Distributor '${
              terminationRequest.sourceAgent || "-"
            }'`}</Text>
            <Icon icon="arrow/up-down" />
            <Text
              fw={600}
            >{`Agent '${terminationRequest.terminatedAgent}'`}</Text>
            <Text>{`Alasan: ${terminationRequest.reason}`}</Text>
          </Flex>
          {terminationRequest.evidence && (
            <Flex
              gap={"4px"}
              align={"center"}
              w={"100%"}
              ta={"center"}
              justify={"center"}
            >
              <Text fw={500}>{"File tambahan: "}</Text>
              <Anchor href={terminationRequest.evidence} fw={500}>
                {terminationRequest.evidence}
              </Anchor>
            </Flex>
          )}
          <Flex
            mih={isMobile ? "33vh" : "fit-content"}
            mah={"33vh"}
            style={{ overflowY: "scroll" }}
            w={"100%"}
          >
            <RequestOrderHistories
              orderHistories={terminationRequest.orderHistory}
            />
          </Flex>
        </>
      )}
    </>
  );
};

export default TerminationRequestDetail;
