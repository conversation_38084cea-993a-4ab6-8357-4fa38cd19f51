import { useForm, Controller } from "react-hook-form";
import { AgentApplicationFormProps, AgentApplicationModalProps } from "..";
import useBreakpoints from "@/hooks/useBreakpoints";
import {
  Button,
  Checkbox,
  Group,
  Stack,
  TextInput,
  Title,
  Text,
  Select,
} from "@mantine/core";
import { BANK_TYPES } from "@/utils/constants";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

export interface AgentApplicationModalContentProps {
  onClose: AgentApplicationModalProps["onClose"];
  onSubmit: AgentApplicationModalProps["onSubmit"];
}

const banks = BANK_TYPES.map(({ name }) => name);

const schema = yup.object().shape({
  bankType: yup
    .string()
    .required("Bank tujuan harus dipilih")
    .oneOf(banks, "Bank tujuan harus dipilih"),
  bankAccountNumber: yup
    .string()
    .required("Nomor rekening harus diisi")
    .matches(/^\d+$/, "Nomor rekening tidak valid")
    .min(8, "Nomor rekening minimal 8 digit")
    .max(20, "Nomor rekening maksimal 20 digit"),
  confirmation: yup
    .boolean()
    .required("Anda harus menyetujui persyaratan ini")
    .oneOf([true], "Anda harus menyetujui persyaratan ini"),
});

const ModalContent: React.FC<AgentApplicationModalContentProps> = ({
  onClose,
  onSubmit,
}) => {
  const { mobile } = useBreakpoints();

  const { loading } = useSelector(
    (state: RootState) => state.distributorAgents
  );

  const form = useForm<AgentApplicationFormProps>({
    mode: "onChange",
    resolver: yupResolver(schema),
    defaultValues: { bankType: "", bankAccountNumber: "", confirmation: false },
  });

  const formSubmit = (data: AgentApplicationFormProps) => {
    if (data.confirmation) {
      onSubmit({
        bankType: data.bankType,
        bankAccountNumber: data.bankAccountNumber,
      });
    }
  };

  return (
    <>
      <Stack gap="xs" ta="center">
        <Title order={2}>Lengkapi Application Form</Title>
        <Text c="text">
          Anda harus memasukkan nomor rekening Anda sebagai tujuan pembayaran
          Agent.
        </Text>
      </Stack>
      <Stack component="form" mt="lg" onSubmit={form.handleSubmit(formSubmit)}>
        <Controller
          name="bankType"
          control={form.control}
          render={({ field }) => (
            <Select
              {...field}
              onChange={(value) => {
                form.setValue("bankType", value!);
              }}
              error={form.formState.errors.bankType?.message}
              required
              searchable
              clearable
              checkIconPosition="right"
              label="Bank Tujuan"
              data={banks}
            />
          )}
        />
        <TextInput
          required
          {...form.register("bankAccountNumber")}
          error={form.formState.errors.bankAccountNumber?.message}
          label="Nomor Rekening"
        />
        <Checkbox
          {...form.register("confirmation")}
          error={form.formState.errors.confirmation?.message}
          label="Saya memahami hubungan kerjasama Agent dengan saya sepenuhnya di luar kendali Hotto pusat."
          description="Jika terjadi perselisihan antara 2 pihak, akan diselesaikan oleh kalian berdua????"
          mt="md"
        />
        <Group mt="sm" justify="center">
          <Button
            fullWidth={mobile}
            onClick={onClose}
            variant="subtle"
            c="text-light"
          >
            Kembali
          </Button>
          <Button fullWidth={mobile} type="submit" loading={loading}>
            Simpan
          </Button>
        </Group>
      </Stack>
    </>
  );
};

export default ModalContent;
