import { Divider, <PERSON>lex, <PERSON>, Button } from "@mantine/core";
import RequestCardHeader from "../../RequestCardHeader";
import { RequestCardData, RequestType } from "@/utils/types/request";
import { useMediaQuery } from "@mantine/hooks";
import { useState } from "react";
import ReviewStatus from "@/components/common/ReviewStatus";
import RequestModal from "@/components/common/RequestModal";

const AgentStatusReactivationContent = ({ ...request }: RequestCardData) => {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [selectedStatus, setSelectedStatus] = useState<"accept" | "reject">(
    "accept"
  );
  const [openedModal, setOpenedModal] = useState(false);

  const handleAcceptRequest = () => {
    setSelectedStatus("accept");
    setOpenedModal(true);
  };

  const handleRejectRequest = () => {
    setSelectedStatus("reject");
    setOpenedModal(true);
  };

  return (
    <Flex direction={"column"}>
      <RequestModal
        opened={openedModal}
        onClose={() => setOpenedModal(false)}
        requestId={request.id}
        status={selectedStatus}
        type={RequestType.Reactivation}
      />
      <RequestCardHeader
        createdAt={request.createdAt}
        leftText={`${request.requesterName} ingin mengaktifkan ulang status`}
        rightText={"Last purchase"}
        updatedAt={request.updatedAt}
      />
      <Divider />
      {request.metadatas && (
        <Flex
          direction={isMobile ? "column" : "row"}
          align={isMobile ? "flex-start" : "center"}
          gap={"lg"}
          justify={"space-between"}
          p={"lg"}
        >
          <Text fw={500}>{`Jumlah beli: ${request.metadatas[0].field}`}</Text>
          <Flex w={isMobile ? "100%" : "fit-content"} justify={"flex-end"}>
            {request.metadatas[0].status === "pending" ? (
              <Flex
                direction={"row"}
                gap={"md"}
                w={isMobile ? "100%" : "fit-content"}
                justify={"center"}
              >
                <Button variant="subtle" onClick={handleRejectRequest}>
                  Reject
                </Button>
                <Flex w={isMobile ? "33%" : "fit-content"}>
                  <Button onClick={handleAcceptRequest}>Accept</Button>
                </Flex>
              </Flex>
            ) : (
              <ReviewStatus status={request.metadatas[0].status} />
            )}
          </Flex>
        </Flex>
      )}
    </Flex>
  );
};

export default AgentStatusReactivationContent;
