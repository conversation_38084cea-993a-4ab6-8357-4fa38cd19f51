import ButtonGroup from "@/components/common/ButtonGroup";
import ResponsiveModal from "@/components/common/ResponsiveModal";
import useBreakpoints from "@/hooks/useBreakpoints";
import { isEmpty } from "@/utils/common-functions";
import { Stack, Title, Paper, Text, TextInput, Button } from "@mantine/core";
import { useEffect, useState } from "react";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { approveOrder } from "@/store/slices/onlineShopSlices/orderListSlice";

export interface OrderResiInputModalProps {
  opened: boolean;
  onClose: () => void;
  data: {
    id: number | string;
    resiNumber?: string;
  };
}

const OrderResiInputModal: React.FC<OrderResiInputModalProps> = ({
  opened,
  onClose,
  data: { id, resiNumber = "" },
}) => {
  const dispatch: AppDispatch = useDispatch();
  const { mobile } = useBreakpoints();
  const { loading } = useSelector((state: RootState) => state.orderList);
  const [resi, setResi] = useState(resiNumber);

  useEffect(() => {
    setResi(resiNumber);
  }, [resiNumber]);

  const handleClose = () => {
    setResi(resiNumber);
    onClose();
  };

  const handleSubmit = () => {
    dispatch(
      approveOrder(
        id,
        {
          isApproved: true,
          resiNumber: resi,
        },
        "resiNumber"
      )
    ).then(handleClose);
  };

  return (
    <ResponsiveModal
      opened={opened}
      onClose={handleClose}
      modalProps={{ padding: "lg" }}
      drawerProps={{ padding: "lg" }}
    >
      <Stack
        component="form"
        onSubmit={(e) => {
          e.preventDefault();
          handleSubmit();
        }}
      >
        <Title order={3} ta="center">
          {isEmpty(resiNumber) ? "Input" : "Edit"} Nomor Resi
        </Title>
        <TextInput
          required
          name="resi"
          label="Nomor Resi"
          placeholder="Masukan nomor resi"
          value={resi}
          onChange={(e) => setResi(e.target.value)}
        />
        <Paper mx="-lg" bg="error.1" px="lg" py="sm" radius="8px 8px 0 0">
          <Text c="error" fw={500} fz="0.9rem">
            Pastikan Anda memasukan nomor resi yang tepat!
          </Text>
        </Paper>
        <ButtonGroup justify="center" w="100%">
          <Button
            fullWidth={mobile}
            color="default"
            variant="subtle"
            onClick={handleClose}
          >
            Kembali
          </Button>
          <Button
            fullWidth={mobile}
            disabled={isEmpty(resi)}
            loading={loading.approve}
            type="submit"
          >
            Submit
          </Button>
        </ButtonGroup>
      </Stack>
    </ResponsiveModal>
  );
};

export default OrderResiInputModal;
