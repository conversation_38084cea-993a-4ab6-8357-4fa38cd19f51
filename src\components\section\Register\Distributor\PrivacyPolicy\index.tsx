"use client";

import { Paper, Text, Stack, List, Box, ScrollArea } from "@mantine/core";
import { useInViewport } from "@mantine/hooks";
import { useEffect } from "react";

export interface PrivacyPolicyProps {
  onRead: (read: boolean) => void;
}

const COLLECTED_INFO = [
  "Informasi Identitas: Nama, alamat email, nomor telepon, dan informasi demografis lainnya.",
  "Informasi Akun: <PERSON>a pengguna, kata sandi, dan informasi akun lainnya.",
  "Informasi Pembayaran: Informasi pembayaran lainnya, dan detail transaksi.",
  "Informasi Aktivitas: Aktivitas Anda di Layanan kami, seperti halaman yang Anda kunjungi, waktu yang Anda habiskan di setiap halaman, dan tindakan yang Anda ambil.",
  "Informasi Perangkat: Alamat IP Anda, jenis browser dan perangkat yang Anda gunakan, dan informasi perangkat lainnya.",
  "Informasi Konten: Konten yang Anda buat dan unggah ke Layanan kami, seperti kode, komentar, dan postingan forum.",
  "Informasi Media Sosial: Informasi dari akun media sosial Anda yang Anda pilih untuk dibagikan dengan kami, seperti nama, gambar, dan daftar teman.",
];

const HOW_INFO_COLLECTED = [
  "Ketika Anda membuat akun dan mendaftar untuk Layanan kami. Kami meminta informasi identitas, informasi akun, dan informasi pembayaran dari Anda saat Anda membuat akun.",
  "Ketika Anda menggunakan Layanan kami. Kami mengumpulkan informasi tentang aktivitas Anda di Layanan kami, seperti halaman yang Anda kunjungi, waktu yang Anda habiskan di setiap halaman, dan tindakan yang Anda ambil. Kami juga mengumpulkan informasi tentang perangkat Anda dan konten yang Anda buat dan unggah ke Layanan kami.",
  "Ketika Anda menghubungi kami untuk mendapatkan dukungan. Kami mengumpulkan informasi identitas, informasi kontak, dan informasi tentang masalah yang Anda alami saat Anda menghubungi kami untuk mendapatkan dukungan.",
  "Ketika Anda berpartisipasi dalam survei atau kontes. Kami mengumpulkan informasi identitas dan informasi kontak dari Anda saat Anda berpartisipasi dalam survei atau kontes.",
  "Ketika Anda menggunakan fitur media sosial di Layanan kami. Kami mengumpulkan informasi dari akun media sosial Anda yang Anda pilih untuk dibagikan dengan kami, seperti nama, gambar, dan daftar teman.",
];

const PrivacyPolicy: React.FC<PrivacyPolicyProps> = ({ onRead }) => {
  const { ref, inViewport } = useInViewport();

  useEffect(() => {
    if (inViewport) {
      onRead(true);
    }
  }, [inViewport]);

  // TODO: change privacy policy to hotto's

  return (
    <Paper
      withBorder
      p="lg"
      bg="bg"
      ta={{ sm: "justify" }}
      style={{ borderColor: "var(--mantine-color-gray-5)" }}
    >
      <ScrollArea
        h={500}
        offsetScrollbars
        scrollbarSize={4}
        scrollHideDelay={0}
      >
        <Stack>
          <Text fw={700} fz="h3">
            Kebijakan Privasi Hotto
          </Text>
          <Box>
            <Text fw={600}>Pendahuluan</Text>
            <Text>
              Coding Studio berkomitmen untuk melindungi privasi pengguna kami.
              Kebijakan Privasi ini menjelaskan bagaimana kami mengumpulkan,
              menggunakan, dan mengungkapkan informasi pribadi Anda. Kebijakan
              ini berlaku untuk situs web Coding Studio, aplikasi seluler, dan
              layanan lainnya yang kami tawarkan (secara kolektif, “Layanan”).
            </Text>
          </Box>
          <Box>
            <Text fw={600}>Informasi yang Kami Kumpulkan</Text>
            <Text>
              Kami mengumpulkan berbagai jenis informasi pribadi dari Anda,
              termasuk:
            </Text>
            <List withPadding mr="lg">
              {COLLECTED_INFO.map((info, index) => (
                <List.Item key={index}>{info}</List.Item>
              ))}
            </List>
          </Box>
          <Box>
            <Text fw={600}>Bagaimana Kami Mengumpulkan Informasi</Text>
            <Text>
              Kami mengumpulkan berbagai jenis informasi pribadi dari Anda,
              termasuk:
            </Text>
            <List withPadding mr="lg">
              {HOW_INFO_COLLECTED.map((info, index) => (
                <List.Item key={index}>{info}</List.Item>
              ))}
            </List>
          </Box>
        </Stack>
        <Box h={20} ref={ref} />
      </ScrollArea>
    </Paper>
  );
};

export default PrivacyPolicy;
