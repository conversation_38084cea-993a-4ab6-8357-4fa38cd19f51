"use client";

import PageLayout from "@/components/common/PageLayout";
import ProductDetailForm from "@/components/section/OnlineShop/ProductDetailForm";
import { withAuth } from "@/utils/withAuth";

const AdminCreateProductDetalPage = () => {
  return (
    <PageLayout title="Produk" navigationContent={{ title: "Tambah Produk" }}>
      <ProductDetailForm />
    </PageLayout>
  );
};

export default withAuth(AdminCreateProductDetalPage);
