import useBreakpoints from "@/hooks/useBreakpoints";
import { Flex, Stack, Text } from "@mantine/core";
import { useQueryState } from "nuqs";
import FilterButton from "@/components/common/FilterButton";
import DonutChart from "@/components/common/DonutChart";
import { RootState } from "@/store";
import { useSelector } from "react-redux";
import { useEffect } from "react";
import { fetchCustomerStatsOverview } from "@/store/slices/onlineShopSlices/dashboardSlice";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import { numberToString } from "@/utils/common-functions";

const CustomerStatisticsChart = () => {
  const {
    customerStatsOverview,
    loading: { customerStatsOverview: loading },
  } = useSelector((state: RootState) => state.dashboard);
  const dispatch = useDispatch<AppDispatch>();

  const { mobile } = useBreakpoints();
  const [chart, setChart] = useQueryState("chart", {
    defaultValue: "topSpender",
  });

  const handleChart = (value: string) => {
    setChart(value);
  };

  useEffect(() => {
    if (chart && !loading) {
      dispatch(
        fetchCustomerStatsOverview(chart as "topSpender" | "demography")
      );
    }
  }, [chart]);

  const CHART_DATA =
    customerStatsOverview?.topSpenders.map((item) => ({
      name: item.name,
      value: item.totalSpend,
    })) || [];

  const description = numberToString(
    CHART_DATA.reduce((acc, item) => {
      return acc + item.value;
    }, 0)
  );

  return (
    <Stack gap={"xl"} p={"xl"}>
      <Flex gap={"xs"} align={"center"}>
        <Text fw={600} size="sm">
          View Chart:
        </Text>
        {CHART_OPTIONS.map((item) => (
          <FilterButton
            key={item.value}
            item={item}
            activeFilter={chart}
            handleFilter={handleChart}
          />
        ))}
      </Flex>
      <Flex justify={"center"} align={"center"}>
        <DonutChart
          data={CHART_DATA}
          chartLabel={"Total Transaksi"}
          chartDescription={description}
          legendLabel={"Top Spender"}
          legendPosition={mobile ? "bottom" : "right"}
          isPrice
          loading={loading}
        />
      </Flex>
    </Stack>
  );
};

export default CustomerStatisticsChart;

const CHART_OPTIONS = [
  {
    label: "Top Spender",
    value: "topSpender",
  },
  {
    label: "Demografi",
    value: "demography",
  },
];
