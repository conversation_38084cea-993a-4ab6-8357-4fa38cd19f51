"use client";

import Footer from "@/components/common/Footer";
import RegisterForms from "@/components/section/Register/Distributor/Forms";
import useBreakpoints from "@/hooks/useBreakpoints";
import { RootState } from "@/store";
import { Center, Stack, Image } from "@mantine/core";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useSelector } from "react-redux";

const RegisterPage = () => {
  const router = useRouter();
  const { mobile } = useBreakpoints();
  const { status } = useSelector(
    (state: RootState) => state.distributorRegister
  );
  const { role, accessToken } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (accessToken) {
      window.location.href = "/";
      return;
    }

    if (!status) return;

    const redirectMap = {
      waitlisted: "/register/finished?status=waitlisted&role=distributor",
      considered: "/register/distributor/order",
    };

    const redirectUrl = redirectMap[status];
    if (redirectUrl) {
      router.push(redirectUrl);
    }
  }, [accessToken, role, status]);

  return (
    <Center p={{ base: 12, sm: 16, md: 24 }}>
      <Stack w="100%" gap="xl">
        <Image
          radius="md"
          src={`/images/register/hero${mobile ? "-mobile" : ""}.png`}
          alt="hero"
        />
        <RegisterForms />
        <Footer pt={50} />
      </Stack>
    </Center>
  );
};

export default RegisterPage;
