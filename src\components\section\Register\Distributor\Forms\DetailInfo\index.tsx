import LeftSection from "@/components/common/LeftSection";
import { Checkbox, NativeSelect, Stack, TextInput } from "@mantine/core";
import { FormStepperProps } from "..";
import {
  distributorRegisterSchema,
  serializeRegion,
} from "@/utils/common-functions";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { setDetailInfo } from "@/store/slices/distributorRegisterSlice";
import classes from "../../Forms/UserAgreement/style.module.css";
import {
  fetchDistricts,
  fetchIslands,
  fetchProvinces,
  fetchRegencies,
  fetchVillages,
} from "@/store/slices/regionSlice";
import { Phone, Tiktok, Location, Shopee } from "@/utils/icons";

const schema = distributorRegisterSchema.pick([
  "warehouseIsland",
  "warehouseProvince",
  "warehouseRegency",
  "warehouseDistrict",
  "warehouseVillage",
  "warehousePostalCode",
  "warehouseAddress",
  "adminPhoneNumber",
  "tokopedia",
  "shopee",
  "tiktok",
]);

const DetailInfoForm: React.FC<FormStepperProps> = ({
  onFormValid,
  nextClicked,
}) => {
  const dispatch: AppDispatch = useDispatch();
  const region = useSelector((state: RootState) => state.region);
  const { personalInfo, detailInfo } = useSelector(
    (state: RootState) => state.distributorRegister
  );
  const [checked, setChecked] = useState(false);

  const form = useForm({
    mode: "onChange",
    resolver: yupResolver(schema),
    defaultValues: detailInfo,
  });

  useEffect(() => {
    if (nextClicked) form.trigger();
    onFormValid(form.formState.isValid);
  }, [form.formState.isValid, nextClicked]);

  useEffect(() => {
    if (region.islands.length === 0) {
      dispatch(fetchIslands());
    }
  }, []);

  useEffect(() => {
    const subscription = form.watch(() => {
      dispatch(setDetailInfo(form.getValues()));
    });
    return () => subscription.unsubscribe();
  }, []);

  // make sure to refetch region when user come from step 1
  useEffect(() => {
    if (
      personalInfo.island === detailInfo.warehouseIsland &&
      personalInfo.province === detailInfo.warehouseProvince &&
      personalInfo.regency === detailInfo.warehouseRegency &&
      personalInfo.district === detailInfo.warehouseDistrict &&
      personalInfo.village === detailInfo.warehouseVillage &&
      personalInfo.postalCode === detailInfo.warehousePostalCode &&
      personalInfo.address === detailInfo.warehouseAddress
    ) {
      setChecked(true);
    }

    if (
      detailInfo.warehouseIsland ||
      detailInfo.warehouseProvince ||
      detailInfo.warehouseRegency ||
      detailInfo.warehouseDistrict ||
      detailInfo.warehouseVillage
    ) {
      if (
        personalInfo.island !== detailInfo.warehouseIsland ||
        personalInfo.province !== detailInfo.warehouseProvince ||
        personalInfo.regency !== detailInfo.warehouseRegency ||
        personalInfo.district !== detailInfo.warehouseDistrict ||
        personalInfo.village !== detailInfo.warehouseVillage
      ) {
        dispatch(fetchProvinces(detailInfo.warehouseIsland))
          .then(() => fetchRegencies(detailInfo.warehouseProvince))
          .then(() => form.resetField("warehouseRegency"))
          .then(() => dispatch(fetchDistricts(detailInfo.warehouseRegency)))
          .then(() => form.resetField("warehouseDistrict"))
          .then(() => dispatch(fetchVillages(detailInfo.warehouseDistrict)))
          .then(() => {
            form.resetField("warehouseVillage");
            form.resetField("warehousePostalCode");
          });
      }
    }
  }, []);

  const handleSetRegion = (target: React.ChangeEvent<HTMLInputElement>) => {
    const check = target.currentTarget.checked;
    setChecked(check);
    if (check) {
      dispatch(fetchRegencies(personalInfo.province))
        .then(() => dispatch(fetchDistricts(personalInfo.regency)))
        .then(() => dispatch(fetchVillages(personalInfo.district)))
        .then(() => {
          form.setValue("warehouseIsland", personalInfo.island, {
            shouldValidate: true,
          });
          form.setValue("warehouseProvince", personalInfo.province, {
            shouldValidate: true,
          });
          form.setValue("warehouseRegency", personalInfo.regency, {
            shouldValidate: true,
          });
          form.setValue("warehouseDistrict", personalInfo.district, {
            shouldValidate: true,
          });
          form.setValue("warehouseVillage", personalInfo.village, {
            shouldValidate: true,
          });
          form.setValue("warehousePostalCode", personalInfo.postalCode, {
            shouldValidate: true,
          });
          form.setValue("warehouseAddress", personalInfo.address, {
            shouldValidate: true,
          });
        });
    } else {
      resetRegionValue(
        [
          "warehouseIsland",
          "warehouseProvince",
          "warehouseRegency",
          "warehouseDistrict",
          "warehouseVillage",
          "warehousePostalCode",
          "warehouseAddress",
        ],
        true
      );
    }
  };

  const resetRegionValue = (
    names: (
      | "warehouseIsland"
      | "warehouseProvince"
      | "warehouseRegency"
      | "warehouseDistrict"
      | "warehouseVillage"
      | "warehousePostalCode"
      | "warehouseAddress"
    )[],
    shouldValidate?: boolean
  ) => {
    for (const name of names) {
      form.setValue(name, "", {
        shouldValidate: shouldValidate || form.getFieldState(name).isTouched,
      });
    }
  };

  return (
    <Stack w="100%" h="100%" gap="xl">
      <Checkbox
        classNames={classes}
        checked={checked}
        onChange={handleSetRegion}
        label="Lokasi warehouse sama dengan KTP"
        description="Checklist jika alamat warehouse Anda sama dengan alamat KTP"
      />
      <NativeSelect
        {...form.register("warehouseIsland")}
        required
        label="Pulau Domisili KTP"
        disabled={region.islands.length === 0 || checked}
        error={form.formState.errors.warehouseIsland?.message}
        data={[
          { value: "", label: "Select an option..." },
          ...region.islands.map(({ name }) => {
            return { value: name, label: name };
          }),
        ]}
        onChange={(e) => {
          const value = e.target.value;
          form.setValue("warehouseIsland", value);
          form.trigger("warehouseIsland");
          if (value) {
            dispatch(fetchProvinces(value)).then(() => {
              resetRegionValue([
                "warehouseProvince",
                "warehouseRegency",
                "warehouseDistrict",
                "warehouseVillage",
                "warehousePostalCode",
              ]);
            });
          }
        }}
      />
      <NativeSelect
        {...form.register("warehouseProvince")}
        required
        label="Provinsi Warehouse"
        disabled={
          region.filteredProvinces.length === 0 ||
          checked ||
          !form.watch("warehouseIsland")
        }
        error={form.formState.errors.warehouseProvince?.message}
        data={serializeRegion(region.filteredProvinces, true)}
        onChange={(e) => {
          const value = e.target.value;
          form.setValue("warehouseProvince", value);
          form.trigger("warehouseProvince");
          if (value) {
            dispatch(fetchRegencies(value)).then(() => {
              resetRegionValue([
                "warehouseRegency",
                "warehouseDistrict",
                "warehouseVillage",
                "warehousePostalCode",
              ]);
            });
          }
        }}
      />
      <NativeSelect
        {...form.register("warehouseRegency")}
        required
        label="Kabupaten/Kota Warehouse"
        disabled={
          region.regencies.length === 0 ||
          checked ||
          !form.watch("warehouseProvince")
        }
        error={form.formState.errors.warehouseRegency?.message}
        data={serializeRegion(region.regencies, true)}
        onChange={(e) => {
          const value = e.target.value;
          form.setValue("warehouseRegency", value);
          form.trigger("warehouseRegency");
          if (value) {
            dispatch(fetchDistricts(value)).then(() => {
              resetRegionValue([
                "warehouseDistrict",
                "warehouseVillage",
                "warehousePostalCode",
              ]);
            });
          }
        }}
      />
      <NativeSelect
        {...form.register("warehouseDistrict")}
        required
        label="Kecamatan Warehouse"
        disabled={
          region.districts.length === 0 ||
          checked ||
          !form.watch("warehouseRegency")
        }
        error={form.formState.errors.warehouseDistrict?.message}
        data={serializeRegion(region.districts, true)}
        onChange={(e) => {
          const value = e.target.value;
          form.setValue("warehouseDistrict", value);
          form.trigger("warehouseDistrict");
          if (value) {
            dispatch(fetchVillages(value)).then(() => {
              resetRegionValue(["warehouseVillage", "warehousePostalCode"]);
            });
          }
        }}
      />
      <NativeSelect
        {...form.register("warehouseVillage")}
        required
        label="Kelurahan Warehouse"
        disabled={
          region.villages.length === 0 ||
          checked ||
          !form.watch("warehouseDistrict")
        }
        error={form.formState.errors.warehouseVillage?.message}
        data={serializeRegion(region.villages, true)}
        onChange={(e) => {
          const value = e.target.value;
          form.setValue("warehouseVillage", value);
          form.trigger("warehouseVillage");
          if (value) {
            resetRegionValue(["warehousePostalCode"]);
          }
        }}
      />
      <NativeSelect
        {...form.register("warehousePostalCode")}
        required
        label="Kodepos Warehouse"
        disabled={
          region.postalCodes.length === 0 ||
          checked ||
          !form.watch("warehouseVillage")
        }
        error={form.formState.errors.warehousePostalCode?.message}
        data={[
          { value: "", label: "Select an option..." },
          ...region.postalCodes,
        ]}
      />
      <TextInput
        {...form.register("warehouseAddress")}
        error={form.formState.errors.warehouseAddress?.message}
        required
        disabled={checked}
        label="Alamat Lengkap Warehouse"
        leftSectionPointerEvents="none"
        leftSectionWidth={40}
        leftSection={<LeftSection icon={<Location />} />}
      />
      <TextInput
        {...form.register("adminPhoneNumber")}
        error={form.formState.errors.adminPhoneNumber?.message}
        placeholder="08xxxxxxxxxx"
        label="Nomor Telepon Admin"
        leftSectionPointerEvents="none"
        leftSectionWidth={40}
        leftSection={<LeftSection icon={<Phone />} />}
      />
      <TextInput
        {...form.register("tokopedia")}
        error={form.formState.errors.tokopedia?.message}
        label="Link Tokopedia"
        leftSectionPointerEvents="none"
        leftSectionWidth={40}
        leftSection={<LeftSection icon="social/tokopedia" />}
      />
      <TextInput
        {...form.register("shopee")}
        error={form.formState.errors.shopee?.message}
        label="Link Shopee"
        leftSectionPointerEvents="none"
        leftSectionWidth={40}
        leftSection={
          <LeftSection
            icon={<Shopee color="var(--mantine-color-orange-7)" />}
          />
        }
      />
      <TextInput
        {...form.register("tiktok")}
        error={form.formState.errors.tiktok?.message}
        label="Link Tiktok"
        leftSectionPointerEvents="none"
        leftSectionWidth={40}
        leftSection={
          <LeftSection icon={<Tiktok color="var(--mantine-color-black)" />} />
        }
      />
    </Stack>
  );
};

export default DetailInfoForm;
