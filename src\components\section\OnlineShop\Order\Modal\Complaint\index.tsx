import ResponsiveModal from "@/components/common/ResponsiveModal";
import NotReceived from "./NotReceived";

export interface ComplaintModalProps {
  opened: boolean;
  onClose: () => void;
  orderId: string;
}

const ComplaintModal: React.FC<ComplaintModalProps> = ({
  opened,
  onClose,
  orderId,
}) => {
  return (
    <ResponsiveModal
      opened={opened}
      onClose={onClose}
      modalProps={{ size: "lg", padding: "lg" }}
    >
      <NotReceived onClose={onClose} />
    </ResponsiveModal>
  );
};

export default ComplaintModal;
