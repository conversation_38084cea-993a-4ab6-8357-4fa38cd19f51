import { Stack } from "@mantine/core";
import CustomersStatisticsCards from "../../../Statistics/Customers/Cards";
import CustomersStatisticsChart from "../../../Statistics/Customers/Chart";
import CustomersStatisticsTable from "../../../Statistics/Customers/Table";
import { useDetailOverviewContext } from "@/context/DetailOverviewContext";
import { isEmpty } from "@/utils/common-functions";

const DetailCustomerStatistics = () => {
  const { slug } = useDetailOverviewContext();

  if (isEmpty(slug)) return null;

  return (
    <Stack>
      <CustomersStatisticsCards slug={slug} />
      <CustomersStatisticsChart slug={slug} />
      <CustomersStatisticsTable slug={slug} />
    </Stack>
  );
};

export default DetailCustomerStatistics;
