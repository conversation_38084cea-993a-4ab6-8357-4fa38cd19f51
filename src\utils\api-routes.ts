import { parseRegion } from "./common-functions";
import { SortFilterValue } from "./constants";
import {
  CustomerChartCategories,
  OrderStatus,
  SalesChartCategories,
  StatisticsTimeFilters,
} from "./types/online-shop";
import { RegionType } from "./types/region";

export const LOGIN = "/auth/login";
export const LOGIN_WITH_PHONE = LOGIN + "/phone";
export const RESET_PASSWORD = "/auth/reset-password";
export const DISTRIBUTOR_REGISTER = "/auth/register/distributor";
export const DISTRIBUTOR_SUBMIT_NEW_SK = "/submit/sk";
export const DISTRIBUTOR_SUBMIT_PAYMENT_PROOF = "/orders/submit/payment-proofs";
export const AGENT_REGISTER = "/auth/register/agent";
export const AGENT_SUBMIT_PAYMENT_PROOF = "/submit/payment-proof";

export const REFRESH_TOKEN = "/auth/token/refresh";
export const UPLOAD_FILE = "/upload";
export const GET_USER_DETAILS = `/auth/profile`;

export const GET_ISLANDS = "/location/islands";
export const GET_ISLANDS_WITH_STATUS = "/admin/requests/links/status";

export const PRODUCTS_URL = "/products";
export const GET_PRODUCTS = (status?: boolean, order?: SortFilterValue) =>
  `${PRODUCTS_URL}?status=${status === undefined ? "" : status}&order=${
    order || ""
  }`;
export const UPDATE_PRODUCT_STATUS = (id: number) =>
  `${PRODUCTS_URL}/${id}/status`;

export const GET_AGENT_LIST = (search?: string) =>
  `/agents${search ? `?search=${search}` : ""}`;
export const GET_AGENT_DETAILS = (uoc: string) => `/agents/${uoc}`;
export const GET_AGENT_LIST_IN_DISTRIBUTOR = (uoc: string) =>
  `/admin/distributor/${uoc}/agents`;
export const GET_AGENTS_BY_SEARCH = (search: string) =>
  `/admin/search/agents?search=${search}`;
export const FORCE_UPDATE_AGENT_DETAILS = (uoc: string) =>
  `/admin/user/${uoc}/force-update`;

export const GET_ISLAND_PROVINCES = (island: string) =>
  `/admin/agents?island=${island}`;

export const GET_ADMIN_REQUESTS = (
  page: number,
  status: string,
  sortBy: string,
  isSend: boolean = false
) =>
  `/admin/requests?status=${
    status === "all" ? "" : status
  }&page=${page}&order=${sortBy}&isSend=${isSend}`;
export const GET_ADMIN_REQUESTS_COUNT = (status: string) =>
  `/admin/requests/count?status=${status}`;
export const GET_ADMIN_REQUEST_DETAIL = (id: number, field: string = "") =>
  field ? `/admin/requests/${id}?field=${field}` : `/admin/requests/${id}`;
export const UPDATE_ADMIN_REQUEST_STATUS = (id: number) =>
  `/admin/requests/${id}/status`;
export const GET_ADMIN_BROADCASTS = (isDraft: boolean) =>
  `/admin/broadcasts?isDraft=${isDraft}`;
export const GET_ADMIN_BROADCAST_BY_ID = (id: number) =>
  `/admin/broadcasts/${id}`;
export const POST_ADMIN_BROADCAST = "/admin/broadcasts";
export const GET_LINKS_BY_ISLAND = (island: string) =>
  `/admin/requests/links?island=${island}`;
export const SUBMIT_INVALID_LINKS_REASONS_BY_ISLAND = (island: string) =>
  `/admin/requests/links/${island}`;
export const UPDATE_MARKETPLACE_LINK = `/agents/update-link`;
export const GET_REQUESTS_DRAFTS = `/requests/draft`;
export const DELETE_REQUEST_DRAFT = (id: number) => `/requests/draft/${id}`;
export const GET_REQUEST_DRAFT_DETAIL = (id: number) => `/requests/draft/${id}`;
export const CREATE_DATA_CHANGE_REQUEST = (draft: boolean) =>
  `/requests/field-change?draft=${draft}`;
export const UPDATE_REQUEST_DRAFT = (id: number) => `/requests/draft/${id}`;
export const CREATE_AGENT_TERMINATION_REQUEST = (
  draft: boolean,
  selfTerminate?: boolean
) => `/requests/${selfTerminate ? "self-" : ""}terminate-agent?draft=${draft}`;

export const GET_UPDATE_LOGS = (
  uoc: string,
  page: number,
  order: SortFilterValue,
  size: number,
  status?: string
) =>
  `/requests/logs/${uoc}?status=${
    status || ""
  }&page=${page}&size=${size}&order=${order}`;

export const GET_NOTIFICATIONS = (page: number, size: number, order: string) =>
  `/notifications?page=${page}&size=${size}&order=${order}`;
export const GET_NOTIFICATION_COUNT = `/notifications/count`;
export const CREATE_COMPLAINT = (requestId: number) =>
  `/requests/complain/${requestId}`;
export const SUBMIT_RECEIVE_AGENT = "/agents/receive-agent";
export const FORCE_TERMINATE_DISTRIBUTOR = (uoc: string) =>
  `/admin/user/${uoc}/force-terminate`;

export const GET_AVAILABLE_DISTRIBUTORS = (
  province: string,
  city: string,
  district: string
) => {
  const param = new URLSearchParams();
  if (province) param.set("province", province);
  if (city) param.set("city", city);
  if (district) param.set("district", district);
  return `/agents/available/area?${param.toString()}`;
};

export const UPDATE_RESELECT_DISTRIBUTOR = "/requests/reselect-distributor";
export const GET_REGIONS = (type: RegionType, code?: string) => {
  const parsedCode = code ? parseRegion(code).code : undefined;
  return `${process.env.NEXT_PUBLIC_REGION_API_BASE_URL}/${type}${
    parsedCode ? `/${parsedCode}` : ""
  }.json`;
};

// ONLINE SHOP
export const GET_PRODUCT_DETAIL = (productId: string) =>
  `/products/${productId}`;
export const GET_PRODUCT_IMAGES = `/products/images`;
export const CREATE_PRODUCT = "/products";
export const UPDATE_PRODUCT_DETAIL = (productId: string) =>
  `/products/${productId}`;
export const GET_ORDERS = (
  status: OrderStatus,
  page: number,
  order: SortFilterValue,
  size: number,
  search?: string
) => {
  const param = new URLSearchParams();
  const sts = status === "all" ? "" : status;
  if (sts) param.set("status", sts);
  if (page) param.set("page", page.toString());
  if (order) param.set("order", order);
  if (size) param.set("size", size.toString());
  if (search) param.set("search", search.toLowerCase());
  return `/orders?${param.toString()}`;
};
export const APPROVE_ORDER = (id: string | number) =>
  `/orders/${id}/process-status`;

export const TODAY_STATS = "/dashboard/order-stats";
export const SHOP_STATS = "/dashboard/shop-stats";
export const BEST_SELLER_PRODUCTS = "/dashboard/best-selling-products";
export const SHOP_BALANCE = "/dashboard/shop-balance";
export const CUSTOMER_STATS_OVERVIEW = (
  filter: "topSpender" | "demography" = "topSpender"
) => `/dashboard/customer-stats-summary?filter=${filter}`;
export const CUSTOMER_STATS_LIST = (
  page: number,
  filter: "topSpender" | "demography" = "topSpender"
) => `/dashboard/customer-stats?page=${page}&filter=${filter}`;
export const DOWNLOAD_CUSTOMER_STATS = (
  filter: "topSpender" | "demography" = "topSpender"
) => `/dashboard/customer-stats/download?filter=${filter}`;

export const GET_TOP_STATS = (
  type:
    | "top-selling-stores"
    | "top-purchasing-customers" = "top-selling-stores",
  page: number,
  size: number,
  slug?: string
) => `/overview/${type}${slug ? `/${slug}` : ""}?page=${page}&size=${size}`;
export const GET_STATISTICS_SUMMARY = (
  type: "sales-stats" | "customer-demography-stats" = "sales-stats",
  filter?: StatisticsTimeFilters,
  slug?: string
) =>
  `/overview/${type}/summary${slug ? `/${slug}` : ""}?filter=${filter || ""}`;
export const GET_STATISTICS_CHART_DATA = (
  type: "sales-stats" | "customer-demography-stats" = "sales-stats",
  filter?: StatisticsTimeFilters,
  category?: SalesChartCategories | CustomerChartCategories,
  slug?: string
) =>
  `/overview/${type}/chart${slug ? `/${slug}` : ""}?filter=${
    filter || ""
  }&category=${category || ""}`;
export const GET_OVERVIEW_ORDER_HISTORY = (
  slug: string,
  page: number,
  size: number
) =>
  `/overview/order-history${slug ? `/${slug}` : ""}?page=${page}&size=${size}`;

export const GET_WAITLIST_DATA = (role: "agent" | "distributor") =>
  `/admin/agents/waitlist?role=${role}`;

export const UPDATE_MAX_KUOTA = `/admin/agents/kuota`;

export const GET_PRODUCT_LIST = (type: 1 | 2 = 2) =>
  `/products?orderNumber=${type.toString()}`;
export const GET_SHIPPING_OPTIONS = "/shipping-options";
export const CREATE_FIRST_ORDERS = "/orders";
export const GET_FIRST_ORDERS = (uoc: string) => `/orders/${uoc}`;
