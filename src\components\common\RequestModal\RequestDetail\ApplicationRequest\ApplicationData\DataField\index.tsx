import dynamic from "next/dynamic";
import { isValidUrl } from "@/utils/common-functions";
import { Flex, Text, Divider, Anchor, Image, SimpleGrid } from "@mantine/core";
const PDFViewer = dynamic(() => import("@/components/common/PDFViewer"), {
  ssr: false,
});

export interface DataFieldProps {
  label: string;
  value?: string | string[];
  hideDivider?: boolean;
  isPdf?: boolean;
}

const DataField: React.FC<DataFieldProps> = ({
  label,
  value,
  hideDivider,
  isPdf,
}) => {
  const isUrl = typeof value === "string" && isValidUrl(value);
  const isImages = Array.isArray(value);

  const renderField = (): React.ReactNode => {
    switch (true) {
      case isPdf:
        return <PDFViewer pdf={value as string} />;
      case isUrl:
        return (
          <Anchor href={value} target="_blank">
            {value}
          </Anchor>
        );
      case isImages:
        return (
          <SimpleGrid cols={value.length > 1 ? { base: 1, sm: 2 } : 1}>
            {value.map((item, index) => (
              <Image
                key={index}
                src={item}
                radius="md"
                onClick={() => window.open(item, "_blank")}
                style={{
                  aspectRatio: value.length > 1 ? "1/1" : "auto",
                  objectFit: "cover",
                  cursor: "pointer",
                }}
              />
            ))}
          </SimpleGrid>
        );
      default:
        return <Text>{(value as string) || "-"}</Text>;
    }
  };

  return (
    <>
      <Flex gap="xs" direction={isPdf || isImages ? "column" : "row"}>
        <Text c="text">{label}: </Text>
        {renderField()}
      </Flex>
      {!hideDivider && <Divider />}
    </>
  );
};

export default DataField;
