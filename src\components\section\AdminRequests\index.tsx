import { Flex, Text, Title } from "@mantine/core";
import { AdminRequestHeader } from "./AdminRequestHeader";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { extractMonthYearFromTimestamp } from "@/utils/common-functions";
import React, { useEffect, useState } from "react";
import {
  fetchAdminRequests,
  fetchAdminRequestsCount,
} from "@/store/slices/adminRequestSlice";
import { useSearchParams } from "next/navigation";
import InfiniteScroll from "react-infinite-scroll-component";
import { RequestCardData } from "@/utils/types/request";
import RequestCard from "@/components/common/RequestCard";
import InfiniteScrollWrapper from "@/components/common/InfiniteScrollWrapper";

const AdminRequests = () => {
  const dispatch: AppDispatch = useDispatch();
  const { adminRequestResponse, requests, newRequestCount } = useSelector(
    (state: RootState) => state.adminRequest
  );
  const searchParams = useSearchParams();
  const filter = searchParams?.get("filter") || "all";
  const sortBy = searchParams?.get("sortBy") || "desc";
  const [page, setPage] = useState(1);

  const handleNextPage = () => {
    const nextPage = page + 1;
    setPage(nextPage);
    dispatch(fetchAdminRequests(nextPage, filter, sortBy));
  };

  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  useEffect(() => {
    setPage(1);
    dispatch(fetchAdminRequests(1, filter, sortBy));
  }, [filter, sortBy]);

  useEffect(() => {
    dispatch(fetchAdminRequestsCount("pending"));
  }, []);

  const renderRequests = (requests: RequestCardData[]) => {
    let currMonthYear = "";
    return (
      <InfiniteScrollWrapper>
        {adminRequestResponse && requests && requests.length > 0 && (
          <InfiniteScroll
            className="infinite-scroll"
            dataLength={requests.length}
            hasMore={requests.length < adminRequestResponse.totalItems}
            next={handleNextPage}
            loader={
              <Text c="text-light" ta={"center"} py={"md"}>
                Loading more requests...
              </Text>
            }
            endMessage={
              <Text
                c="text-light"
                ta={"center"}
                py={"md"}
                onClick={handleScrollToTop}
                className="end-of-requests"
              >
                End of requests
              </Text>
            }
          >
            {requests.map((request) => {
              const requestMonthYear = extractMonthYearFromTimestamp(
                request.createdAt
              );
              if (currMonthYear !== requestMonthYear) {
                currMonthYear = requestMonthYear;
                return (
                  <React.Fragment key={requestMonthYear}>
                    <Title order={3}>{requestMonthYear}</Title>
                    <RequestCard key={request.id} {...request} />
                  </React.Fragment>
                );
              }
              return <RequestCard key={request.id} {...request} />;
            })}
          </InfiniteScroll>
        )}
      </InfiniteScrollWrapper>
    );
  };

  return (
    <Flex direction={"column"} gap={"xl2"}>
      <AdminRequestHeader count={newRequestCount || 0} />
      {renderRequests(requests)}
    </Flex>
  );
};

export default AdminRequests;
