import { Flex, Paper, Text, But<PERSON>, CloseButton } from "@mantine/core";
import Icon from "../Icon";
import { useState } from "react";
import ChooseDistributorModal from "../RequestCard/Contents/ChooseNewDistributor/ChooseDistributorModal";

interface TerminatedDistributorWarningProps {
  onClose: () => void;
  requestId: number;
  onComplete?: () => void;
}

const TerminatedDistributorWarning = ({
  onClose,
  requestId,
  onComplete,
}: TerminatedDistributorWarningProps) => {
  const [opened, setOpened] = useState(false);

  return (
    <Paper bg={"error.1"} radius={"0 0 12px 12px"} w={"100%"}>
      <ChooseDistributorModal
        opened={opened}
        onClose={() => setOpened(false)}
        requestId={requestId}
        onComplete={onComplete}
      />
      <Flex justify={"space-between"} gap={"md"} p={"md"}>
        <Flex gap={"xs"} maw={"85%"}>
          <Icon icon="warning" size={16} />
          <Flex c={"error"} direction={"column"} gap={"2px"}>
            <Text fw={600}>
              Distributor Anda diberhentikan! Anda harus mencari distributor
              baru
            </Text>
            <Text>
              Distributor Anda telah berhenti bekerja dengan Hotto. Kami
              menyarankan Anda untuk mencari distributor baru untuk mempermudah
              proses order Anda.
            </Text>
          </Flex>
        </Flex>
        <Flex gap={"lg"}>
          <Button onClick={() => setOpened(true)}>View</Button>
          <CloseButton onClick={onClose} />
        </Flex>
      </Flex>
    </Paper>
  );
};

export default TerminatedDistributorWarning;
