"use client";

import Consideration from "@/components/section/Register/Distributor/Consideration";
import Footer from "@/components/common/Footer";
import useBreakpoints from "@/hooks/useBreakpoints";
import { Center, Stack, Image, Box } from "@mantine/core";
import { parseAsStringLiteral, useQueryState } from "nuqs";
import { MAX_WIDTH } from "@/utils/constants";

const RegisterFinishedPage = () => {
  const { mobile } = useBreakpoints();
  const [status] = useQueryState(
    "status",
    parseAsStringLiteral(["waitlisted", "considered"] as const).withDefault(
      "considered"
    )
  );
  const [role] = useQueryState(
    "role",
    parseAsStringLiteral(["distributor", "agent"] as const).withDefault(
      "distributor"
    )
  );

  return (
    <Center p={{ base: 12, sm: 16, md: 24 }}>
      <Stack w="100%" gap="xl">
        <Image
          radius="md"
          src={`/images/register/hero${mobile ? "-mobile" : ""}.png`}
          alt="hero"
        />
        <Box maw={MAX_WIDTH} mx="auto">
          <Consideration role={role} status={status} />
        </Box>
        <Footer pt={50} />
      </Stack>
    </Center>
  );
};

export default RegisterFinishedPage;
