import api from "@/lib/ecommerce";
import { AppDispatch } from "@/store";
import {
  GET_OVERVIEW_ORDER_HISTORY,
  GET_STATISTICS_CHART_DATA,
  GET_STATISTICS_SUMMARY,
  GET_TOP_STATS,
} from "@/utils/api-routes";
import { ITEMS_PER_PAGE } from "@/utils/constants";
import { ApiResponse } from "@/utils/types";
import {
  TopPurchasingCustomersData,
  TopPurchasingCustomersResponse,
  CustomerSummaryData,
  SalesSummaryData,
  TopSellingStoresData,
  TopSellingStoresResponse,
  SalesSummaryResponse,
  StatisticsTimeFilters,
  CustomerSummaryResponse,
  SalesChartCategories,
  CustomerChartCategories,
  StatisticsChartData,
  StatisticsChartResponse,
  OverviewOrderHistoryData,
} from "@/utils/types/online-shop";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { AxiosResponse } from "axios";
import { toast } from "sonner";

interface OverviewState {
  topSellingStores: TopSellingStoresData;
  topPurchasingCustomers: TopPurchasingCustomersData;
  salesSummary: SalesSummaryData;
  customerSummary: CustomerSummaryData;
  salesChartData: StatisticsChartData;
  customerChartData: StatisticsChartData;
  orderHistory: OverviewOrderHistoryData;
  loading: {
    topSellingStores: boolean;
    topPurchasingCustomers: boolean;
    salesSummary: boolean;
    customerSummary: boolean;
    salesChart: boolean;
    customerChart: boolean;
    orderHistory: boolean;
    download: boolean;
  };
}

const initialState: OverviewState = {
  topSellingStores: {} as TopSellingStoresData,
  topPurchasingCustomers: {} as TopPurchasingCustomersData,
  salesSummary: {} as SalesSummaryData,
  customerSummary: {} as CustomerSummaryData,
  salesChartData: {} as StatisticsChartData,
  customerChartData: {} as StatisticsChartData,
  orderHistory: {} as OverviewOrderHistoryData,
  loading: {
    topSellingStores: false,
    topPurchasingCustomers: false,
    salesSummary: false,
    customerSummary: false,
    salesChart: false,
    customerChart: false,
    orderHistory: false,
    download: false,
  },
};

const overviewSlice = createSlice({
  name: "overview",
  initialState,
  reducers: {
    setTopSellingStores(
      state,
      action: PayloadAction<OverviewState["topSellingStores"]>
    ) {
      state.topSellingStores = action.payload;
    },
    setTopPurchasingCustomers(
      state,
      action: PayloadAction<OverviewState["topPurchasingCustomers"]>
    ) {
      state.topPurchasingCustomers = action.payload;
    },
    setSalesSummary(
      state,
      action: PayloadAction<OverviewState["salesSummary"]>
    ) {
      state.salesSummary = action.payload;
    },
    setCustomerSummary(
      state,
      action: PayloadAction<OverviewState["customerSummary"]>
    ) {
      state.customerSummary = action.payload;
    },
    setSalesChartData(
      state,
      action: PayloadAction<OverviewState["salesChartData"]>
    ) {
      state.salesChartData = action.payload;
    },
    setCustomerChartData(
      state,
      action: PayloadAction<OverviewState["customerChartData"]>
    ) {
      state.customerChartData = action.payload;
    },
    setOrderHistory(
      state,
      action: PayloadAction<OverviewState["orderHistory"]>
    ) {
      state.orderHistory = action.payload;
    },
    setLoading(
      state,
      action: PayloadAction<{
        type: keyof OverviewState["loading"];
        loading: boolean;
      }>
    ) {
      state.loading[action.payload.type] = action.payload.loading;
    },
  },
});

const act = overviewSlice.actions;

export const fetchTopSellingStores =
  (page: number = 1, size: number = ITEMS_PER_PAGE) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(act.setLoading({ type: "topSellingStores", loading: true }));
      const res: AxiosResponse<TopSellingStoresResponse> = await api.get(
        GET_TOP_STATS("top-selling-stores", page, size)
      );
      if (res) {
        dispatch(act.setTopSellingStores(res.data.data));
      }
    } catch {
      toast.error("Gagal mendapatkan data statistik toko.");
      dispatch(act.setTopSellingStores(initialState.topSellingStores));
    } finally {
      dispatch(act.setLoading({ type: "topSellingStores", loading: false }));
    }
  };

export const fetchTopPurchasingCustomers =
  (page: number = 1, size: number = ITEMS_PER_PAGE, slug?: string) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(
        act.setLoading({ type: "topPurchasingCustomers", loading: true })
      );
      const res: AxiosResponse<TopPurchasingCustomersResponse> = await api.get(
        GET_TOP_STATS("top-purchasing-customers", page, size, slug)
      );
      if (res) {
        dispatch(act.setTopPurchasingCustomers(res.data.data));
      }
    } catch {
      toast.error("Gagal mendapatkan data pembelian teratas customer.");
      dispatch(
        act.setTopPurchasingCustomers(initialState.topPurchasingCustomers)
      );
    } finally {
      dispatch(
        act.setLoading({ type: "topPurchasingCustomers", loading: false })
      );
    }
  };

export const fetchSalesSummary =
  (filter: StatisticsTimeFilters, slug?: string) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(act.setLoading({ type: "salesSummary", loading: true }));
      const res: AxiosResponse<SalesSummaryResponse> = await api.get(
        GET_STATISTICS_SUMMARY("sales-stats", filter, slug)
      );
      if (res) {
        dispatch(act.setSalesSummary(res.data.data));
      }
    } catch {
      toast.error("Gagal mendapatkan data statistik penjualan.");
      dispatch(act.setSalesSummary(initialState.salesSummary));
    } finally {
      dispatch(act.setLoading({ type: "salesSummary", loading: false }));
    }
  };

export const fetchCustomerSummary =
  (filter: StatisticsTimeFilters, slug?: string) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(act.setLoading({ type: "customerSummary", loading: true }));
      const res: AxiosResponse<CustomerSummaryResponse> = await api.get(
        GET_STATISTICS_SUMMARY("customer-demography-stats", filter, slug)
      );
      if (res) {
        dispatch(act.setCustomerSummary(res.data.data));
      }
    } catch {
      toast.error("Gagal mendapatkan data statistik customer.");
      dispatch(act.setCustomerSummary(initialState.customerSummary));
    } finally {
      dispatch(act.setLoading({ type: "customerSummary", loading: false }));
    }
  };

export const fetchSalesChartData =
  (
    filter: StatisticsTimeFilters,
    category: SalesChartCategories,
    slug?: string
  ) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(act.setLoading({ type: "salesChart", loading: true }));
      const res: AxiosResponse<StatisticsChartResponse> = await api.get(
        GET_STATISTICS_CHART_DATA("sales-stats", filter, category, slug)
      );
      if (res) {
        dispatch(act.setSalesChartData(res.data.data));
      }
    } catch {
      toast.error("Gagal mendapatkan data grafik statistik penjualan.");
      dispatch(act.setSalesChartData(initialState.salesChartData));
    } finally {
      dispatch(act.setLoading({ type: "salesChart", loading: false }));
    }
  };

export const fetchCustomerChartData =
  (
    filter: StatisticsTimeFilters,
    category: CustomerChartCategories,
    slug?: string
  ) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(act.setLoading({ type: "customerChart", loading: true }));
      const res: AxiosResponse<StatisticsChartResponse> = await api.get(
        GET_STATISTICS_CHART_DATA(
          "customer-demography-stats",
          filter,
          category,
          slug
        )
      );
      if (res) {
        dispatch(act.setCustomerChartData(res.data.data));
      }
    } catch {
      toast.error("Gagal mendapatkan grafik statistik customer.");
      dispatch(act.setCustomerChartData(initialState.customerChartData));
    } finally {
      dispatch(act.setLoading({ type: "customerChart", loading: false }));
    }
  };

export const fetchOrderHistory =
  (slug: string, page: number = 1, size: number = ITEMS_PER_PAGE) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(act.setLoading({ type: "orderHistory", loading: true }));
      const res: AxiosResponse<ApiResponse<OverviewOrderHistoryData>> =
        await api.get(GET_OVERVIEW_ORDER_HISTORY(slug, page, size));
      if (res.data.data) {
        dispatch(act.setOrderHistory(res.data.data));
      }
    } catch {
      toast.error("Gagal mendapatkan riwayat order.");
      dispatch(act.setOrderHistory(initialState.orderHistory));
    } finally {
      dispatch(act.setLoading({ type: "orderHistory", loading: false }));
    }
  };

export const downloadOverview =
  (slug: string, type: "order-history" | "top-purchasing-customers") =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(act.setLoading({ type: "download", loading: true }));
      const res = await api.get(`/overview/${type}/${slug}/download`);
      if (res) {
        const blob = new Blob([res.data], { type: "application/csv" });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${type}-${slug}.csv`;
        a.click();
      }
    } catch {
      toast.error("Gagal mengunduh data, coba lagi nanti.");
    } finally {
      dispatch(act.setLoading({ type: "download", loading: false }));
    }
  };

export default overviewSlice.reducer;
