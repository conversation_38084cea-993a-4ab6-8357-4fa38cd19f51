import { Checkbox, Flex, ScrollArea, Skeleton, Text } from "@mantine/core";
import ProductImageContainer from "./ProductImageContainer";

interface ImageMultiSelectProps {
  selectedImages: string[];
  imageSelection: string[];
  onChange: (images: string[]) => void;
  loading?: boolean;
}

const ImageMultiSelect = ({
  selectedImages,
  imageSelection,
  onChange,
  loading = false,
}: ImageMultiSelectProps) => {
  const handleToggleImage = (image: string) => {
    const newSelectedImages = selectedImages.includes(image)
      ? selectedImages.filter((img) => img !== image)
      : [...selectedImages, image];
    onChange(newSelectedImages);
  };

  return (
    <ScrollArea maw={"100%"} pb={"xs"}>
      {loading && (
        <Flex justify={"center"} align={"center"} gap={"sm"}>
          {Array.from({ length: 6 }).map((_, index) => (
            <Skeleton key={index} width={200} height={200} />
          ))}
        </Flex>
      )}

      {!loading && (
        <Flex gap={"md"} w={"fit-content"}>
          {imageSelection && imageSelection.length > 0 ? (
            <>
              {imageSelection.map((image, idx) => (
                <Flex
                  key={idx}
                  direction={"column"}
                  gap={"sm"}
                  justify={"center"}
                  align={"center"}
                >
                  <ProductImageContainer
                    image={image}
                    selected={selectedImages.includes(image)}
                    onClick={() => handleToggleImage(image)}
                  />
                  <Checkbox
                    checked={selectedImages.includes(image)}
                    onChange={() => handleToggleImage(image)}
                  />
                </Flex>
              ))}
            </>
          ) : (
            <Text py={"md"} c={"text-light"} fw={500}>
              Oops! Tidak ada foto yang dapat ditampilkan.
            </Text>
          )}
        </Flex>
      )}
    </ScrollArea>
  );
};

export default ImageMultiSelect;
