import {
  DetailInfoFormValues,
  VerificationInfoFormValues,
} from "@/utils/types/distributor-register";
import { createSlice } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { AppDispatch, RootState } from "..";
import api from "@/lib/axios";
import { AGENT_REGISTER, GET_AVAILABLE_DISTRIBUTORS } from "@/utils/api-routes";
import { agentRegisterSchema, parseRegion } from "@/utils/common-functions";
import * as yup from "yup";

import {
  AgentPersonalInfoFormValues,
  AgentRegisterPayload,
} from "@/utils/types/agent-register";
import { AvailableDistributor } from "@/utils/types/request";

export interface AgentRegisterState {
  personalInfo: AgentPersonalInfoFormValues;
  detailInfo: DetailInfoFormValues;
  distributorId: string;
  verificationInfo: VerificationInfoFormValues;
  availableDistributors: AvailableDistributor[];
  paymentProof: string;
  error?: string;
  isLoading: boolean;
  success?: boolean;
}

const initialState: AgentRegisterState = {
  personalInfo: {
    name: "",
    nik: "",
    email: "",
    phoneNumber: "",
    island: "",
    province: "",
    regency: "",
    district: "",
    village: "",
    postalCode: "",
    address: "",
  },
  detailInfo: {
    warehouseIsland: "",
    warehouseProvince: "",
    warehouseRegency: "",
    warehouseDistrict: "",
    warehouseVillage: "",
    warehousePostalCode: "",
    warehouseAddress: "",
    adminPhoneNumber: "",
    tokopedia: "",
    shopee: "",
    tiktok: "",
  },
  distributorId: "",
  verificationInfo: {
    idCardImage: "",
    selfieImage: "",
  },
  availableDistributors: [],
  paymentProof: "",
  error: undefined,
  isLoading: false,
  success: false,
};

const agentRegisterSlice = createSlice({
  name: "agentRegister",
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    setSuccess: (state, action) => {
      state.success = action.payload;
      toast.success("Pendaftaran kamu berhasil!");
    },
    setPersonalInfo: (state, action) => {
      state.personalInfo = action.payload;
    },
    setDetailInfo: (state, action) => {
      state.detailInfo = action.payload;
    },
    setVerificationInfo: (state, action) => {
      state.verificationInfo = action.payload;
    },
    setDistributorId: (state, action) => {
      state.distributorId = action.payload;
    },
    setAvailableDistributors: (state, action) => {
      state.availableDistributors = action.payload;
    },
    setPaymentProof: (state, action) => {
      state.paymentProof = action.payload;
    },
  },
});

export const savePersonalInfo =
  (data: AgentPersonalInfoFormValues) => async (dispatch: AppDispatch) => {
    dispatch(agentRegisterSlice.actions.setPersonalInfo(data));
  };

export const saveDetailInfo =
  (data: DetailInfoFormValues) => async (dispatch: AppDispatch) => {
    dispatch(agentRegisterSlice.actions.setDetailInfo(data));
  };

export const saveDistributorId =
  (data: string) => async (dispatch: AppDispatch) => {
    dispatch(agentRegisterSlice.actions.setDistributorId(data));
  };

export const saveVerificationInfo =
  (data: VerificationInfoFormValues) => async (dispatch: AppDispatch) => {
    dispatch(agentRegisterSlice.actions.setVerificationInfo(data));
  };

export const fetchAvailableDistributors =
  (province: string, city: string, district: string) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(agentRegisterSlice.actions.setLoading(true));
      const response = await api.get(
        GET_AVAILABLE_DISTRIBUTORS(province, city, district)
      );
      dispatch(
        agentRegisterSlice.actions.setAvailableDistributors(response.data.data)
      );
    } catch (error) {
      dispatch(agentRegisterSlice.actions.setError(error));
    } finally {
      dispatch(agentRegisterSlice.actions.setLoading(false));
    }
  };

export const resetInfo = () => async (dispatch: AppDispatch) => {
  dispatch(
    agentRegisterSlice.actions.setPersonalInfo(initialState.personalInfo)
  );
  dispatch(agentRegisterSlice.actions.setDetailInfo(initialState.detailInfo));
  dispatch(
    agentRegisterSlice.actions.setDistributorId(initialState.distributorId)
  );
  dispatch(
    agentRegisterSlice.actions.setVerificationInfo(
      initialState.verificationInfo
    )
  );
};

export const submitRegister =
  () => async (dispatch: AppDispatch, getState: () => RootState) => {
    const state = getState().agentRegister;

    const data = {
      ...state.personalInfo,
      ...state.detailInfo,
      ...state.verificationInfo,
      distributorId: state.distributorId,
    };
    try {
      dispatch(agentRegisterSlice.actions.setLoading(true));

      await agentRegisterSchema.validate(data, {
        abortEarly: false,
      });

      const dataToSubmit: AgentRegisterPayload = {
        name: data.name,
        nik: data.nik,
        email: data.email,
        phoneNumber: data.phoneNumber,
        island: data.island,
        province: parseRegion(data.province).name,
        city: parseRegion(data.regency).name,
        district: parseRegion(data.district).name,
        kelurahan: parseRegion(data.village).name,
        postalCode: data.postalCode,
        address: data.address,
        warehouseIsland: data.warehouseIsland,
        warehouseProvince: parseRegion(data.warehouseProvince).name,
        warehouseCity: parseRegion(data.warehouseRegency).name,
        warehouseDistrict: parseRegion(data.warehouseDistrict).name,
        warehouseKelurahan: parseRegion(data.warehouseVillage).name,
        warehousePostalCode: data.warehousePostalCode,
        warehouseAddress: data.warehouseAddress,
        adminPhoneNumber: data.adminPhoneNumber,
        ktp: data.idCardImage,
        selfieKtp: data.selfieImage,
        shops: {
          tokopedia: data.tokopedia,
          shopee: data.shopee,
          tiktok: data.tiktok,
        },
        distributorUOC: data.distributorId,
      };
      await api.post(AGENT_REGISTER, dataToSubmit);
      dispatch(resetInfo());
      dispatch(agentRegisterSlice.actions.setSuccess(true));
    } catch (error) {
      if (error instanceof yup.ValidationError) {
        toast.error(`Validasi gagal: ${error.errors.join(", ")}`);
      } else {
        toast.error(
          "Terjadi kesalahan saat mendaftar, silahkan coba beberapa saat lagi."
        );
      }
      dispatch(agentRegisterSlice.actions.setError(error));
    } finally {
      dispatch(agentRegisterSlice.actions.setLoading(false));
    }
  };

export const { setPaymentProof } = agentRegisterSlice.actions;

export default agentRegisterSlice.reducer;
