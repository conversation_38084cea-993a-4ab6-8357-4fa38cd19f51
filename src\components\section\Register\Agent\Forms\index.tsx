"use client";

import {
  <PERSON>,
  Stack,
  Text,
  Title,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Toolt<PERSON>,
} from "@mantine/core";
import { useEffect, useState } from "react";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { submitRegister } from "@/store/slices/agentRegisterSlice";
import useBreakpoints from "@/hooks/useBreakpoints";
import ButtonGroup from "@/components/common/ButtonGroup";
import AgentPersonalInfoForm from "./PersonalInfo";
import AgentDetailInfoForm from "./DetailInfo";
import AgentUserAgreementForm from "./UserAgreement";
import VerificationInfoForm from "../../Distributor/Forms/VerificationInfo";
import AgentVerificationInfoForm from "./VerificationInfo";

export interface FormStepperProps {
  onFormValid: (isValid: boolean) => void;
  nextClicked?: boolean;
}

const AgentRegisterForms: React.FC = () => {
  const dispatch: AppDispatch = useDispatch();
  const { isLoading } = useSelector((state: RootState) => state.agentRegister);
  const { mobile } = useBreakpoints();
  const [active, setActive] = useState(0);
  const [continuable, setContinuable] = useState(false);
  const [nextClicked, setNextClicked] = useState(false);

  const nextStep = () =>
    setActive((current) => (current < maxStep ? current + 1 : current));
  const prevStep = () =>
    setActive((current) => (current > 0 ? current - 1 : current));

  const handleNext = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    if (continuable) {
      nextStep();
      setContinuable(false);
      setNextClicked(false);
    } else {
      setNextClicked(true);
    }
  };

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [active]);

  const STEPS = [
    {
      label: "Informasi Pribadi",
      step: (
        <AgentPersonalInfoForm
          nextClicked={nextClicked}
          onFormValid={(isValid) => {
            setContinuable(isValid);
          }}
        />
      ),
    },
    {
      label: "Menjadi Agent Hotto",
      step: (
        <AgentDetailInfoForm
          nextClicked={nextClicked}
          onFormValid={(isValid) => {
            setContinuable(isValid);
          }}
        />
      ),
    },
    {
      label: "Verifikasi Identitas",
      step: (
        <AgentVerificationInfoForm
          onFormValid={(isValid) => {
            setContinuable(isValid);
          }}
        />
      ),
    },
    {
      label: "Persetujuan User",
      step: (
        <AgentUserAgreementForm
          onFormValid={(isValid) => {
            setContinuable(isValid);
          }}
        />
      ),
    },
  ];

  const maxStep = STEPS.length - 1;

  return (
    <Center w="100%">
      <Stack gap="xl" align="center">
        <Title ta="center" order={2}>
          Jadilah Agent Kami dan Hadirkan Gaya Hidup Sehat untuk Semua!
        </Title>
        <Stack gap={32} w="100%" maw={{ base: "100%", sm: 700 }}>
          <Stepper
            active={active}
            onStepClick={setActive}
            allowNextStepsSelect={false}
            styles={{ steps: { width: "100%" } }}
          >
            {STEPS.map(({ label, step }, index) => (
              <Stepper.Step key={`${index + 1}-${label}`} label={label}>
                <Text c="text-light" mb="lg" ta="center">
                  Isi form ini untuk menjadi agent resmi Hotto.
                </Text>
                {step}
              </Stepper.Step>
            ))}
          </Stepper>
          <ButtonGroup mt="xl" justify="center" w="100%">
            {active > 0 && (
              <Button
                c="black"
                fullWidth={mobile}
                variant="subtle"
                onClick={prevStep}
              >
                Kembali
              </Button>
            )}
            {active === maxStep ? (
              <Button
                fullWidth={mobile}
                loading={isLoading}
                disabled={!continuable}
                onClick={() => {
                  dispatch(submitRegister());
                }}
              >
                Daftar
              </Button>
            ) : (
              <Tooltip
                withArrow
                disabled={continuable}
                label="Isi form di atas dengan benar terlebih dahulu"
              >
                <Button
                  fullWidth={mobile}
                  onClick={handleNext}
                  data-disabled={!continuable}
                >
                  Selanjutnya
                </Button>
              </Tooltip>
            )}
          </ButtonGroup>
        </Stack>
      </Stack>
    </Center>
  );
};

export default AgentRegisterForms;
