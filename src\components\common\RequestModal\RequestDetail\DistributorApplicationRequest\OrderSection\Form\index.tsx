import { FirstOrder } from "@/utils/types/request";
import LeftSection from "@/components/common/LeftSection";
import {
  Stack,
  Box,
  Text,
  NumberInput,
  NumberFormatter,
  Button,
} from "@mantine/core";
import { Rupiah } from "@/utils/icons";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useState } from "react";
import ButtonGroup from "@/components/common/ButtonGroup";

interface OrderSectionFormProps {
  order: FirstOrder;
  address: string;
  onChange?: (id: number, cost: number) => void;
  onPrev?: () => void;
}

const OrderSectionForm = ({
  order,
  address,
  onChange,
  onPrev,
}: OrderSectionFormProps) => {
  const { firstOrdersToUpdate, loading } = useSelector(
    (state: RootState) => state.adminRequest
  );
  const initialCost = firstOrdersToUpdate.find(
    (item) => item.orderId === order.orderId
  )?.shippingCost;
  const [cost, setCost] = useState<number>(initialCost || 0);

  return (
    <Stack>
      <Box>
        <Text c="text-light">Produk dipilih</Text>
        {order.products.map((product, index) => (
          <Text key={index} fw={500}>
            {product.name} (<NumberFormatter value={product.price} />)
          </Text>
        ))}
      </Box>
      <Box>
        <Text c="text-light">Jasa kargo dipilih</Text>
        <Text fw={500}>{order.courier}</Text>
      </Box>
      <Box>
        <Text c="text-light">Alamat warehouse dikirim</Text>
        <Text fw={500}>{address}</Text>
      </Box>
      <Text>
        Silakan tentukan biaya ongkir yang akan dibebankan ke calon distributor
        ini.
      </Text>
      {order.products.map((product, index) => {
        const free = product.name.includes("120 Pouch");
        return (
          <Box key={index}>
            <NumberInput
              required
              hideControls
              thousandSeparator=","
              value={free ? 0 : cost}
              disabled={free}
              onChange={(value) => {
                if (free) return;
                setCost(value as number);
              }}
              label={`Biaya ongkir produk ${index + 1}`}
              leftSection={
                <LeftSection
                  icon={
                    <Rupiah
                      size={13}
                      color="var(--mantine-color-text-light-filled)"
                    />
                  }
                />
              }
            />
            {free && (
              <Text fz="sm" c="text-light">
                Karena produk yang dipilih adalah 120 pouch, biaya ongkir
                dikenakan gratis.
              </Text>
            )}
          </Box>
        );
      })}
      <ButtonGroup mt="xl" justify="space-between">
        <Button variant="subtle" color="text-dark" onClick={onPrev}>
          Kembali
        </Button>
        <Button
          loading={loading}
          onClick={() => onChange?.(order.orderId, cost)}
        >
          Submit
        </Button>
      </ButtonGroup>
    </Stack>
  );
};

export default OrderSectionForm;
