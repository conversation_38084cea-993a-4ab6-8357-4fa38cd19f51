import {
  Stack,
  Group,
  Text,
  Divider,
  Flex,
  StackProps,
  Skeleton,
  Box,
} from "@mantine/core";
import BackButton from "../../BackButton";
import { padding } from "..";

export interface NavigationTitleProps {
  title: React.ReactNode;
  description?: React.ReactNode;
  rightContent?: React.ReactNode;
  backUrl?: string;
  loading?: boolean;
}

const NavigationTitle: React.FC<
  NavigationTitleProps & Partial<Omit<StackProps, "title">>
> = ({ title, description, rightContent, backUrl, loading, ...props }) => {
  return (
    <Stack w="100%" mb="xl" {...props}>
      <Flex
        gap="sm"
        justify="space-between"
        align={{ base: "flex-end", sm: "center" }}
        direction={{ base: "column", sm: "row" }}
      >
        <Group align="center" w={{ base: "100%", sm: "fit-content" }}>
          <BackButton href={backUrl} disabled={loading} />
          {loading ? (
            <Stack gap="xs">
              <Skeleton h="20px" w="10rem" />
              <Skeleton h="12px" w="15rem" />
            </Stack>
          ) : (
            <Box>
              {typeof title === "string" ? (
                <Text size="xl" fw={500}>
                  {title}
                </Text>
              ) : (
                title
              )}
              {description &&
                (typeof description === "string" ? (
                  <Text size="sm" c="text-light">
                    {description}
                  </Text>
                ) : (
                  description
                ))}
            </Box>
          )}
        </Group>
        {loading ? <Skeleton h="2.5rem" w="7rem" /> : rightContent}
      </Flex>
      <Divider size="sm" mx={`-${padding}`} />
    </Stack>
  );
};

export default NavigationTitle;
