import ButtonGroup from "@/components/common/ButtonGroup";
import useBreakpoints from "@/hooks/useBreakpoints";
import { AppDispatch, RootState } from "@/store";
import { deleteProducts } from "@/store/slices/onlineShopSlices/productListSlice";
import { parseToRupiah } from "@/utils/common-functions";
import {
  Modal,
  Stack,
  Text,
  Title,
  Flex,
  Paper,
  ModalProps,
  Image,
  Box,
  Button,
} from "@mantine/core";
import { useDispatch, useSelector } from "react-redux";

interface DeleteConfirmationModal {
  opened: ModalProps["opened"];
  onClose: ModalProps["onClose"];
}

const DeleteConfirmationModal: React.FC<DeleteConfirmationModal> = ({
  opened,
  onClose,
}) => {
  const dispatch: AppDispatch = useDispatch();
  const { mobile } = useBreakpoints();

  const { products, selectedProducts, loading } = useSelector(
    (state: RootState) => state.productList
  );

  const productsToDelete = products.filter(({ id }) =>
    selectedProducts.includes(id)
  );

  const totalProduct =
    productsToDelete.length > 1 ? productsToDelete.length : "";

  const handleDelete = () => {
    dispatch(deleteProducts()).then(() => onClose());
  };

  return (
    <Modal
      centered
      size="lg"
      padding={0}
      fullScreen={mobile}
      opened={opened}
      onClose={onClose}
      withCloseButton={false}
    >
      <Stack justify="space-between" mih={mobile ? "100dvh" : undefined}>
        <Stack p="lg">
          <Modal.CloseButton />
          <Box ta="center">
            <Title order={2} mb="sm">
              Hapus {totalProduct} Produk Ini?
            </Title>
            <Text c="text">
              Jika produk sedang aktif, maka produk akan hilang dari etalase
            </Text>
          </Box>
          <Stack>
            {productsToDelete.map((product) => (
              <Paper key={product.id} withBorder p="sm">
                <Flex gap="md">
                  <Image
                    w={100}
                    h={100}
                    width={100}
                    height={100}
                    radius="md"
                    fit="cover"
                    src={product.images[0]}
                    alt={product.name}
                    bd="2px solid var(--mantine-primary-color-filled)"
                  />
                  <Stack justify="space-between">
                    <Box>
                      <Text fw={500}>{product.name}</Text>
                      <Text>Variasi: {product.variant}</Text>
                    </Box>
                    <Text c="text">{parseToRupiah(product.unitPrice)}</Text>
                  </Stack>
                </Flex>
              </Paper>
            ))}
          </Stack>
        </Stack>
        <Stack w="100%">
          <Paper bg="error.1" px="lg" py="sm" radius="8px 8px 0 0">
            <Text c="error" fw={500} fz="0.9rem">
              Produk yang dihapus akan hilang dari database
            </Text>
          </Paper>
          <ButtonGroup p="lg" justify="center" w="100%">
            <Button
              c="black"
              fullWidth={mobile}
              variant="subtle"
              onClick={onClose}
            >
              Kembali
            </Button>
            <Button
              color="error"
              fullWidth={mobile}
              disabled={loading}
              onClick={handleDelete}
            >
              Hapus
            </Button>
          </ButtonGroup>
        </Stack>
      </Stack>
    </Modal>
  );
};

export default DeleteConfirmationModal;
