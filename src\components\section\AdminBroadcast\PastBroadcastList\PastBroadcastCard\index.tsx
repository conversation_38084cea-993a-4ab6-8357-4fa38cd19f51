import Icon from "@/components/common/Icon";
import { formatDateToString } from "@/utils/common-functions";
import { BroadcastRecipient, BroadcastType } from "@/utils/types/broadcast";
import { Flex, Paper, Text } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";

interface PastBroadcastCardProps {
  title: string;
  createdAt: string;
  isAllRecipient: boolean;
  recipients: BroadcastRecipient[];
  type: BroadcastType;
}

const PastBroadcastCard = ({
  title,
  createdAt,
  isAllRecipient,
  recipients,
  type,
}: PastBroadcastCardProps) => {
  const isMobile = useMediaQuery("(max-width: 768px)");

  const recipientRoleAndCount = () => {
    if (isAllRecipient) {
      return "semua distributor dan agent";
    }
    const agentCount = recipients.filter(
      (recipient) => recipient.role === "agent"
    ).length;
    const distributorCount = recipients.filter(
      (recipient) => recipient.role === "distributor"
    ).length;
    if (agentCount === 0) {
      return `${distributorCount} distributor ${recipients
        .map((recipient) => recipient.name)
        .join(", ")}`;
    }
    if (distributorCount === 0) {
      return `${agentCount} agent ${recipients
        .map((recipient) => recipient.name)
        .join(", ")}`;
    }
    return `${agentCount} agent dan ${distributorCount} distributor ${recipients
      .map((recipient) => recipient.name)
      .join(", ")}`;
  };

  return (
    <Paper bg={"bg"} radius={"8px"} p={"md"}>
      <Flex
        gap={"md"}
        justify={"space-between"}
        align={isMobile ? "center" : "flex-start"}
      >
        <Flex gap={"sm"} align={"center"} w={"60%"}>
          <Icon
            icon={`broadcast/${
              type === BroadcastType.WARNING ? "warning" : "announce"
            }`}
          />
          <Flex direction={"column"} gap={"2px"} w={"100%"}>
            <Text fw={500}>{title}</Text>
            <Text
              c={"text-light"}
              size="14px"
              style={{
                whiteSpace: "nowrap",
                textOverflow: "ellipsis",
                overflow: "hidden",
                width: "100%",
              }}
            >
              Dikirim ke {`${recipientRoleAndCount()}`}
            </Text>
          </Flex>
        </Flex>
        <Flex direction={"column"} ta={"right"}>
          {!isMobile && (
            <Text c={"text-light"} fw={300}>
              Sent
            </Text>
          )}
          <Text c={"text-light"} fw={500} size={isMobile ? "xs" : "md"}>
            {formatDateToString(createdAt, "number")}
          </Text>
        </Flex>
      </Flex>
    </Paper>
  );
};

export default PastBroadcastCard;
