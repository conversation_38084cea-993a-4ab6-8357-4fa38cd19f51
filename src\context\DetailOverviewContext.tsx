import { createContext, PropsWithChildren, useContext } from "react";

export interface DetailOverviewContextType {
  slug: string;
}

const DetailOverviewContext = createContext<
  DetailOverviewContextType | undefined
>(undefined);

export const useDetailOverviewContext = (): DetailOverviewContextType => {
  const context = useContext(DetailOverviewContext);
  if (!context) {
    throw new Error(
      "useDetailOverviewContext must be used within a DetailOverviewProvider"
    );
  }
  return context;
};

export const DetailOverviewProvider: React.FC<
  PropsWithChildren & DetailOverviewContextType
> = ({ children, slug }) => {
  return (
    <DetailOverviewContext.Provider value={{ slug }}>
      {children}
    </DetailOverviewContext.Provider>
  );
};
