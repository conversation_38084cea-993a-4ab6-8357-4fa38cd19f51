import Icon from "@/components/common/Icon";
import UpdateLog from "@/components/section/AdminAgentDistributor/UpdateLog";
import useBreakpoints from "@/hooks/useBreakpoints";
import { formatDateToString } from "@/utils/common-functions";
import { Flex, Text } from "@mantine/core";

interface ForceUpdateDataNotificationCardContentProps {
  date: string;
  field: string;
  oldValue: string;
  newValue: string;
}

const ForceUpdateDataNotificationCardContent = ({
  date,
  field,
  oldValue,
  newValue,
}: ForceUpdateDataNotificationCardContentProps) => {
  const { tablet, mobile } = useBreakpoints();

  return (
    <Flex
      direction={mobile ? "column" : "row"}
      justify={"space-between"}
      gap={mobile ? "xs" : "md"}
      p={"md"}
    >
      <Flex gap={"md"} justify={"flex-start"} align={"flex-start"} w={"100%"}>
        <Icon icon={`broadcast/warning`} size={20} />
        <Flex direction={"column"} w={"100%"} miw={"100%"} gap={"xs"}>
          <Text fw={500} maw={"95%"}>
            {`Terdapat Perubahan Data oleh Admin Hotto`}
          </Text>
          <UpdateLog
            log={{
              fieldChanged: field,
              old: oldValue,
              new: newValue,
            }}
            fw={500}
          />
          <Text fw={500} c={"text-light"} maw={"95%"} size="sm">
            {`Harap untuk memastikan perubahan ini sesuai. Anda dapat menghubungi CS Hotto untuk informasi lebih lanjut.`}
          </Text>
        </Flex>
      </Flex>

      <Flex
        direction={"column"}
        ta={mobile ? "left" : "right"}
        align={mobile ? "flex-start" : "flex-end"}
      >
        <Text fw={300} c={"text-light"} size="sm">
          Sent
        </Text>
        <Text fw={500} c={"text-light"} size="sm">
          {formatDateToString(date, "number")}
        </Text>
      </Flex>
    </Flex>
  );
};

export default ForceUpdateDataNotificationCardContent;
