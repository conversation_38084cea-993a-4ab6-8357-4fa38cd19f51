import BrandLogo from "@/components/common/BrandLogo";
import Icon from "@/components/common/Icon";
import { SPACES } from "@/styles/spacing";
import {
  Card,
  Divider,
  Flex,
  Group,
  Paper,
  ScrollArea,
  Spoiler,
  Text,
  Title,
} from "@mantine/core";

export interface AnnouncementWrapperProps {
  children: React.ReactNode;
}

export interface AnnouncementItemProps {
  title: string;
  description?: string;
  date: string;
  isDescriptionTruncated?: boolean;
}

export interface AnnouncementProps {
  data: AnnouncementItemProps[];
}

export const AnnouncementWrapper = ({ children }: AnnouncementWrapperProps) => {
  return (
    <Paper color="primary" p={SPACES.md} bg="primary.1">
      <Flex direction="column" gap={SPACES.xl} h="100%">
        <AnnouncementHeader />
        <ScrollArea h={{ base: 200, sm: "100%" }} flex={{ sm: 1 }}>
          <Flex direction="column" gap={SPACES.xs}>
            {children}
          </Flex>
        </ScrollArea>
      </Flex>
    </Paper>
  );
};

export const AnnouncementHeader = () => {
  return (
    <Group gap={SPACES.xs} align="center" p={SPACES.sm}>
      <BrandLogo size={30} />
      <Divider orientation="vertical" color="gray" />
      <Title order={4} c="black">
        Announcements
      </Title>
    </Group>
  );
};

export const AnnouncementItem = ({
  title,
  description,
  date,
}: AnnouncementItemProps) => {
  return (
    <Card maw={{ base: "100%", sm: 452 }}>
      <Flex gap={SPACES.xs} direction="column">
        <Flex justify="space-between" align="center" c="accent">
          <Icon icon="broadcast/announce" />
          <Text>{date}</Text>
        </Flex>
        <Title order={4}>{title}</Title>
        {description && (
          <Spoiler maxHeight={42} showLabel="See All" hideLabel="Hide">
            <Text fw={300}>{description}</Text>
          </Spoiler>
        )}
      </Flex>
    </Card>
  );
};

const Announcements = ({ data }: AnnouncementProps) => {
  return (
    <AnnouncementWrapper>
      {data.map((announcement, index) => (
        <AnnouncementItem key={index} {...announcement} />
      ))}
    </AnnouncementWrapper>
  );
};

export default Announcements;
