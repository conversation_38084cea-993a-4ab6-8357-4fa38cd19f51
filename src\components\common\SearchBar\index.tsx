"use client";

import {
  ActionIcon,
  <PERSON>ton,
  Divider,
  Flex,
  Input,
  InputProps,
  PolymorphicComponentProps,
} from "@mantine/core";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import React, { useState, useEffect, HTMLAttributeAnchorTarget } from "react";
import Icon from "../Icon";
import Link, { LinkProps } from "next/link";

export interface SearchBarProps {
  placeholder?: string;
  href?: LinkProps["href"];
  linkTarget?: HTMLAttributeAnchorTarget;
}

/**
 * SearchBar is a React functional component that renders a search input field with optional link functionality.
 * It handles search queries and allows clearing the search input. The component updates the URL search parameters
 * and triggers navigation based on user input.
 *
 * @param {string} [placeholder="Search"] - Placeholder text for the search input.
 * @param {string} [href] - Optional URL to navigate to when the search input is linked.
 * @param {string} [linkTarget] - Specifies where to open the linked document.
 * @returns {JSX.Element} The rendered search bar component.
 */

const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = "Search",
  href,
  linkTarget,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [searchValue, setSearchValue] = useState<string>(
    searchParams?.get("search") || ""
  );

  useEffect(() => {
    const currentSearch = searchParams?.get("search") || "";
    setSearchValue(currentSearch);
  }, [searchParams]);

  const changeSearchQuery = (value: string) => {
    if (searchParams) {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set("search", value);
      newSearchParams.set("page", "1");
      router.push(`${pathname}?${newSearchParams.toString()}`);
    }
  };

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    changeSearchQuery(searchValue);
  };

  const handleClear = () => {
    setSearchValue("");
    changeSearchQuery("");
  };

  const SearchIcon = (
    <Flex align={"center"} justify={"center"} gap={"xs"}>
      <Icon icon="search" size={16} />
      <Divider orientation="vertical" />
    </Flex>
  );

  const ClearButton = (
    <ActionIcon variant="subtle" onClick={handleClear}>
      <Icon icon="close" size={14} />
    </ActionIcon>
  );

  const sharedProps: PolymorphicComponentProps<"input", InputProps> = {
    size: "md",
    leftSection: SearchIcon,
    placeholder: placeholder,
    miw: { base: "13em", md: "18em" },
  };

  return href ? (
    <Link href={href} target={linkTarget}>
      <Input
        {...sharedProps}
        role="link"
        style={{ cursor: "pointer", pointerEvents: "none" }}
      />
    </Link>
  ) : (
    <form onSubmit={handleSearch}>
      <Flex direction={"row"} align={"center"} gap={"xs"}>
        <Input
          {...sharedProps}
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          rightSection={searchValue ? ClearButton : null}
          rightSectionPointerEvents="auto"
        />
        <Button type="submit">Cari</Button>
      </Flex>
    </form>
  );
};

export default SearchBar;
