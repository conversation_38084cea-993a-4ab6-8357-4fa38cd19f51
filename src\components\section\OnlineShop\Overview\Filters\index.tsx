import SelectButton, {
  SelectButtonProps,
} from "@/components/common/SelectButton";
import useOverviewFilters from "@/hooks/useOverviewFilters";
import { RootState } from "@/store";
import { StatisticsTimeFilters } from "@/utils/types/online-shop";
import { Group, Text, Select, SelectProps } from "@mantine/core";
import { useSelector } from "react-redux";

const TIME_FILTERS_SELECT_DATA: SelectProps["data"] = [
  {
    label: "Harian",
    value: "daily",
  },
  {
    label: "Mingguan",
    value: "weekly",
  },
  {
    label: "Bulanan",
    value: "monthly",
  },
];

const STATS_TYPE_SELECT_DATA: SelectButtonProps["data"] = [
  {
    label: "Sales",
    value: "sales",
  },
  {
    label: "Demografi Customer",
    value: "demo",
  },
];

const OverviewStatisticFilters = () => {
  const { loading } = useSelector((state: RootState) => state.overview);
  const { stats, time, setStats, setPage, setTime } = useOverviewFilters();

  const disabled =
    loading.salesChart ||
    loading.customerChart ||
    loading.salesSummary ||
    loading.customerSummary;

  const handleTimeChange = (value: StatisticsTimeFilters) => {
    setTime(value);
  };

  const handleStatsChange = (value: "sales" | "demo") => {
    setPage(1);
    setStats(value);
  };

  return (
    <Group justify="space-between">
      <Group>
        <Text>Lihat Statistik: </Text>
        <SelectButton
          alt
          size="xs"
          value={stats}
          data={STATS_TYPE_SELECT_DATA}
          disabled={disabled}
          onChange={(value) => handleStatsChange(value!)}
        />
      </Group>
      <Select
        w="7em"
        value={time}
        data={TIME_FILTERS_SELECT_DATA}
        disabled={disabled}
        onChange={(value) => handleTimeChange(value as StatisticsTimeFilters)}
      />
    </Group>
  );
};

export default OverviewStatisticFilters;
