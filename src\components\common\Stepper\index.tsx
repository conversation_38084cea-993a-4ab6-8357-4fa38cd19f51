import { Stepper as <PERSON><PERSON><PERSON>tep<PERSON> } from "@mantine/core";
import { Stepper<PERSON><PERSON>, StepperWrapper } from "./style";
import { useMediaQuery } from "@mantine/hooks";

export interface StepData {
  index: number;
  label: string;
  description?: string;
}

interface StepperProps {
  /**
   * The index of the active step 0 based.
   */
  active: number;
  /**
   * The array of steps. Each step is an object with an `index` number and a `label` string.
   * Optionally, a `description` string can be added to each step.
   */
  steps: StepData[];
  onStepClick: (step: number) => void;
}

const Stepper = ({ active, steps, onStepClick }: StepperProps) => {
  const isMobile = useMediaQuery("(max-width: 768px)");
  return (
    <StepperWrapper>
      <MantineStepper
        active={active}
        onStepClick={onStepClick}
        size={isMobile ? "xs" : "md"}
      >
        {steps.map((step) => (
          <MantineStepper.Step
            key={step.index}
            label={step.label}
            description={step.description}
          />
        ))}
      </MantineStepper>
      {steps.length > 1 && <StepperLine />}
    </StepperWrapper>
  );
};

export default Stepper;
