"use client";

import PageLayout from "@/components/common/PageLayout";
import dynamic from "next/dynamic";
import { withAuth } from "@/utils/withAuth";
const Waitlist = dynamic(() => import("@/components/section/Waitlist"), {
  ssr: false,
});

const WaitlistPage = () => {
  return (
    <PageLayout
      title="Agent & Distributor"
      navigationContent={{
        title: "Kuota Agent & Distributor",
      }}
    >
      <Waitlist />
    </PageLayout>
  );
};

export default withAuth(WaitlistPage);
