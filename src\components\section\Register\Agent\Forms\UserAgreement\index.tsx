import {
  Stack,
  Checkbox,
  NativeSelect,
  Text,
  Title,
  Anchor,
  Button,
} from "@mantine/core";
import { FormStepperProps } from "..";
import { useEffect, useState } from "react";
import classes from "./style.module.css";
import PrivacyPolicy from "../../../Distributor/PrivacyPolicy";
import { agentRegisterSchema, parseRegion } from "@/utils/common-functions";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchAvailableDistributors,
  saveDistributorId,
} from "@/store/slices/agentRegisterSlice";

const schema = agentRegisterSchema.pick(["distributorId"]);

const AgentUserAgreementForm: React.FC<FormStepperProps> = ({
  onFormValid,
}) => {
  const [read, setRead] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const dispatch: AppDispatch = useDispatch();

  const { detailInfo, availableDistributors, distributorId, isLoading } =
    useSelector((state: RootState) => state.agentRegister);

  const form = useForm({
    mode: "onChange",
    resolver: yupResolver(schema),
    defaultValues: { distributorId },
  });

  useEffect(() => {
    const { warehouseProvince, warehouseRegency, warehouseDistrict } =
      detailInfo;
    if (warehouseProvince && warehouseRegency && warehouseDistrict) {
      dispatch(
        fetchAvailableDistributors(
          parseRegion(warehouseProvince).name,
          parseRegion(warehouseRegency).name,
          parseRegion(warehouseDistrict).name
        )
      );
    }
  }, []);

  useEffect(() => {
    dispatch(saveDistributorId(form.getValues().distributorId));
  }, [form.getValues().distributorId]);

  useEffect(() => {
    onFormValid(isChecked && !!distributorId);
  }, [distributorId, isChecked]);

  const handleDistributorChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    form.setValue("distributorId", e.target.value, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    });
  };

  return (
    <Stack gap="xl">
      {availableDistributors.length > 0 ? (
        <NativeSelect
          required
          label="Pilih Distributor"
          value={form.getValues().distributorId}
          error={form.formState.errors.distributorId?.message}
          data={[
            { value: "", label: "Pilih Distributor..." },
            ...availableDistributors.map(({ uoc, name }) => ({
              value: uoc,
              label: name,
            })),
          ]}
          disabled={isLoading}
          onChange={handleDistributorChange}
        />
      ) : (
        <Stack align="center" py="xl">
          <Title order={4} ta="center">
            Mohon maaf, saat ini belum tersedia distributor untuk lokasi Anda.
          </Title>
          <Text size="sm" c="text-light" ta="center">
            Ingin melihat daftar distributor resmi Hotto?
          </Text>
          <Button
            component="a"
            href="https://hotto.co.id/list-distributor-resmi/"
            target="_blank"
            variant="light"
            size="sm"
          >
            Lihat Daftar Distributor
          </Button>
        </Stack>
      )}

      {availableDistributors.length > 0 && (
        <>
          <PrivacyPolicy onRead={setRead} />
          <Checkbox
            classNames={classes}
            disabled={!read}
            onChange={(e) => setIsChecked(e.currentTarget.checked)}
            label="Saya setuju untuk mengikuti syarat & ketentuan dari Hotto."
            description="Saya memahami bahwa pelanggaran terhadap syarat & ketentuan ini dapat membuat NIK saya diblacklist dari aplikasi agent Hotto ke depannya."
          />
        </>
      )}
    </Stack>
  );
};

export default AgentUserAgreementForm;
