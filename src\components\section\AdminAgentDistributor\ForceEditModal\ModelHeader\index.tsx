import Stepper from "@/components/common/Stepper";
import { Flex, Text, Title } from "@mantine/core";
import { FORCE_EDIT_STEPPER_DATA } from "..";
import { useMediaQuery } from "@mantine/hooks";
import { capitalize } from "@/utils/common-functions";

interface ForceEditModalHeaderProps {
  role: string;
  currStep: number;
  setCurrStep: (step: number) => void;
  nextStepValidation: () => boolean;
}

const ForceEditModalHeader = ({
  role,
  currStep,
  setCurrStep,
  nextStepValidation,
}: ForceEditModalHeaderProps) => {
  const isMobile = useMediaQuery("(max-width: 768px)");

  const getStepTitle = () => {
    switch (currStep) {
      case 0:
        return "Masukkan bukti permintaan request dari distributor.";
      case 1:
        return "Perubahan yang dibuat akan langsung mengubah data agent & berdampak ke website Hotto.";
      case 2:
        return "<PERSON><PERSON>ut adalah perubahan yang ingin Anda buat";
      default:
        return "";
    }
  };

  return (
    <Flex
      direction={"column"}
      align={"center"}
      gap={"md"}
      w={isMobile ? "100%" : "60%"}
      pb={"xs"}
    >
      <Title>{`Edit ${capitalize(role)}`}</Title>
      <Stepper
        active={currStep}
        steps={FORCE_EDIT_STEPPER_DATA}
        onStepClick={(step: number) => {
          // can go back, but if want to go next need to check validation
          if (step < currStep || nextStepValidation()) {
            setCurrStep(step);
          }
        }}
      />
      <Text c="text-light" ta={"center"} h={"2.5em"}>
        {getStepTitle()}
      </Text>
    </Flex>
  );
};

export default ForceEditModalHeader;
