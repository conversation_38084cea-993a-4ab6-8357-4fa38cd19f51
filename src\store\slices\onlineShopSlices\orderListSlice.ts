import {
  ApproveOrderPayload,
  OrdersData,
  OrdersResponse,
  OrderStatus,
} from "@/utils/types/online-shop";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import api from "@/lib/ecommerce";
import { APPROVE_ORDER, GET_ORDERS } from "@/utils/api-routes";
import { AxiosResponse } from "axios";
import { toast } from "sonner";
import { ITEMS_PER_PAGE, SortFilterValue } from "@/utils/constants";
import { AppDispatch, RootState } from "@/store";
import { ApiResponse } from "@/utils/types";

interface OrderListState {
  data: OrdersData;
  selectedOrders: (string | number)[];
  loading: {
    approve: boolean;
    orders: boolean;
  };
  filters: {
    status: OrderStatus;
    order: SortFilterValue;
    page: number;
    search: string;
  };
}

const initialState: OrderListState = {
  data: {
    page: 1,
    totalItems: 0,
    orders: [],
  },
  selectedOrders: [],
  loading: {
    approve: false,
    orders: false,
  },
  filters: {
    status: "all",
    order: "desc",
    page: 1,
    search: "",
  },
};

const orderListSlice = createSlice({
  name: "orderList",
  initialState,
  reducers: {
    setOrders(state, action: PayloadAction<OrderListState["data"]>) {
      state.data = action.payload;
    },
    setSelectedOrders(
      state,
      action: PayloadAction<OrderListState["selectedOrders"]>
    ) {
      state.selectedOrders = action.payload;
    },
    setLoading: (
      state,
      action: PayloadAction<{
        type: keyof OrderListState["loading"];
        loading: boolean;
      }>
    ) => {
      const { type, loading } = action.payload;
      state.loading[type] = loading;
    },
    setFilters: (state, action: PayloadAction<OrderListState["filters"]>) => {
      state.filters = action.payload;
    },
  },
});

const act = orderListSlice.actions;

export const fetchOrders =
  (
    status: OrderStatus = "all",
    page: number = 1,
    order: SortFilterValue = "desc",
    size: number = ITEMS_PER_PAGE,
    search: string = ""
  ) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(act.setOrders(initialState.data));
      dispatch(act.setSelectedOrders(initialState.selectedOrders));
      dispatch(act.setLoading({ type: "orders", loading: true }));
      const res: AxiosResponse<OrdersResponse> = await api.get(
        GET_ORDERS(status, page, order, size, search)
      );
      if (res.data.status_code === 200 && res.data.data.orders) {
        dispatch(act.setOrders(res.data.data));
      }
    } catch (error: unknown) {
      toast.error(
        "Gagal mendapatkan daftar order. Silahkan muat ulang halaman dan coba lagi."
      );
    } finally {
      dispatch(act.setLoading({ type: "orders", loading: false }));
    }
  };

export const approveOrder =
  (
    id: string | number,
    data: ApproveOrderPayload,
    type: "payment" | "resiNumber" | "completeOrder" = "payment"
  ) =>
  async (dispatch: AppDispatch, getState: () => RootState) => {
    try {
      dispatch(act.setLoading({ type: "approve", loading: true }));
      const res: AxiosResponse<ApiResponse<null>> = await api.patch(
        APPROVE_ORDER(id),
        data
      );
      if (res.data.status_code === 200) {
        toast.success(
          type === "payment"
            ? "Pembayaran berhasil dikonfirmasi."
            : type === "resiNumber"
            ? "Resi berhasil diinputkan."
            : "Pesanan berhasil diselesaikan."
        );

        const status = getState().orderList.filters.status;
        const order = getState().orderList.filters.order;
        const page = getState().orderList.filters.page;
        const search = getState().orderList.filters.search;
        dispatch(fetchOrders(status, page, order, ITEMS_PER_PAGE, search));

        return Promise.resolve();
      }
    } catch (error: unknown) {
      toast.error(
        type === "payment"
          ? "Gagal menyetujui pembayaran. Silahkan muat ulang halaman dan coba lagi."
          : type === "resiNumber"
          ? "Gagal menginputkan resi. Silahkan muat ulang halaman dan coba lagi."
          : "Gagal menyelesaikan pesanan. Silahkan muat ulang halaman dan coba lagi."
      );
      return Promise.reject(error);
    } finally {
      dispatch(act.setLoading({ type: "approve", loading: false }));
    }
  };

export const { setSelectedOrders, setFilters } = act;

export default orderListSlice.reducer;
