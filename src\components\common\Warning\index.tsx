import { Flex, Paper, Text } from "@mantine/core";
import Icon from "../Icon";

interface WarningProps {
  title: string;
  caption: string;
}

const Warning = ({ title, caption }: WarningProps) => {
  return (
    <Paper
      bg={"error.1"}
      w={"100%"}
      radius={"md"}
      bd="1px solid var(--mantine-color-error-filled)"
    >
      <Flex align={"center"} gap={"md"} w={"100%"} px={"lg"} py={"sm"}>
        <Icon icon="warning" size={26} />
        <Flex direction={"column"} ta={"left"}>
          <Text fw={700} c={"error"}>
            {title}
          </Text>
          <Text fw={500} inline>
            {caption}
          </Text>
        </Flex>
      </Flex>
    </Paper>
  );
};

export default Warning;
