import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./slices/authSlice";
import distributorRegisterReducer from "./slices/distributorRegisterSlice";
import userProfileReducer from "./slices/userProfileSlice";
import adminAgentDetailsReducer from "./slices/adminAgentDetailsSlice";
import adminAgentListInDistributorReducer from "./slices/adminAgentListInDistributorSlice";
import adminAgentListReducer from "./slices/adminAgentListSlice";
import adminRequest from "./slices/adminRequestSlice";
import adminBroadcastReducer from "./slices/adminBroadcastSlice";
import fileReducer from "./slices/fileSlice";
import requestReducer from "./slices/distributorRequestSlice";
import adminMarketplaceLinksReducer from "./slices/adminMarketplaceLinksSlice";
import userAgentDetailsReducer from "./slices/userAgentDetailsSlice";
import distributorAgentsReducer from "./slices/distributorAgentsSlice";
import distributorNotificationReducer from "./slices/distributorNotificationSlice";
import agentRequestReducer from "./slices/agentRequestSlice";
import regionReducer from "./slices/regionSlice";
import productDetailReducer from "./slices/onlineShopSlices/productDetailSlice";
import productListReducer from "./slices/onlineShopSlices/productListSlice";
import orderListReducer from "./slices/onlineShopSlices/orderListSlice";
import agentRegisterReducer from "./slices/agentRegisterSlice";
import dashboardReducer from "./slices/onlineShopSlices/dashboardSlice";
import overviewReducer from "./slices/onlineShopSlices/overviewSlice";
import waitlistReducer from "./slices/waitlistSlice";

export const store = configureStore({
  reducer: {
    auth: authReducer,
    distributorRegister: distributorRegisterReducer,
    agentRegister: agentRegisterReducer,
    userProfile: userProfileReducer,
    file: fileReducer,
    adminAgentDetails: adminAgentDetailsReducer,
    adminAgentListInDistributor: adminAgentListInDistributorReducer,
    adminAgentList: adminAgentListReducer,
    adminRequest: adminRequest,
    adminBroadcast: adminBroadcastReducer,
    request: requestReducer,
    adminMarketplaceLinks: adminMarketplaceLinksReducer,
    userAgentDetails: userAgentDetailsReducer,
    distributorAgents: distributorAgentsReducer,
    distributorNotification: distributorNotificationReducer,
    agentRequest: agentRequestReducer,
    region: regionReducer,
    productList: productListReducer,
    productDetail: productDetailReducer,
    orderList: orderListReducer,
    dashboard: dashboardReducer,
    overview: overviewReducer,
    waitlist: waitlistReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
