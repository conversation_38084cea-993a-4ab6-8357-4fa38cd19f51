"use client";

import { usePathname } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { Fragment, useEffect } from "react";
import { fetchAdminRequestsCount } from "@/store/slices/adminRequestSlice";
import { fetchNotificationCount } from "@/store/slices/distributorNotificationSlice";
import { Badge, Box, Flex, NavLink } from "@mantine/core";
import Link from "next/link";
import { useDisclosure } from "@mantine/hooks";
import { CaretRight } from "@/utils/icons";
import { IconType } from "react-icons/lib";
import classes from "./style.module.css";

export interface MenuItemProps {
  label: string;
  icon: React.ReactNode | [IconType, IconType];
  href?: string;
  links?: { label: string; href: string }[];
}

export interface MenuItemComponentProps extends MenuItemProps {
  onClick?: () => void;
}

const MenuItem = ({
  label,
  icon,
  href,
  onClick,
  links,
}: MenuItemComponentProps) => {
  const dispatch: AppDispatch = useDispatch();
  const pathName = usePathname();
  const isActive = href ? pathName.startsWith(href) : false;
  const hasLinks = Array.isArray(links);
  const singleIcon = !Array.isArray(icon);
  const [opened, handlers] = useDisclosure(hasLinks && isActive);

  const { data: profile } = useSelector(
    (state: RootState) => state.userProfile
  );

  const { newRequestCount } = useSelector(
    (state: RootState) => state.adminRequest
  );

  const { newNotificationCount } = useSelector(
    (state: RootState) => state.distributorNotification
  );

  // Fetch notification count when needed
  useEffect(() => {
    if (!newRequestCount && href === "/admin/request") {
      dispatch(fetchAdminRequestsCount("pending"));
    }
  }, [href]);

  useEffect(() => {
    if (!newNotificationCount && href === "/notification") {
      if (profile && profile.role && profile.role.name) {
        dispatch(fetchNotificationCount(profile.role.name));
      }
    }
  }, [href, profile]);

  const handleMenuClick = () => (hasLinks ? handlers.toggle() : onClick?.());

  const rightSection = () => {
    const notificationRoutes = [
      { path: "/notification", count: newNotificationCount },
      { path: "/admin/request", count: newRequestCount },
    ];

    const route = notificationRoutes.find(
      (route) => route.path === href && route.count > 0
    );

    return (
      <Flex align="center" gap="sm">
        {route && (
          <Badge fw={400} color="error" size="lg" circle>
            {route.count}
          </Badge>
        )}
        {hasLinks && <CaretRight />}
      </Flex>
    );
  };

  const linkProps = !hasLinks && href ? { href, component: Link } : {};
  const Icon = singleIcon ? icon : icon[isActive ? 1 : 0]({ size: 24 });
  const Wrapper = hasLinks ? Box : Fragment;

  return (
    <Wrapper>
      {/* @ts-expect-error dynamic link component */}
      <NavLink
        {...linkProps}
        label={label}
        childrenOffset={0}
        opened={opened}
        active={isActive}
        onClick={handleMenuClick}
        rightSection={rightSection()}
        leftSection={Icon}
        classNames={classes}
        data-flip-icon={label === "Broadcast" ? true : undefined}
      >
        {hasLinks &&
          links.map((link) => (
            <NavLink
              data-nested
              key={link.href}
              label={link.label}
              component={Link}
              href={link.href}
              classNames={classes}
              active={pathName === link.href}
            />
          ))}
      </NavLink>
    </Wrapper>
  );
};

export default MenuItem;
