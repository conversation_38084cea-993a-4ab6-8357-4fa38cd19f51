import { REFRESH_TOKEN } from '@/utils/api-routes'
import {
 ACCESS_TOKEN_VAR,
 REFRESH_TOKEN_VAR,
 ROLE_VAR,
} from '@/utils/constants'
import axios from 'axios'
import Cookies from 'js-cookie'
import { toast } from 'sonner'

const api = axios.create({
 baseURL: `${process.env.NEXT_PUBLIC_API_BASE_URL}/api`,
})

api.interceptors.request.use(
 (config) => {
  const token = Cookies.get(ACCESS_TOKEN_VAR)
  if (token && config.headers) {
   config.headers['Authorization'] = `Bearer ${token}`
  }
  return config
 },
 (error) => Promise.reject(error)
)

api.interceptors.response.use(
 (response) => response,
 async (error) => {
  const originalRequest = error.config

  if (error.response?.status === 401 && !originalRequest._retry) {
   originalRequest._retry = true

   try {
    const refreshToken =
     typeof window !== 'undefined'
      ? localStorage.getItem(REFRESH_TOKEN_VAR)
      : null

    if (refreshToken) {
     const { data } = await api.post(REFRESH_TOKEN, {
      refresh_token: refreshToken,
     })

     Cookies.set(ACCESS_TOKEN_VAR, data.access_token, { expires: 7 }) // Set cookie with expiration
     originalRequest.headers['Authorization'] = `Bearer ${data.access_token}`
     return api(originalRequest)
    }
   } catch (err) {
    console.error('Token refresh failed:', err)
    toast.info(
     'Your session has expired. You will be reddirected to login page in 3 second.'
    )
    Cookies.remove(ACCESS_TOKEN_VAR)
    localStorage.removeItem(REFRESH_TOKEN_VAR)
    const currRole = localStorage.getItem(ROLE_VAR)
    setTimeout(() => {
     if (currRole === 'admin') {
      window.location.href = '/login/admin'
     } else {
      window.location.href = '/login'
     }
    }, 3000)
   }
  }
  return Promise.reject(error)
 }
)

export default api
