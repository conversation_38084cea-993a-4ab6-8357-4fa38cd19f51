import { RequestMetadata } from "@/utils/types/request";
import { Flex } from "@mantine/core";
import InvalidMarketplaceLinkRow from "./InvalidMarketplaceLinkRow";

interface InvalidMarketplaceLinksProps {
  invalidLinks: RequestMetadata[];
  reason: string;
  openModal: (shopType: string) => void;
}

const InvalidMarketplaceLinks = ({
  invalidLinks,
  reason,
  openModal,
}: InvalidMarketplaceLinksProps) => {
  return (
    <Flex direction={"column"}>
      {invalidLinks.map((link, index) => (
        <InvalidMarketplaceLinkRow
          key={index}
          shopType={link.field}
          shopUrl={link.oldValue}
          status={link.status}
          reason={reason}
          openModal={() => {
            openModal(link.field);
          }}
        />
      ))}
    </Flex>
  );
};

export default InvalidMarketplaceLinks;
