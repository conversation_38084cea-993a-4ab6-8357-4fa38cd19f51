import Loop from "@/components/common/Loop";
import SearchBar from "@/components/common/SearchBar";
import Chevron from "@/components/icons/Chevron";
import { Group, Text, Menu, Button } from "@mantine/core";

const AgentsListHeader: React.FC = () => {
  return (
    <Group justify="space-between">
      <Group>
        <Text size="xl">List of Agents</Text>
        <SearchBar href="/agent/search" placeholder="Cari Agent" />
      </Group>
      {/* TODO: implement download button */}
      <Menu shadow="md" width={150}>
        <Menu.Target>
          <Button rightSection={<Chevron direction="down" fontSize={18} />}>
            Unduh
          </Button>
        </Menu.Target>
        <Menu.Dropdown>
          <Loop count={5}>
            <Menu.Item>Menu</Menu.Item>
          </Loop>
        </Menu.Dropdown>
      </Menu>
    </Group>
  );
};

export default AgentsListHeader;
