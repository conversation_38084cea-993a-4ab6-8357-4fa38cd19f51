import { isEmpty } from "@/utils/common-functions";
import {
  Input,
  InputWrapperProps,
  Group,
  ButtonProps,
  Button,
  ComboboxItem,
  GroupProps,
} from "@mantine/core";
import { useUncontrolled } from "@mantine/hooks";
import { PropsWithChildren } from "react";

export interface SelectButtonProps<T extends string = string>
  extends Omit<InputWrapperProps, "onChange"> {
  data: (ComboboxItem & {
    rightSection?: ButtonProps["rightSection"];
    leftSection?: ButtonProps["leftSection"];
  })[];
  value?: T;
  defaultValue?: T;
  allowDeselect?: boolean;
  connected?: boolean;
  alt?: boolean;
  onChange?: (value: T | null) => void;
  disabled?: ButtonProps["disabled"];
  size?: ButtonProps["size"];
  color?: ButtonProps["color"];
  radius?: ButtonProps["radius"];
  gap?: GroupProps["gap"];
}

const SelectButton = <T extends string = string>({
  data,
  value,
  defaultValue,
  allowDeselect,
  connected,
  alt,
  onChange,
  disabled,
  size,
  color = "primary",
  radius,
  gap = "xs",
  ...props
}: SelectButtonProps<T>) => {
  if (isEmpty(data)) throw new Error("Data is required for SelectButton.");

  const [_value, handleChange] = useUncontrolled<T | null>({
    value,
    defaultValue,
    onChange,
  });

  const handleSelect = (value: string, disabled?: boolean) => {
    if (disabled || (_value === value && !allowDeselect)) return;
    handleChange(_value === value && allowDeselect ? null : (value as T));
    handleChange(_value === value && allowDeselect ? null : (value as T));
  };

  const GroupComponent = ({ children }: PropsWithChildren) => {
    return connected ? (
      <Button.Group>{children}</Button.Group>
    ) : (
      <Group gap={gap}>{children}</Group>
    );
  };

  return (
    <Input.Wrapper {...props}>
      <GroupComponent>
        {data.map((item, index) => {
          const selected = _value === item.value;
          return (
            <Button
              key={`button-select-${item.value}-${index}`}
              size={size}
              color={color}
              radius={radius}
              disabled={item.disabled || disabled}
              rightSection={item.rightSection}
              leftSection={item.leftSection}
              onClick={() => handleSelect(item.value, item.disabled)}
              c={alt ? "black" : undefined}
              variant={selected ? (alt ? "light" : "filled") : "outline"}
              aria-checked={selected}
              role="radio"
              styles={{
                root: {
                  border: alt
                    ? selected
                      ? `1.5px solid var(--mantine-color-${color}-filled)`
                      : `1.5px solid var(--mantine-color-gray-filled)`
                    : undefined,
                },
              }}
            >
              {item.label}
            </Button>
          );
        })}
      </GroupComponent>
    </Input.Wrapper>
  );
};

export default SelectButton;
