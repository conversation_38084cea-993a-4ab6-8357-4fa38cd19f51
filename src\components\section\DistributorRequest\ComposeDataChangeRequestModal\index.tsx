import { Drawer, Flex, Modal } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import DataChangeModalContent from "./DataChangeModalContent";
import { useEffect, useState } from "react";
import { AgentDistributorDetailsForceUpdateData } from "@/utils/types/distributor-agent";
import { getValueFromAgentDetails } from "@/utils/common-functions";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { fetchUserAgentDetails } from "@/store/slices/userAgentDetailsSlice";
import {
  clearDataChangeDraft,
  fetchDataChangeRequestDraft,
} from "@/store/slices/distributorRequestSlice";
import { DATA_CHANGES_REQUEST_FIELD_KEYS } from "@/utils/constants";

interface RequestModalProps {
  id?: number;
  opened: boolean;
  onClose: () => void;
}

const ComposeDataChangeRequestModal = ({
  id,
  opened,
  onClose,
}: RequestModalProps) => {
  const dispatch: AppDispatch = useDispatch();

  const { data: agentDetails } = useSelector(
    (state: RootState) => state.userAgentDetails
  );
  const { dataChangeDraft } = useSelector((state: RootState) => state.request);

  const [currStep, setCurrStep] = useState(0);
  const [changes, setChanges] =
    useState<AgentDistributorDetailsForceUpdateData>({});

  useEffect(() => {
    if (opened) {
      dispatch(fetchUserAgentDetails());
      if (id) {
        dispatch(fetchDataChangeRequestDraft(id));
      } else {
        dispatch(clearDataChangeDraft());
      }
    }
  }, [opened, dispatch, id]);

  useEffect(() => {
    if (dataChangeDraft && dataChangeDraft.fieldChange) {
      for (const key of DATA_CHANGES_REQUEST_FIELD_KEYS) {
        if (getValueFromAgentDetails(key, dataChangeDraft.fieldChange)) {
          handleChanges(
            getValueFromAgentDetails(
              key,
              dataChangeDraft.fieldChange
            ) as string,
            key
          );
        }
      }
    }
  }, [dataChangeDraft]);

  const handleChanges = (data: string | undefined, key: string) => {
    if (!agentDetails) return;
    setChanges((prevChanges) => {
      const newChanges: AgentDistributorDetailsForceUpdateData = {
        ...prevChanges,
      };
      // Get the original value
      const originalValue = getValueFromAgentDetails(key, agentDetails);
      if (data === originalValue) {
        if (key.includes(".")) {
          // Handle nested keys
          const keys = key.split(".");
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          let currentObject: any = newChanges;

          keys.slice(0, keys.length - 1).forEach((k) => {
            if (!currentObject[k]) {
              currentObject[k] = {};
            }
            currentObject = currentObject[k];
          });
          // Delete the last nested key
          delete currentObject[keys[keys.length - 1]];
        } else {
          // If it's a simple key, delete it from changes
          delete newChanges[key];
        }
      } else {
        // If the data is different, update the value
        if (key.includes(".")) {
          // Handle nested keys
          const keys = key.split(".");
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          let currentObject: any = newChanges;
          keys.slice(0, keys.length - 1).forEach((k) => {
            if (!currentObject[k]) {
              currentObject[k] = {};
            }
            currentObject = currentObject[k];
          });
          currentObject[keys[keys.length - 1]] = data;
        } else {
          newChanges[key] = data;
        }
      }
      return newChanges;
    });
  };

  const handleCloseModal = () => {
    setCurrStep(0);
    setChanges({});
    dispatch(clearDataChangeDraft());
    onClose();
  };

  const isMobile = useMediaQuery("(max-width: 768px)");

  const renderModalChildren = () => {
    return (
      <Flex
        direction={"column"}
        align={"center"}
        justify={"center"}
        gap={"sm"}
        p={isMobile ? 0 : "lg"}
      >
        {agentDetails && (
          <DataChangeModalContent
            onClose={handleCloseModal}
            id={id}
            currStep={currStep}
            setCurrStep={setCurrStep}
            changes={changes}
            handleChanges={handleChanges}
            agentDetails={agentDetails}
            existingReason={dataChangeDraft?.reason}
          />
        )}
      </Flex>
    );
  };

  return (
    <>
      {isMobile ? (
        <Drawer
          opened={opened}
          onClose={handleCloseModal}
          radius={"8px 8px 0 0"}
          size={"90vh"}
          position="bottom"
        >
          {opened && renderModalChildren()}
        </Drawer>
      ) : (
        <Modal
          opened={opened}
          onClose={handleCloseModal}
          radius={8}
          size={"xl"}
          centered
        >
          {opened && renderModalChildren()}
        </Modal>
      )}
    </>
  );
};

export default ComposeDataChangeRequestModal;
