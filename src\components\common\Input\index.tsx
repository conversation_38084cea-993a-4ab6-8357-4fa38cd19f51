import {
  Flex,
  Input as MantineInput,
  Paper,
  InputProps as MantineInputProps,
} from "@mantine/core";
import Icon from "../Icon";

interface InputProps extends MantineInputProps {
  icon?: string;
  placeholder?: string;
  type?: string;
  name?: string;
  value?: string;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  defaultValue?: string;
  readOnly?: boolean;
}

const Input = ({ icon, ...props }: InputProps) => {
  const leftSection = !icon ? null : (
    <Paper>
      <Flex align="center" justify="flex-start" gap="6px">
        <Icon icon={icon} size={14} />
        <Paper
          h={"1em"}
          w={"1px"}
          radius={0}
          style={{
            borderLeft: `1px solid var(--mantine-color-bg-filled)`,
          }}
        />
      </Flex>
    </Paper>
  );

  return <MantineInput {...props} leftSection={leftSection} />;
};

export default Input;
