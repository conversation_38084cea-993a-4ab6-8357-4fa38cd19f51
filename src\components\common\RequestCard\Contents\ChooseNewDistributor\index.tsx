import { RequestCardData } from "@/utils/types/request";
import { Divider, Flex, Text, Button } from "@mantine/core";
import RequestCardHeader from "../../RequestCardHeader";
import useBreakpoints from "@/hooks/useBreakpoints";
import ReviewStatus from "@/components/common/ReviewStatus";
import { useState } from "react";
import ChooseDistributorModal from "./ChooseDistributorModal";

const ChooseNewDistributorRequestCard = ({ ...request }: RequestCardData) => {
  const { mobile } = useBreakpoints();
  const [modalOpened, setModalOpened] = useState(false);

  return (
    <Flex direction={"column"}>
      <ChooseDistributorModal
        opened={modalOpened}
        onClose={() => setModalOpened(false)}
        requestId={request.id}
      />
      <RequestCardHeader
        leftText={`Anda harus mencari distributor baru!`}
        rightText={`Sent`}
        leftCaption={"Distributor Anda telah berhenti bekerja dengan <PERSON>."}
        rightItalic={false}
        updatedAt={request.updatedAt || request.createdAt}
      />
      <Divider />
      {request.metadatas[0] ? (
        <Flex
          direction={mobile ? "column" : "row"}
          align={mobile ? "flex-start" : "center"}
          justify={"space-between"}
          p={"lg"}
          gap={"lg"}
        >
          <Text
            fw={500}
          >{`Terdapat ${request.metadatas[0].oldValue} distributor lain di area Anda yang menerima aplikasi`}</Text>
          <Flex w={mobile ? "100%" : "fit-content"} justify={"flex-end"}>
            {request.metadatas[0].status === "pending" ? (
              <Text fw={400} fs={"italic"} ta={"right"}>
                Sedang direview
              </Text>
            ) : !request.metadatas[0].status ? (
              <Button onClick={() => setModalOpened(true)}>View</Button>
            ) : (
              <ReviewStatus status={request.metadatas[0].status} />
            )}
          </Flex>
        </Flex>
      ) : (
        <Text c={"text-light"} p={"lg"} fw={500}>
          Tidak ada detail data
        </Text>
      )}
    </Flex>
  );
};

export default ChooseNewDistributorRequestCard;
