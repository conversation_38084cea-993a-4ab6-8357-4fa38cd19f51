export enum NotificationType {
  // Requests:
  ForceDataChange = "force_change_field",
  DataChangeRequestStatus = "change_field",
  AgentTermination = "terminate_agent",
  AgentApplication = "new_agent",
  // Broadcasts:
  PublicAnnouncement = "public_announcement",
  PrivateAnnouncement = "private_announcement",
  WarningLetter = "warning_letter",
  Promotion = "promotion",
  // Force Distri PHK
  ForceTermination = "force_terminate_agent",
}

export interface NotificationResponse {
  notifications: NotificationData[];
  page: number;
  totalItems: number;
}

export interface NotificationData {
  id: number;
  title: string;
  body: string;
  type: NotificationType;
  requesterName?: string;
  requesterRole?: "Agent" | "Distributor" | "Admin";
  metadata: NotificationMetadata[];
  reason?: string;
  documents?: string[];
  createdAt?: string;
  updatedAt?: string;
}

export interface NotificationMetadata {
  id: number;
  field: string;
  oldValue: string;
  newValue: string;
  complain?: string;
  rejectReason: string;
  status: "pending" | "accept" | "reject";
  requestStep: 1 | 2 | 3;
}

export interface NotificationMetadata {
  name: string;
}
