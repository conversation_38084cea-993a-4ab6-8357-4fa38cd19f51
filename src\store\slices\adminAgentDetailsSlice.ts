import api from "@/lib/axios";
import {
  FORCE_UPDATE_AGENT_DETAILS,
  GET_AGENT_DETAILS,
  FORCE_TERMINATE_DISTRIBUTOR,
} from "@/utils/api-routes";
import { createSlice } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { AppDispatch } from "..";
import {
  AgentDistributorDetailsData,
  AgentDistributorDetailsForceUpdateData,
  AgentDistributorDetailsResponse,
} from "@/utils/types/distributor-agent";

const adminAgentDetailsSlice = createSlice({
  name: "adminAgentDetails",
  initialState: {
    loading: false,
    error: undefined,
    data: {} as AgentDistributorDetailsData,
  },
  reducers: {
    setError(state, action) {
      state.error = action.payload;
      if (action.payload) toast.error(action.payload);
    },
    setLoading(state, action) {
      state.loading = action.payload;
    },
    setData(state, action) {
      state.data = action.payload;
    },
  },
});

export const fetchAdminAgentDetails =
  (uoc: string) => async (dispatch: AppDispatch) => {
    try {
      dispatch(adminAgentDetailsSlice.actions.setError(undefined));
      dispatch(adminAgentDetailsSlice.actions.setLoading(true));
      const res: { data: AgentDistributorDetailsResponse } = await api.get(
        GET_AGENT_DETAILS(uoc)
      );
      if (res.data.status_code === 200 && res.data.data) {
        dispatch(adminAgentDetailsSlice.actions.setData(res.data.data));
      } else {
        dispatch(adminAgentDetailsSlice.actions.setError(res.data.message));
      }
    } catch {
      dispatch(
        adminAgentDetailsSlice.actions.setError("Failed get agent details.")
      );
    } finally {
      dispatch(adminAgentDetailsSlice.actions.setLoading(false));
    }
  };

export const forceUpdateAgent =
  (
    uoc: string,
    requestEvidences: string[],
    fieldsToUpdate: AgentDistributorDetailsForceUpdateData,
    reason: string,
    refetchData?: boolean
  ) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(adminAgentDetailsSlice.actions.setLoading(true));
      const res = await api.patch(FORCE_UPDATE_AGENT_DETAILS(uoc), {
        requestEvidences,
        fieldsToUpdate,
        reason,
      });
      if (res) {
        toast.success("Successfully force updated agent/distributor data.");
        if (refetchData) {
          dispatch(fetchAdminAgentDetails(uoc));
        }
      }
    } catch {
      dispatch(
        adminAgentDetailsSlice.actions.setError(
          "Failed to force update agent/distributor data."
        )
      );
    } finally {
      dispatch(adminAgentDetailsSlice.actions.setLoading(false));
    }
  };

export const terminateDistributor =
  (uoc: string, reason: string, onSuccessDelete?: () => void) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(adminAgentDetailsSlice.actions.setLoading(true));
      const res = await api.put(FORCE_TERMINATE_DISTRIBUTOR(uoc), {
        reason,
      });
      if (res) {
        toast.success("PHK Distributor berhasil dilakukan.");
        if (onSuccessDelete) {
          onSuccessDelete();
        }
      }
    } catch {
      dispatch(
        adminAgentDetailsSlice.actions.setError(
          "Gagal melakukan PHK Distributor."
        )
      );
    } finally {
      dispatch(adminAgentDetailsSlice.actions.setLoading(false));
    }
  };

export default adminAgentDetailsSlice.reducer;
