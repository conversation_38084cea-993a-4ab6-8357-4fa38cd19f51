"use client";

import { <PERSON>er, Flex, Modal, Paper, Button } from "@mantine/core";
import ForceEditModalHeader from "./ModelHeader";
import { useState } from "react";
import { StepData } from "@/components/common/Stepper";
import { ForceEditModalContentContainer } from "./style";
import { useMediaQuery } from "@mantine/hooks";
import ForceEditForm from "./ForceEditModalContents/ForceEditForm";
import {
  AgentDistributorDetailsData,
  AgentDistributorDetailsForceUpdateData,
} from "@/utils/types/distributor-agent";
import ChangesSubmitted from "./ForceEditModalContents/ChangesSubmitted";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { forceUpdateAgent } from "@/store/slices/adminAgentDetailsSlice";
import { SUPER_ADMIN_ROLE } from "@/utils/constants";
import MultipleFileInput from "@/components/common/MultipleFileInput";
import {
  checkObjectHasValue,
  getValueFromAgentDetails,
} from "@/utils/common-functions";

interface ForceEditModalProps {
  opened: boolean;
  onClose: () => void;
  agentDetails: AgentDistributorDetailsData;
}

export const FORCE_EDIT_STEPPER_DATA: StepData[] = [
  {
    index: 0,
    label: "Bukti Request",
  },
  {
    index: 1,
    label: "Informasi Distributor",
  },
  {
    index: 2,
    label: "Konfirmasi",
  },
];

const ForceEditModal = ({
  opened,
  onClose,
  agentDetails,
}: ForceEditModalProps) => {
  const dispatch: AppDispatch = useDispatch();
  const { role } = useSelector((state: RootState) => state.auth);
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [currStep, setCurrStep] = useState(0);
  const [evidences, setEvidences] = useState<string[]>([]);
  const [reason, setReason] = useState<string>("");
  const [changes, setChanges] =
    useState<AgentDistributorDetailsForceUpdateData>({});
  const [isChangesValid, setIsChangesValid] = useState(false);

  const prevStep = () => {
    if (currStep > 0) {
      setCurrStep(currStep - 1);
    }
  };

  const nextStep = () => {
    if (currStep < FORCE_EDIT_STEPPER_DATA.length - 1) {
      setCurrStep(currStep + 1);
    }
  };

  const handleSubmitEdit = () => {
    const isSuperAdmin = role === SUPER_ADMIN_ROLE;
    dispatch(
      forceUpdateAgent(
        agentDetails.uoc,
        evidences,
        changes,
        reason,
        isSuperAdmin
      )
    );
    handleCloseModal();
  };

  const handleChanges = (data: string | undefined, key: string) => {
    setChanges((prevChanges) => {
      const newChanges: AgentDistributorDetailsForceUpdateData = {
        ...prevChanges,
      };

      // Get the original value
      const originalValue = getValueFromAgentDetails(key, agentDetails);

      if (data === originalValue) {
        if (key.includes(".")) {
          // Handle nested keys
          const keys = key.split(".");
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          let currentObject: any = newChanges;

          keys.slice(0, keys.length - 1).forEach((k) => {
            if (!currentObject[k]) {
              currentObject[k] = {};
            }
            currentObject = currentObject[k];
          });
          // Delete the last nested key
          delete currentObject[keys[keys.length - 1]];
        } else {
          // If it's a simple key, delete it from changes
          delete newChanges[key];
        }
      } else {
        // If the data is different, update the value
        if (key.includes(".")) {
          // Handle nested keys
          const keys = key.split(".");
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          let currentObject: any = newChanges;
          keys.slice(0, keys.length - 1).forEach((k) => {
            if (!currentObject[k]) {
              currentObject[k] = {};
            }
            currentObject = currentObject[k];
          });
          currentObject[keys[keys.length - 1]] = data;
        } else {
          newChanges[key] = data;
        }
      }

      return newChanges;
    });
  };

  const handleCloseModal = () => {
    setCurrStep(0);
    setChanges({});
    onClose();
  };

  const checkNextStepEligibility = (): boolean => {
    switch (currStep) {
      case 0:
        return evidences.length > 0;
      case 1:
        return isChangesValid && checkObjectHasValue(changes);
      case 2:
        return reason.length > 0;
      default:
        return false;
    }
  };

  const renderStepContent = () => {
    switch (currStep) {
      case 0:
        return (
          <MultipleFileInput
            fileType="images_and_document"
            uploadedFiles={evidences}
            setUploadedFiles={setEvidences}
            prefixUrl={`bukti-request/${agentDetails.id}`}
            label="Bukti Request"
          />
        );
      case 1:
        return (
          <ForceEditForm
            agentDetails={agentDetails}
            changes={changes}
            handleChanges={handleChanges}
            setChangesValidity={setIsChangesValid}
          />
        );
      case 2:
        return (
          <ChangesSubmitted
            changes={changes}
            reason={reason}
            setReason={setReason}
          />
        );
      default:
        return null;
    }
  };
  const renderModalChildren = () => {
    return (
      <Flex
        direction={"column"}
        align={"center"}
        justify={"center"}
        className="modal-content"
        p={isMobile ? 0 : "lg"}
      >
        {/* Header: Title + Stepper */}
        <ForceEditModalHeader
          role={agentDetails.roleName}
          currStep={currStep}
          setCurrStep={setCurrStep}
          nextStepValidation={checkNextStepEligibility}
        />

        {/* Content: Instruction + Form */}
        <ForceEditModalContentContainer>
          {renderStepContent()}
        </ForceEditModalContentContainer>

        <Flex
          direction={"column"}
          w={"100%"}
          pos={"relative"}
          style={{ zIndex: 2 }}
        >
          {currStep === 2 && (
            <Paper
              pos={"absolute"}
              c={"error"}
              bg={"error.1"}
              p={"sm"}
              px={isMobile ? "sm" : "lg"}
              radius={"8px 8px 0 0 "}
              w={isMobile ? "108%" : "110%"}
              ml={isMobile ? "-4%" : "-5%"}
              ta={isMobile ? "center" : "left"}
              fw={600}
              top={"-3em"}
            >
              Pastikan data yang Anda update adalah benar!
            </Paper>
          )}
          <Flex
            direction={isMobile ? "column-reverse" : "row"}
            justify={"space-between"}
            pt={"md"}
            bg={"bg"}
            align={"center"}
            w={"100%"}
            gap={"sm"}
          >
            <Button onClick={prevStep} variant="subtle">
              Kembali
            </Button>
            <Button
              disabled={!checkNextStepEligibility()}
              onClick={
                currStep < FORCE_EDIT_STEPPER_DATA.length - 1
                  ? nextStep
                  : handleSubmitEdit
              }
            >
              {currStep < FORCE_EDIT_STEPPER_DATA.length - 1
                ? "Selanjutnya"
                : "Edit"}
            </Button>
          </Flex>
        </Flex>
      </Flex>
    );
  };

  return (
    <>
      {isMobile ? (
        <Drawer
          opened={opened}
          onClose={onClose}
          size={"92vh"}
          radius={"8px 8px 0 0"}
          position="bottom"
        >
          {opened && renderModalChildren()}
        </Drawer>
      ) : (
        <Modal
          opened={opened}
          onClose={handleCloseModal}
          radius={8}
          size={"xl"}
        >
          {opened && renderModalChildren()}
        </Modal>
      )}
    </>
  );
};

export default ForceEditModal;
