import UpdateLog from "@/components/section/AdminAgentDistributor/UpdateLog";
import { UpdateLogData } from "@/utils/types/distributor-agent";
import { Divider, Flex, Paper, Text } from "@mantine/core";
import React from "react";

interface PreviousUpdateLogsProps {
  updateLogs: UpdateLogData[];
}

const PreviousUpdateLogs = ({ updateLogs }: PreviousUpdateLogsProps) => {
  return (
    <Paper radius={"8px"} bg={"bg"} p={"md"} w={"100%"} h={"fit-content"}>
      <Flex direction={"column"} w={"100%"} gap={"sm"}>
        <Text fw={600}>Update Log</Text>
        {updateLogs.slice(0, 2).map((log, i) => (
          <React.Fragment key={i}>
            <UpdateLog log={{ ...log, status: undefined }} />
            {i === 0 && updateLogs.length > 1 && <Divider />}
          </React.Fragment>
        ))}
      </Flex>
    </Paper>
  );
};

export default PreviousUpdateLogs;
