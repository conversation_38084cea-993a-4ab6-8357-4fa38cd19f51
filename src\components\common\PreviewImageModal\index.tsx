import { Image, Modal } from "@mantine/core";

interface PreviewImageModalProps {
  opened: boolean;
  onClose: () => void;
  imageUrl: string;
}

const PreviewImageModal = ({
  opened,
  onClose,
  imageUrl,
}: PreviewImageModalProps) => {
  return (
    <Modal opened={opened} onClose={onClose} radius={8} size={"xl"} centered>
      <Image
        width={"100%"}
        height={"auto"}
        fit="contain"
        src={imageUrl}
        alt="evidence"
      />
    </Modal>
  );
};

export default PreviewImageModal;
