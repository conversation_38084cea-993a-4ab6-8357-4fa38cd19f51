import Dot from "@/components/icons/Dot";
import { Flex, Paper, Text } from "@mantine/core";
import React from "react";

export interface TotalProductProps {
  type: string;
  total: number;
  color?: string;
}

const TotalProduct = ({ type, total, color }: TotalProductProps) => {
  return (
    <Paper
      withBorder
      px={12}
      py={6}
      bg="bg"
      w="fit-content"
      aria-label={`Total ${type}`}
      role="product total"
    >
      <Flex justify="center" align="center" gap="sm">
        <Dot color={`var(--mantine-color-${color}-0)`} fontSize={24} />
        <Flex direction="column">
          <Text size="sm" c="text">
            Total {type}
          </Text>
          <Text size="sm">{total} RO</Text>
        </Flex>
      </Flex>
    </Paper>
  );
};

export default TotalProduct;
