import {
  getOrderStatuslabel,
  OrderDetail,
  OrderStatusLabel,
  OrderStatusType,
} from "@/utils/types/online-shop";
import {
  Anchor,
  Box,
  Card,
  NumberFormatter,
  Spoiler,
  Stack,
  Table,
  Text,
} from "@mantine/core";
import OrderCardHeader from "./Header";
import OrderCardFooter from "./Footer";
import ActivationStatus from "@/components/common/ActivationStatus";
import OrderProductCard from "./ProductCard";

export interface OrderCardProps {
  data: OrderDetail;
  checked?: boolean;
  onCheckboxChange?: (checked: boolean) => void;
  onEvidenceClick?: (viewOnly: boolean) => void;
  onResiInputClick?: () => void;
  onComplaintClick?: (orderId: string) => void;
}

const OrderCard: React.FC<OrderCardProps> = ({
  data,
  checked,
  onCheckboxChange,
  onEvidenceClick,
  onResiInputClick,
  onComplaintClick,
}) => {
  return (
    <Card bg="bg" withBorder>
      <Card.Section inheritPadding withBorder p="lg">
        <OrderCardHeader
          invoice={data.orderID}
          customer={data.custName}
          date={data.createdAt}
          checked={checked}
          onCheckboxChange={onCheckboxChange}
        />
      </Card.Section>
      <Box pl="2rem" py="lg">
        <Table withRowBorders={false}>
          <Table.Tbody style={{ verticalAlign: "top" }}>
            <Table.Tr>
              <Table.Td w="40%">
                <Spoiler
                  maxHeight={200}
                  showLabel="Lihat semua"
                  hideLabel="Sembunyikan"
                >
                  <Stack>
                    {data.orderItems.map((item, index) => (
                      <OrderProductCard key={index} product={item} />
                    ))}
                  </Stack>
                </Spoiler>
              </Table.Td>
              <Table.Td w="15%">
                <Text fw={500}>
                  <NumberFormatter value={data.totalPrice} />
                </Text>
                <Text>
                  {data.orderItems.reduce((total, item) => total + item.qty, 0)}{" "}
                  Produk
                </Text>
                <Text c="text" mt="xs">
                  Dibayar dengan {data.paymentMethodText}
                </Text>
                {data.evidence && (
                  <Anchor
                    underline="always"
                    onClick={() => onEvidenceClick?.(true)}
                  >
                    Lihat bukti
                  </Anchor>
                )}
              </Table.Td>
              <Table.Td w="15%">
                <ActivationStatus
                  customText={getOrderStatuslabel(
                    data.status as OrderStatusType
                  )}
                  status="active"
                />
                {data.status === OrderStatusType.WaitingForDelivery && (
                  <Text mt="xs" c="text">
                    Pesanan harus segera dikirimkan
                  </Text>
                )}
              </Table.Td>
              <Table.Td w="15%">
                <Text>{data.courierFullName}</Text>
                <Text c="text">
                  {data.resiNumber
                    ? `Nomor Resi: ${data.resiNumber}`
                    : ` Drop off ke ${data.courierFullName}`}
                </Text>
              </Table.Td>
              <Table.Td w="15%">
                <Text>{data.destinationAddress}</Text>
                <Text c="text" mt="xs">
                  {data.orderItems.reduce((total, item) => total + item.qty, 0)}{" "}
                  Produk
                </Text>
              </Table.Td>
            </Table.Tr>
          </Table.Tbody>
        </Table>
      </Box>
      <Card.Section inheritPadding withBorder p="lg">
        <OrderCardFooter
          data={data}
          onConfirmPaymentClick={() => onEvidenceClick?.(false)}
          onResiInputClick={onResiInputClick}
          onComplaintClick={() => onComplaintClick?.(data.orderID)}
        />
      </Card.Section>
    </Card>
  );
};

export default OrderCard;
