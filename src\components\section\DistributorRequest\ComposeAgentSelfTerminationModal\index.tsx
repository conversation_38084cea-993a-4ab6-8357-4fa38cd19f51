import {
  Textare<PERSON>,
  Drawer,
  Flex,
  Modal,
  Text,
  Title,
  Button,
} from "@mantine/core";
import useBreakpoints from "@/hooks/useBreakpoints";
import ButtonGroup from "@/components/common/ButtonGroup";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { ComposeAgentSelfTerminationData } from "@/utils/types/request";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import {
  clearAgentTerminationDraft,
  createAgentSelfTerminationRequest,
  fetchAgentTerminationRequestDraft,
  updateAgentTerminationRequest,
} from "@/store/slices/distributorRequestSlice";
import { toast } from "sonner";
import MultipleFileInput from "@/components/common/MultipleFileInput";

const schema = yup.object({
  reason: yup.string().required("Alasan harus diisi"),
  evidences: yup.array(yup.string().required()).default([]),
});

interface RequestModalProps {
  id?: number;
  opened: boolean;
  onClose: () => void;
}

const ComposeAgentSelfTerminationModal = ({
  id,
  opened,
  onClose,
}: RequestModalProps) => {
  const form = useForm({
    resolver: yupResolver<ComposeAgentSelfTerminationData>(schema),
    mode: "onChange",
    defaultValues: {
      reason: "",
      evidences: [],
    },
  });

  const dispatch: AppDispatch = useDispatch();
  const { agentTerminationDraft: draft } = useSelector(
    (state: RootState) => state.request
  );

  useEffect(() => {
    if (opened) {
      if (id) {
        dispatch(fetchAgentTerminationRequestDraft(id));
        return;
      }
      dispatch(clearAgentTerminationDraft());
    }
  }, [opened, id]);

  useEffect(() => {
    if (draft) {
      form.setValue("reason", draft.reason || "");
      form.setValue("evidences", draft.evidences || []);
      form.trigger();
    }
  }, [draft]);

  const handleCloseModal = () => {
    form.reset();
    onClose();
  };

  const handleSaveAsDraftButtonClick = () => {
    if (id) {
      dispatch(
        updateAgentTerminationRequest(
          id,
          "",
          form.watch("reason"),
          form.watch("evidences")
        )
      );
    } else {
      dispatch(
        createAgentSelfTerminationRequest(
          form.watch("reason"),
          form.watch("evidences"),
          true
        )
      );
    }
    handleCloseModal();
  };

  const handleSubmitButtonClick = () => {
    if (form.formState.isValid) {
      dispatch(
        createAgentSelfTerminationRequest(
          form.watch("reason"),
          form.watch("evidences"),
          false,
          id
        )
      );
    } else {
      toast.error("Mohon lengkapi semua data yang dibutuhkan");
    }
    handleCloseModal();
  };

  const { mobile } = useBreakpoints();

  const renderModalChildren = (
    <Flex
      direction="column"
      align="center"
      justify="center"
      gap="sm"
      p={mobile ? 0 : "lg"}
    >
      <Flex direction="column" ta="center" justify="center" gap="xs">
        <Title order={2}>Pemutusan Hubungan kerja Agent</Title>
        <Text c="text" fw={500} size="sm">
          Pemutusan hubungan kerja akan menghentikan hubungan kerja antara Anda
          dengan agent terkait. Mohon untuk secara bijak membuat keputusan ini.
        </Text>
      </Flex>

      <Textarea
        required
        label="Alasan pemutusan hubungan"
        autosize
        minRows={4}
        maxRows={8}
        w="100%"
        error={form.formState.errors.reason?.message}
        styles={{
          label: { fontSize: "1em" },
        }}
        {...form.register("reason")}
      />

      <MultipleFileInput
        prefixUrl={`agent-self-termination${id ? `/${id}` : ""}`}
        label="Dokumen Bukti"
        uploadedFiles={form.watch("evidences")}
        setUploadedFiles={(evidences) => {
          form.setValue("evidences", evidences);
          form.trigger("evidences");
        }}
      />
      {form.formState.errors?.evidences && (
        <Text c={"red"} size="sm">
          {form.formState.errors.evidences.message}
        </Text>
      )}

      <ButtonGroup justify={"space-between"} w={"100%"} pt={"xl"}>
        {mobile ? (
          <Button variant="subtle" onClick={handleSaveAsDraftButtonClick}>
            Simpan sebagai draft
          </Button>
        ) : (
          <Button variant="subtle" onClick={handleCloseModal}>
            Kembali
          </Button>
        )}
        <ButtonGroup>
          {!mobile && (
            <Button variant="subtle" onClick={handleSaveAsDraftButtonClick}>
              Simpan sebagai draft
            </Button>
          )}
          <Button
            onClick={handleSubmitButtonClick}
            disabled={!form.formState.isValid}
          >
            Kirim
          </Button>
        </ButtonGroup>
      </ButtonGroup>
    </Flex>
  );

  return (
    <>
      {mobile ? (
        <Drawer
          opened={opened}
          onClose={handleCloseModal}
          radius={"8px 8px 0 0"}
          size={"lg"}
          position="bottom"
        >
          {renderModalChildren}
        </Drawer>
      ) : (
        <Modal
          opened={opened}
          onClose={handleCloseModal}
          radius={8}
          size={"xl"}
          centered
        >
          {renderModalChildren}
        </Modal>
      )}
    </>
  );
};

export default ComposeAgentSelfTerminationModal;
