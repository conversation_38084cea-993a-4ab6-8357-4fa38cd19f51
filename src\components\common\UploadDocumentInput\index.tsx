import React, {
  ChangeEvent,
  DragEvent,
  useEffect,
  useRef,
  useState,
} from "react";
import { InputFileWrapper } from "./style";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import Icon from "../Icon";
import { Box, LoadingOverlay, Text } from "@mantine/core";
import { validateFileSize } from "@/utils/common-functions";
import { postFile, resetFile } from "@/store/slices/fileSlice";

export type FileType =
  | "all"
  | "images"
  | "word"
  | "pdf"
  | "document"
  | "images_and_document";

const ACCEPT_TYPES = {
  all: "*/*",
  images: "image/*",
  word: "application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  pdf: "application/pdf",
  document:
    "application/pdf, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  images_and_document:
    "image/*, application/pdf, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document",
};

const HELPER_TEXT = {
  all: "Upload any file",
  images: "Upload image files (JPG, PNG, etc.)",
  word: "Upload Word documents (.DOC or .DOCX)",
  pdf: "Upload PDF files",
  document: "Upload document files (.PDF, .DOC, or .DOCX)",
  images_and_document:
    "Upload image files (JPG, PNG, etc.) or documents (.PDF, .DOC, or .DOCX)",
};

interface InputFileProps {
  /**
   * This callback is called after a file is successfully uploaded.
   * @param uploadedUrl the url of the uploaded file
   * @param prefix the prefix that was passed when calling this component, this is used to identify
   * what field the uploaded file is for, in cases where multiple files can be uploaded.
   */
  onUploaded?: (uploadedUrl: string, prefix?: string) => void;
  prefix: string;
  fileType?: FileType;
  helperText?: string;
}

const UploadDocumentInput = ({
  prefix,
  fileType = "document",
  onUploaded,
  helperText,
}: InputFileProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dispatch: AppDispatch = useDispatch();
  const { uploadedFileUrl, uploadedFileKey, loading } = useSelector(
    (state: RootState) => state.file
  );

  const [isDragging, setIsDragging] = useState(false);

  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      if (!validateFileSize(file)) return;
      const formData = new FormData();
      // Replace spaces with underscores in filename
      const fileName = file.name.replace(/\s+/g, "_");
      formData.append("file", file, fileName);
      dispatch(postFile(formData, prefix || "default"));
    }
  };

  // for file drag & drop:
  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };
  const handleDragEnter = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };
  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };
  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      if (!validateFileSize(file)) return;
      const formData = new FormData();
      formData.append("file", file, file.name);
      dispatch(postFile(formData, prefix || "default"));
    }
  };

  const openFileDialog = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  useEffect(() => {
    if (uploadedFileKey && uploadedFileKey !== prefix) {
      return;
    }
    if (uploadedFileUrl && uploadedFileUrl.length > 0) {
      if (onUploaded) {
        onUploaded(uploadedFileUrl, prefix);
        dispatch(resetFile());
      }
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  }, [uploadedFileUrl]);

  return (
    <Box pos="relative" ta={"center"}>
      <LoadingOverlay
        visible={loading}
        zIndex={1000}
        overlayProps={{ blur: 1 }}
      />
      <InputFileWrapper
        onDragOver={handleDragOver}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={isDragging ? "dragging" : ""}
        onClick={openFileDialog}
      >
        <input
          type="file"
          name={prefix}
          ref={fileInputRef}
          onChange={handleFileInputChange}
          accept={ACCEPT_TYPES[fileType]}
          style={{ display: "none" }}
        />
        <Icon size={80} icon="upload-document" />
        <Text fw={500}>
          Drag & drop files or <span className="underline">Browse</span>
        </Text>
        <Text c={"text-light"}>{helperText || HELPER_TEXT[fileType]}</Text>
      </InputFileWrapper>
    </Box>
  );
};

export default UploadDocumentInput;
