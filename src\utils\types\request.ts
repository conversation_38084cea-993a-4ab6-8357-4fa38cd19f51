import { HistoriesData, UpdateLogData } from "./distributor-agent";

export interface AdminRequestResponse {
  requests: RequestCardData[];
  page: number;
  totalItems: number;
}
export enum RequestType {
  DataChange = "change_field",
  ForceDataChange = "force_change_field",
  Reactivation = "reactivate_account",
  Termination = "terminate_agent",
  Application = "new_agent",
  AdminCheckLinks = "check_marketplace_link",
  DistributorCheckMarketplaceLinks = "check_invalid_link",
  ChangeDistributor = "change_distributor",
}

export enum ComposeRequestType {
  DataChange = "change_field",
  AgentTermination = "terminate_agent",
}

export interface RequestCardData {
  id: number;
  requesterName?: string;
  requesterRole?: "Agent" | "Distributor" | "Admin";
  targetRequestName?: string;
  requestType: RequestType;
  reason?: string;
  createdAt: string;
  updatedAt?: string;
  metadatas: RequestMetadata[];
  readonly?: boolean;
}

export interface RequestMetadata {
  field: string;
  oldValue: string;
  newValue: string;
  status: "pending" | "accept" | "reject";
  requestStep: 1 | 2 | 3;
}

export interface AdminRequestDetailChangeField {
  field: string;
  oldValue: string;
  newValue: string;
  reason: string;
  sourceAgent: string;
  sourceRole: string;
  updateLogs: UpdateLogData[];
  complaint?: string;
}

export interface AdminRequestDetailTerminateAgent {
  sourceAgent: string;
  sourceRole: string;
  reason: string;
  terminatedAgent: string;
  orderHistory: HistoriesData[];
  evidence?: string;
}

export interface AdminRequestDetailReactivateAccount {
  sourceAgent: string;
  sourceRole: string;
  orderHistory: HistoriesData[];
}

export interface RequestDetailInvalidMarketplace {
  reason: string;
  invalidLink: {
    field: string;
    old: string;
  };
}

export interface AdminRequestUpdateStatusData {
  status: "accept" | "reject";
  reason?: string;
  field?: string;
}

export interface AdminRequestLinksByIslandResponse {
  status: number;
  message: string;
  data: AdminRequestLinksByIslandData[];
}

export interface AdminRequestLinksByIslandData {
  uoc: string;
  userName: string;
  userRole: string;
  links: AdminRequestLinksByIslandLinks[];
}

export interface AdminRequestLinksByIslandLinks {
  id: number;
  type: string;
  url: string;
  isValid: boolean;
  reason: string;
}

export interface AdminSubmitInvalidLinksReasonsResponse {
  status: number;
  message: string;
  data: [];
}

export interface AdminSubmitInvalidLinksReasonsPayload {
  invalidLinks: AdminSubmitInvalidLinksReasonsData[];
}

export interface AdminSubmitInvalidLinksReasonsData {
  id?: number | string; // for filtering data to the store
  userID: string; // uoc
  type: string; // shopee, tokopedia, etc.
  url: string;
  reason: string;
}

export interface UpdateLogsRequestResponse {
  status_code: number;
  message: string;
  data: UpdateLogsRequestData;
}

export interface UpdateLogsRequestData {
  page: number;
  totalItems: number;
  updateLogs: UpdateLogData[];
}

export interface ComposeAgentTerminationData {
  agentId: string;
  reason: string;
  isAgree: boolean;
}

export interface ComposeAgentSelfTerminationData {
  reason: string;
  evidences: string[];
}

export interface AgentTerminationDraftData {
  terminateAgent: {
    agentID: string;
  };
  reason: string;
  requestType: "terminate_agent";
  evidences?: string[];
}

export interface NewApplicationData {
  type: "Agent" | "Distributor";
  requestStep: 1 | 2 | 3 | 4;
  sourceAgent?: string;
  newAgent: {
    nik: string;
    npwp?: string;
    name: string;
    address: string;
    email: string;
    phone: string;
    adminPhone: string;
    warehouseAddress?: string;
    sk?: string;
    ktp?: string;
    selfieKtp?: string;
    shops?: {
      tokopedia?: string;
      shopee?: string;
      tiktok?: string;
    };
    firstOrder?: FirstOrder[];
  };
  transferEvidence?: string;
}

export interface NewApplicationResponse {
  status_code: number;
  message: string;
  data: NewApplicationData;
}

export interface AvailableDistributor {
  uoc: string;
  name: string;
  productSold: string;
  agentCount: string;
  rating: number;
  disabled: boolean;
}

export interface FirstOrder {
  orderId: number;
  products: {
    id: number;
    name: string;
    price: number;
  }[];
  courier: string;
  shippingCost: number;
  notes: string;
  paymentProof: string;
  rejectReason: string;
  subTotalPayment: number;
  totalPayment: number;
}
