"use client";

import CheckMark from "@/components/icons/CheckMark";
import { CrossMark } from "@/components/icons/CrossMark";
import useBreakpoints from "@/hooks/useBreakpoints";
import {
  Paper,
  Flex,
  Text,
  PaperProps,
  PolymorphicComponentProps,
  Center,
} from "@mantine/core";

type StatusType =
  | "accept"
  | "reject"
  | "accepted"
  | "rejected"
  | "forced"
  | "updated";

interface ReviewStatusProps {
  status: StatusType;
}

interface ReviewStatusConfig {
  label: string;
  color: "error" | "success";
}

const STATUS_CONFIG: Record<StatusType, ReviewStatusConfig> = {
  accept: { label: "Accepted", color: "success" },
  accepted: { label: "Accepted", color: "success" },
  reject: { label: "Rejected", color: "error" },
  rejected: { label: "Rejected", color: "error" },
  forced: { label: "Force Edited", color: "success" },
  updated: { label: "Updated", color: "success" },
} as const;

const ReviewStatus = ({ status }: ReviewStatusProps) => {
  const { mobile } = useBreakpoints();

  const config = STATUS_CONFIG[status];
  if (!config) return null;

  const { label, color } = config;
  const bg = `var(--mantine-color-${color}-1)`;
  const borderColor = `var(--mantine-color-${color}-filled)`;

  const sharedProps: PolymorphicComponentProps<"div", PaperProps> = {
    withBorder: true,
    bg,
    c: color,
    "aria-label": label,
    role: "review status",
    variant: "outline",
  };

  const StatusIcon = color === "success" ? CheckMark : CrossMark;

  if (mobile) {
    return (
      <Paper
        {...sharedProps}
        p={0}
        radius={9999}
        h={"xl"}
        w={"xl"}
        bd={`2px solid ${borderColor}`}
      >
        <Center w="100%" h="100%">
          <StatusIcon fontSize={20} />
        </Center>
      </Paper>
    );
  }

  return (
    <Paper
      {...sharedProps}
      radius="8px"
      px={12}
      py={6}
      w="fit-content"
      h="fit-content"
      style={{ borderColor }}
    >
      <Flex justify="center" align="center">
        <Text size="sm" fw={500}>
          {label}
        </Text>
      </Flex>
    </Paper>
  );
};

export default ReviewStatus;
