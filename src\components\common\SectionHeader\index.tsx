import { Anchor, Box, Skeleton, BoxProps, Text, Group } from "@mantine/core";
import Link from "next/link";

interface SectionHeaderProps extends BoxProps {
  title: string;
  description?: string | React.ReactNode;
  link?: string;
  linkLabel?: string;
  loading?: boolean;
}

const SectionHeader = ({
  title,
  description,
  link,
  linkLabel = "Lihat",
  loading = false,
  ...props
}: SectionHeaderProps) => {
  const renderDescription = () => {
    if (typeof description === "string") {
      return loading ? (
        <Skeleton w="200px" h="12px" />
      ) : (
        <Text c="text-light" fw={500} fz="sm">
          {description}
        </Text>
      );
    }
    return description;
  };

  return (
    <Box {...props}>
      <Group justify="space-between" gap="sm">
        {loading ? (
          <Skeleton w="80px" h="20px" mb={"xs"} />
        ) : (
          <Text fw={500} fz="lg">
            {title}
          </Text>
        )}
        {link && !loading && (
          <Anchor component={Link} fz="sm" href={link} fw={500}>
            {linkLabel}
          </Anchor>
        )}
      </Group>
      {renderDescription()}
    </Box>
  );
};

export default SectionHeader;
