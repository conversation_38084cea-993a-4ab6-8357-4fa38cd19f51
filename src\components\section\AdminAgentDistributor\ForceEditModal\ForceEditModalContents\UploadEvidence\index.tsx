import FileUploadInput from "@/components/common/FileUploadInput";
import UploadedFileRow from "@/components/common/UploadedFileRow";
import { Input, SimpleGrid, Text } from "@mantine/core";

interface UploadEvidenceProps {
  evidences: string[];
  setEvidences: (evidences: string[]) => void;
  agentId?: number;
}

const UploadEvidence = ({
  evidences,
  setEvidences,
  agentId,
}: UploadEvidenceProps) => {
  return (
    <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="xl">
      <FileUploadInput
        required
        label="Upload bukti request"
        prefix={`bukti-request${agentId ? `/${agentId}` : ""}`}
        onChange={(url) => {
          setEvidences([...evidences, url]);
        }}
      />
      <Input.Wrapper label="Uploaded bukti request">
        {evidences.length === 0 ? (
          <Text c={"text-light"} fs={"italic"}>
            * Belum ada file yang di-upload *
          </Text>
        ) : (
          evidences.map((file, index) => (
            <UploadedFileRow
              key={index}
              fileUrl={file}
              onDelete={() => {
                setEvidences(evidences.filter((_, i) => i !== index));
              }}
            />
          ))
        )}
      </Input.Wrapper>
    </SimpleGrid>
  );
};

export default UploadEvidence;
