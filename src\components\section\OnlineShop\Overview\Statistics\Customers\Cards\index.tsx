import { Radio, SimpleGrid, Skeleton } from "@mantine/core";
import InfoCard, { InfoCardProps } from "../../../InfoCard";
import Loop from "@/components/common/Loop";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import { fetchCustomerSummary } from "@/store/slices/onlineShopSlices/overviewSlice";
import {
  CustomerChartCategories,
  CustomerSummaryData,
} from "@/utils/types/online-shop";
import classes from "../../Sales/Cards/style.module.css";
import { useEffect } from "react";
import useOverviewFilters from "@/hooks/useOverviewFilters";

const STATS = (
  data: CustomerSummaryData
): (InfoCardProps & { key: CustomerChartCategories })[] => [
  {
    key: "topArea",
    label: "Area Terlaris",
    value: data.topArea,
    description: "Area dengan total rupiah penjualan terbanyak.",
  },
  {
    key: "repeatCustomersPercentage",
    label: "Persentase Pembeli Berulang",
    value: data.repeatCustomersPercentage,
    formatValueAs: "percentage",
    description: "Persentase seorang pembeli melakukan purchase berulang.",
  },
  {
    key: "bestSellingHours",
    label: "Jam Penjualan Terlaris",
    value: data.bestSellingHours,
    description: "Periode jam di mana pembelian paling banyak terjadi.",
  },
  {
    key: "averageRating",
    label: "Rata-Rata Satisfaction Score",
    value: data.averageRating,
    formatValueAs: "rating",
    description:
      "Rata-rata rating yang diberikan pembeli dalam menilai proses beli di Hotto Online Shop.",
  },
];

const CustomersStatisticsCards = ({ slug }: { slug?: string }) => {
  const dispatch: AppDispatch = useDispatch();
  const { customerSummary: data, loading } = useSelector(
    (state: RootState) => state.overview
  );
  const { time, customerChart, setCustomerChart } = useOverviewFilters();

  const handleChange = (value: string | null) => {
    setCustomerChart(value as CustomerChartCategories);
  };

  useEffect(() => {
    if (time && !loading.customerSummary) {
      dispatch(fetchCustomerSummary(time, slug));
    }
  }, [time, slug]);

  return (
    <Radio.Group value={customerChart} onChange={handleChange}>
      <SimpleGrid cols={{ base: 2, md: 4 }}>
        {loading.customerSummary ? (
          <Loop count={4}>
            <Skeleton h={145} />
          </Loop>
        ) : (
          STATS(data).map((stat) => (
            <Radio.Card
              disabled={loading.customerChart || loading.customerSummary}
              key={stat.key}
              value={stat.key}
              classNames={classes}
            >
              <InfoCard
                w="100%"
                h="100%"
                label={stat.label}
                value={stat.value}
                description={stat.description}
                formatValueAs={stat.formatValueAs}
                differences={stat.differences}
              />
            </Radio.Card>
          ))
        )}
      </SimpleGrid>
    </Radio.Group>
  );
};

export default CustomersStatisticsCards;
