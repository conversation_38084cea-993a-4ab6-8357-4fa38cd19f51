"use client";

import BackButton from "@/components/common/BackButton";
import NotFoundPlaceholder from "@/components/common/NotFoundPlaceholder";
import dynamic from "next/dynamic";
const SearchBar = dynamic(() => import("@/components/common/SearchBar"), {
  ssr: false,
});
import { Flex } from "@mantine/core";
import SearchHistory from "@/components/common/SearchHistory";
import { useSearchParams } from "next/navigation";
import AgentCard from "../CityAgentsBoard/CityAgentsColumn/AgentCard";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { useEffect } from "react";
import { fetchAgentsBySearchQuery } from "@/store/slices/adminAgentListSlice";
import { ADMIN_AGENTS_SEARCH_HISTORY_VAR } from "@/utils/constants";
import { useLocalStorage } from "@mantine/hooks";

const AdminAgentsSearch = () => {
  const dispatch: AppDispatch = useDispatch();
  const searchParams = useSearchParams();
  const search = searchParams?.get("search") || "";
  const [histories, setHistories] = useLocalStorage<string[]>({
    key: ADMIN_AGENTS_SEARCH_HISTORY_VAR,
    defaultValue: [],
    getInitialValueInEffect: true,
  });

  const { searchResult, loading } = useSelector(
    (state: RootState) => state.adminAgentList
  );

  useEffect(() => {
    if (!search) return;
    dispatch(fetchAgentsBySearchQuery(search, false));
    setHistories((prev) => [...new Set([search, ...prev])]);
  }, [search]);

  const handleRemoveHistory = (value: string) => {
    if (!histories) return;
    setHistories(histories.filter((v) => v !== value));
  };

  return (
    <Flex direction={"column"} gap={"md"}>
      <Flex direction={"row"} gap={"xs"} align={"center"} py={"md"}>
        <BackButton href="/admin/agent" />
        <SearchBar />
      </Flex>
      {!search && (
        <SearchHistory histories={histories} onRemove={handleRemoveHistory} />
      )}
      {search && searchResult && searchResult.length > 0 && (
        <Flex direction={"row"} gap={"sm"} style={{ flexWrap: "wrap" }}>
          {searchResult.map((value, index) => (
            <AgentCard key={index} data={value} variant="simple" />
          ))}
        </Flex>
      )}
      {search && (!searchResult || searchResult.length === 0) && !loading && (
        <NotFoundPlaceholder
          message={`Kami tidak menemukan agent / distributor dengan nama atau nomor telepon '${search}'`}
        />
      )}
    </Flex>
  );
};

export default AdminAgentsSearch;
