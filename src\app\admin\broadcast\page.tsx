"use client";

import DraftList from "@/components/common/DraftList";
import PageLayout from "@/components/common/PageLayout";
import PastBroadcastList from "@/components/section/AdminBroadcast/PastBroadcastList";
import { AppDispatch, RootState } from "@/store";
import { fetchDraftBroadcasts } from "@/store/slices/adminBroadcastSlice";
import { DraftType } from "@/utils/types";
import { withAuth } from "@/utils/withAuth";
import { Flex } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

const AdminBroadcastPage = () => {
  const dispatch: AppDispatch = useDispatch();

  const { draftBroadcasts } = useSelector(
    (state: RootState) => state.adminBroadcast
  );
  useEffect(() => {
    dispatch(fetchDraftBroadcasts());
  }, [dispatch]);

  const isMobile = useMediaQuery("(max-width: 768px)");
  const isTablet = useMediaQuery("(max-width: 1024px)");
  return (
    <PageLayout>
      <Flex
        gap={"xl"}
        direction={isMobile ? "column-reverse" : "row"}
        justify={"space-between"}
        w={"100%"}
        wrap={isTablet ? "wrap-reverse" : "nowrap"}
      >
        <PastBroadcastList />
        <DraftList drafts={draftBroadcasts} draftType={DraftType.Broadcast} />
      </Flex>
    </PageLayout>
  );
};

export default withAuth(AdminBroadcastPage);
