import ReviewStatus from "@/components/common/ReviewStatus";
import useBreakpoints from "@/hooks/useBreakpoints";
import { capitalize } from "@/utils/common-functions";
import { Flex, Text, Button } from "@mantine/core";

interface InvalidMarketplaceLinkRowProps {
  shopType: string;
  shopUrl: string;
  reason: string;
  status: "accept" | "reject" | "pending" | "updated";
  openModal: () => void;
}

const InvalidMarketplaceLinkRow = ({
  shopType,
  shopUrl,
  status,
  reason,
  openModal,
}: InvalidMarketplaceLinkRowProps) => {
  const { mobile } = useBreakpoints();
  return (
    <Flex
      justify={"space-between"}
      gap={"md"}
      direction={mobile ? "column" : "row"}
    >
      <Flex direction={"column"}>
        <Text fw={500}>{`Link ${capitalize(shopType)}: ${shopUrl}`}</Text>
        <Text fw={400} c={"text-light"}>{`Alasan: ${reason}`}</Text>
      </Flex>
      {status === "pending" ? (
        <Button onClick={openModal}>Update</Button>
      ) : (
        <ReviewStatus status={status} />
      )}
    </Flex>
  );
};

export default InvalidMarketplaceLinkRow;
