import UploadDocumentInput, {
  FileType,
} from "@/components/common/UploadDocumentInput";
import UploadedFileRow from "@/components/common/UploadedFileRow";
import { Flex, Text } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";

interface MultipleFileProps {
  uploadedFiles: string[];
  setUploadedFiles: (uploadedFiles: string[]) => void;
  prefixUrl: string;
  label?: string;
  fileType?: FileType;
}

const MultipleFileInput = ({
  uploadedFiles,
  setUploadedFiles,
  prefixUrl,
  label = "file",
  fileType,
}: MultipleFileProps) => {
  const handleUploadFile = (
    uploadedFileUrl: string,
    prefix: string | undefined
  ) => {
    if (prefix === prefixUrl) {
      setUploadedFiles([...uploadedFiles, uploadedFileUrl]);
    }
  };

  const isMobile = useMediaQuery("(max-width: 768px)");
  return (
    <Flex direction={isMobile ? "column" : "row"} w={"100%"} gap={"md"}>
      <Flex direction={"column"} gap={"xs"} w={isMobile ? "100%" : "50%"}>
        <p>Upload {label}</p>
        <UploadDocumentInput
          onUploaded={handleUploadFile}
          prefix={prefixUrl}
          fileType={fileType}
        />
      </Flex>
      <Flex direction={"column"} gap={"xs"} w={isMobile ? "100%" : "50%"}>
        <p>Uploaded {label}</p>
        {uploadedFiles.length === 0 ? (
          <Text c={"text-light"} fs={"italic"}>
            * Belum ada file yang di-upload *
          </Text>
        ) : (
          uploadedFiles.map((file, index) => (
            <UploadedFileRow
              key={index}
              fileUrl={file}
              onDelete={() => {
                setUploadedFiles(uploadedFiles.filter((_, i) => i !== index));
              }}
            />
          ))
        )}
      </Flex>
    </Flex>
  );
};

export default MultipleFileInput;
