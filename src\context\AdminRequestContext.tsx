// for triggering handle submit from child component
import { createContext, PropsWithChildren, useContext } from "react";

export interface AdminRequestContextType {
  submitRequest?: (status: "accept" | "reject") => Promise<void>;
  closeModal?: () => void;
  active?: number;
}

const AdminRequestContext = createContext<AdminRequestContextType | undefined>(
  undefined
);

export const useAdminRequestContext = (): AdminRequestContextType => {
  const context = useContext(AdminRequestContext);
  if (!context) {
    throw new Error(
      "useAdminRequestContext must be used within a AdminRequestProvider"
    );
  }
  return context;
};

export const AdminRequestProvider: React.FC<
  PropsWithChildren & AdminRequestContextType
> = ({ children, submitRequest, closeModal, active }) => {
  return (
    <AdminRequestContext.Provider value={{ submitRequest, closeModal, active }}>
      {children}
    </AdminRequestContext.Provider>
  );
};
