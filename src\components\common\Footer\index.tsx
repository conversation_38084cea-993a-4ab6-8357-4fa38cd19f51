import { Box, Text, Flex, BoxProps, Anchor, Group } from "@mantine/core";
import React from "react";
import { Whatsapp } from "@/utils/icons";

export type FooterProps = Omit<BoxProps, "c" | "ta">;

const Footer: React.FC<FooterProps> = ({ ...props }) => {
  const wa = process.env.NEXT_PUBLIC_HOTTO_ADMIN_WA!;

  return (
    <Box {...props} c="text-light" ta="center">
      <Text>Ada masalah?</Text>
      <Flex justify="center" wrap={"wrap"} columnGap={14}>
        <Text>Hubungi support kami</Text>
        <Group gap={3}>
          <Whatsapp size={20} />
          <Anchor
            underline="never"
            c="text-light"
            href={`https://wa.me/+62${wa}`}
            target="_blank"
          >
            (+62) {wa}
          </Anchor>
        </Group>
      </Flex>
    </Box>
  );
};

export default Footer;
