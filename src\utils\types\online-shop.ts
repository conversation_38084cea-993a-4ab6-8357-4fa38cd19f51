import { ApiResponse } from ".";
import { SortFilterValue } from "../constants";

export type StatisticsTimeFilters = "daily" | "weekly" | "monthly";

export type SalesChartCategories =
  | "sales"
  | "orders"
  | "bestSellingProducts"
  | "visitors"
  | "productSeen"
  | "conversionRate";

export type CustomerChartCategories =
  | "topArea"
  | "repeatCustomersPercentage"
  | "bestSellingHours"
  | "averageRating";

export interface ProductFilters {
  search?: string;
  status: "all" | "active" | "deactive";
  order: SortFilterValue;
}

export type ProductsResponse = ApiResponse<ProductsData>;

export interface ProductsData {
  totalItems: number;
  products: ProductItem[];
}

export interface ProductItem {
  id: number;
  name: string;
  status: boolean;
  images: string[];
  variant: string;
  totalStocks: number;
  unitPrice: number;
}

export interface UpdateProductStatusPayload {
  status: boolean;
}

export interface ProductDetail {
  name: string;
  description: string;
  status: boolean;
  images: string[]; // minimal 3
  minimumOrder: number;
  totalStocks: number;
  unitPrice: number;
  // firstVariantKey?: string | null;
  // secondVariantKey?: string | null;
  // firstVariantValue?: string[] | null;
  // secondVariantValue?: string[] | null;
  // variants?: ProductVariant[] | null;
}

export interface ProductVariant {
  id?: number;
  firstVariant: string;
  secondVariant: string;
  stock: number;
  price: number;
}

export enum OrderStatusType {
  All = "all",
  WaitingPayment = "waiting_payment",
  WaitingConfirmation = "waiting_confirmation",
  OrderConfirmation = "order_confirmation",
  WaitingForDelivery = "waiting_for_delivery",
  OnDelivery = "on_delivery",
  Complaint = "complaint",
  Finished = "finished",
}

export enum OrderStatusLabel {
  All = "Semua Pesanan",
  WaitingPayment = "Menunggu Pembayaran",
  WaitingConfirmation = "Konfirmasi Pembayaran",
  OrderConfirmation = "Konfirmasi Pesanan",
  WaitingForDelivery = "Perlu Dikirim",
  OnDelivery = "Dalam Pengiriman",
  Complaint = "Dikomplain",
  Finished = "Pesanan Selesai",
}

export const OrderStatuses = [
  "all",
  "waiting_payment",
  "waiting_confirmation",
  "order_confirmation",
  "waiting_for_delivery",
  "on_delivery",
  "complaint",
  "finished",
] as const;

export type OrderStatus = (typeof OrderStatuses)[number];

export const getOrderStatuslabel = (status: OrderStatusType): string => {
  switch (status) {
    case OrderStatusType.WaitingPayment:
      return OrderStatusLabel.WaitingPayment;
    case OrderStatusType.WaitingConfirmation:
      return OrderStatusLabel.WaitingConfirmation;
    case OrderStatusType.OrderConfirmation:
      return OrderStatusLabel.OrderConfirmation;
    case OrderStatusType.WaitingForDelivery:
      return OrderStatusLabel.WaitingForDelivery;
    case OrderStatusType.OnDelivery:
      return OrderStatusLabel.OnDelivery;
    case OrderStatusType.Finished:
      return OrderStatusLabel.Finished;
    case OrderStatusType.Complaint:
      return OrderStatusLabel.Complaint;
    case OrderStatusType.WaitingPayment:
      return OrderStatusLabel.WaitingPayment;
    default:
      return "";
  }
};

export interface OrdersFilters {
  search?: string;
  status: OrderStatus;
  order: SortFilterValue;
  courier: string;
}

export type OrdersResponse = ApiResponse<OrdersData>;

export interface OrdersData {
  page: number;
  totalItems: number;
  orders: OrderDetail[];
}

export interface OrderDetail {
  id: number;
  orderID: string;
  distributorUoc: string;
  distributorSlug: string;
  distributorAccountNo: string;
  distributorAccountName: string;
  custName: string;
  custPhone: string;
  custEmail: string;
  custAccName: string;
  status: OrderStatus;
  courier: string;
  courierFullName: string;
  destinationAddress: string;
  paymentMethod: string;
  paymentMethodText: string;
  bankName: string;
  evidence: string;
  notes: string;
  rejectReason: string;
  resiNumber: string;
  resiTrackUrl: string;
  uniqueId: number;
  shippingCost: number;
  subTotalPrice: number;
  totalPrice: number;
  dueDate: string;
  paymentDate: string;
  orderItems: OrderItem[];
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  productId: number;
  qty: number;
  productName: string;
  productSlug: string;
  productStatus: boolean;
  productDescription: string;
  productImages: string[];
  minimumOrder: number;
  totalStocks: number;
  unitPrice: number;
  createdAt: string;
  updatedAt: string;
}

export interface ApproveOrderPayload {
  isApproved: boolean;
  rejectReason?: string;
  resiNumber?: string;
}

export interface BestSellerProduct {
  id: number;
  images: string[];
  name: string;
  variant?: string;
  seenCount: number;
  productSold: number;
  productRevenue: number;
}

export interface CustomerData {
  id: string;
  name: string;
  lastBoughtProduct: string;
  lastOrderDate: string;
  address: string;
  totalOrders: number;
  totalSpend: number;
}

export interface TodayStats {
  newOrdersCount: number;
  complaintsCount: number;
}

export interface ShopStats {
  totalProductSold: number;
  totalRevenue: number;
  totalProductSeen: number;
  progressPercentage: number;
}

export interface CustomerStatsList {
  page: number;
  totalItems: number;
  customerStats: CustomerData[];
}

export interface CustomerStatsOverview {
  lastUpdateDate: string;
  totalTransaction: number;
  topSpenders: TopSpender[];
}

export interface TopSpender {
  name: string;
  totalSpend: number;
}

export type TopSellingStoresResponse = ApiResponse<TopSellingStoresData>;

export interface TopSellingStoresData {
  page: number;
  totalItems: number;
  storeStats: TopSellingStoresItem[];
}

export interface TopSellingStoresItem {
  userId: string;
  slug: string;
  name: string;
  serviceSpeed: number;
  productSold: number;
  totalCustomers: number;
  revenue: number;
  bestSellingProduct: string;
  rating: number;
}

export type TopPurchasingCustomersResponse =
  ApiResponse<TopPurchasingCustomersData>;

export interface TopPurchasingCustomersData {
  page: number;
  totalItems: number;
  customerStats: TopPurchasingCustomersItem[];
}

export interface TopPurchasingCustomersItem {
  custPhoneNumber: string;
  region: string;
  averageRating: number;
  purchaseFrequency: number;
  totalBoughtProducts: number;
  totalSpend: number;
}

export type SalesSummaryResponse = ApiResponse<SalesSummaryData>;

export interface SalesSummaryData {
  totalSales: SalesSummaryItem;
  totalVisits: SalesSummaryItem;
  productSeen: SalesSummaryItem;
  totalOrders: SalesSummaryItem;
  conversionRate: SalesSummaryItem;
  bestSellingProductVariant: string;
}

export interface SalesSummaryItem {
  value: number;
  progressPercentage: number;
}

export type CustomerSummaryResponse = ApiResponse<CustomerSummaryData>;

export interface CustomerSummaryData {
  topArea: string;
  repeatCustomersPercentage: number;
  bestSellingHours: string;
  averageRating: number;
}

export type StatisticsChartResponse = ApiResponse<StatisticsChartData>;

export interface StatisticsChartData {
  // for sales, only filled if category = bestSellingProducts
  // for customers, only filled if category in [topArea, bestSellingHours]
  totalSales: number;
  content: SalesStatisticsChartItem[];
}

export interface SalesStatisticsChartItem {
  // for sales, "2025-03-01T00:00:00Z" or "Product name"
  // for customers, "2025-03-01T00:00:00Z" or "19.00 - 21.00"
  label: string;
  value: number;
}

export interface OverviewOrderHistoryData {
  page: number;
  totalItems: number;
  orderHistories: OverviewOrderHistoryItem[];
}

export interface OverviewOrderHistoryItem {
  orderId: string;
  status: string;
  phone: string;
  email: string;
  totalPrice: number;
}
