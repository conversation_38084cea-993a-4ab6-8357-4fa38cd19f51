import Icon from "@/components/common/Icon";
import { COLORS } from "@/styles/color";
import { AvailableDistributor } from "@/utils/types/request";
import { Flex, Paper, Text, Divider, Radio, Avatar } from "@mantine/core";

interface AvailableDistributorCardProps {
  distributor: AvailableDistributor;
  selectedDistributor: string;
  onSelect: (uoc: string) => void;
}

const AvailableDistributorCard = ({
  distributor,
  selectedDistributor,
  onSelect,
}: AvailableDistributorCardProps) => {
  return (
    <Paper
      bg={
        selectedDistributor === distributor.uoc
          ? "primary.1"
          : distributor.disabled
          ? "bg"
          : "bg-gray"
      }
      opacity={distributor.disabled ? 0.75 : 1}
      p={"sm"}
      withBorder
      style={{
        outline:
          selectedDistributor === distributor.uoc
            ? `1px solid ${COLORS.accent}`
            : "none",
      }}
    >
      <Flex
        justify={"space-between"}
        align={"center"}
        gap={"sm"}
        w={"100%"}
        onClick={() => {
          if (!distributor.disabled) {
            onSelect(distributor.uoc);
          }
        }}
        style={{
          cursor: distributor.disabled ? "not-allowed" : "pointer",
        }}
      >
        <Flex gap={"md"} align={"center"} w={"80%"}>
          <Avatar size={"lg"} name={distributor.name} color="primary" />
          <Flex gap={"4px"} direction={"column"} maw={"100%"}>
            <Flex className="rating">
              <Paper bg={"success"} p={"6px"} radius={"sm"}>
                <Flex gap={"4px"} w={"fit-content"} align={"center"}>
                  <Icon icon="star" size={12} />
                  <Text
                    fw={600}
                    size={"sm"}
                    lh={0.5}
                    mt={"2px"}
                  >{`${distributor.rating}/5`}</Text>
                </Flex>
              </Paper>
            </Flex>
            <Text fw={600} lineClamp={1} maw={"100%"}>
              {distributor.name}
            </Text>
            <Flex gap={"lg"}>
              <Flex gap={"2px"} direction={"column"}>
                <Text fw={500}>{distributor.productSold}</Text>
                <Text fw={500} c={"text-light"}>
                  Produk Terjual
                </Text>
              </Flex>
              <Divider orientation="vertical" />
              <Flex gap={"2px"} direction={"column"}>
                <Text fw={500}>{distributor.agentCount}</Text>
                <Text fw={500} c={"text-light"}>
                  Agents
                </Text>
              </Flex>
            </Flex>
          </Flex>
        </Flex>
        <Radio
          value={distributor.uoc}
          checked={selectedDistributor === distributor.uoc}
          onChange={() => onSelect(distributor.uoc)}
          disabled={distributor.disabled}
        />
      </Flex>
    </Paper>
  );
};

export default AvailableDistributorCard;
